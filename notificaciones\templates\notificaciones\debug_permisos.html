{% extends 'Base/base.html' %}

{% block title %}Debug Permisos{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>Debug de Permisos de Notificaciones</h2>
            
            <div class="card">
                <div class="card-header">
                    <h5>Información del Usuario</h5>
                </div>
                <div class="card-body">
                    <p><strong>Usuario:</strong> {{ user.username }}</p>
                    <p><strong>Nombre completo:</strong> {{ user.get_full_name|default:"No definido" }}</p>
                    <p><strong>Email:</strong> {{ user.email }}</p>
                    <p><strong>Es staff:</strong> {{ user.is_staff }}</p>
                    <p><strong>Es superuser:</strong> {{ user.is_superuser }}</p>
                    <p><strong>Cargo:</strong> {{ user.cargo.nombre|default:"No definido" }}</p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>Grupos del Usuario</h5>
                </div>
                <div class="card-body">
                    {% if user.groups.all %}
                        <ul>
                            {% for group in user.groups.all %}
                                <li>{{ group.name }}</li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted">El usuario no pertenece a ningún grupo</p>
                    {% endif %}
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>Permisos de Notificaciones</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Permisos Básicos:</h6>
                            <ul class="list-unstyled">
                                <li>
                                    <i class="fas fa-{{ user_permissions.is_admin|yesno:'check text-success,times text-danger' }}"></i>
                                    Es Admin: {{ user_permissions.is_admin }}
                                </li>
                                <li>
                                    <i class="fas fa-{{ user_permissions.is_secretaria|yesno:'check text-success,times text-danger' }}"></i>
                                    Es Secretaria: {{ user_permissions.is_secretaria }}
                                </li>
                                <li>
                                    <i class="fas fa-{{ user_permissions.is_supervisor|yesno:'check text-success,times text-danger' }}"></i>
                                    Es Supervisor: {{ user_permissions.is_supervisor }}
                                </li>
                                <li>
                                    <i class="fas fa-{{ user_permissions.is_empleado|yesno:'check text-success,times text-danger' }}"></i>
                                    Es Empleado: {{ user_permissions.is_empleado }}
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Permisos de Notificaciones:</h6>
                            <ul class="list-unstyled">
                                <li>
                                    <i class="fas fa-{{ user_permissions.can_create_notifications|yesno:'check text-success,times text-danger' }}"></i>
                                    Puede crear notificaciones: {{ user_permissions.can_create_notifications }}
                                </li>
                                <li>
                                    <i class="fas fa-{{ user_permissions.can_send_mass_notifications|yesno:'check text-success,times text-danger' }}"></i>
                                    Puede enviar masivas: {{ user_permissions.can_send_mass_notifications }}
                                </li>
                                <li>
                                    <i class="fas fa-{{ user_permissions.can_manage_all_notifications|yesno:'check text-success,times text-danger' }}"></i>
                                    Puede gestionar todas: {{ user_permissions.can_manage_all_notifications }}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5>Enlaces de Prueba</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>Para Todos:</h6>
                            <a href="{% url 'notificaciones:mis_notificaciones' %}" class="btn btn-primary btn-sm">
                                Mis Notificaciones
                            </a>
                        </div>
                        
                        {% if user_permissions.can_create_notifications %}
                        <div class="col-md-4">
                            <h6>Para Creadores:</h6>
                            <a href="{% url 'notificaciones:lista_notificaciones' %}" class="btn btn-success btn-sm mb-2">
                                Gestionar Notificaciones
                            </a><br>
                            <a href="{% url 'notificaciones:crear_notificacion_usuario' %}" class="btn btn-info btn-sm mb-2">
                                Crear para Usuarios
                            </a><br>
                            <a href="{% url 'notificaciones:crear_notificacion_grupo' %}" class="btn btn-warning btn-sm">
                                Crear para Grupos
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user_permissions.can_send_mass_notifications %}
                        <div class="col-md-4">
                            <h6>Solo Admins:</h6>
                            <a href="{% url 'notificaciones:crear_notificacion_masiva' %}" class="btn btn-danger btn-sm">
                                Notificación Masiva
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-info-circle"></i> Instrucciones:</h6>
                <p>Esta página es solo para debug. Si no ve los enlaces de creación:</p>
                <ol>
                    <li>Verifique que su usuario esté en el grupo correcto (Admin, Secretaria, o Supervisor)</li>
                    <li>Los empleados solo pueden ver sus notificaciones</li>
                    <li>Solo los administradores pueden enviar notificaciones masivas</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}
