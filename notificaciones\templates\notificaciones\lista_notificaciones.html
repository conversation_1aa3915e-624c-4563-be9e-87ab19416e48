{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}

{% block title %}Gestión de Notificaciones{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-bell text-primary"></i>
                        Gestión de Notificaciones
                    </h2>
                    <p class="text-muted mb-0">Administre las notificaciones del sistema</p>
                </div>
                
                {% if user_permissions.can_create_notifications %}
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-plus"></i> Nueva Notificación
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{% url 'notificaciones:crear_notificacion_usuario' %}">
                                <i class="fas fa-user"></i> A Usuarios Específicos
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'notificaciones:crear_notificacion_grupo' %}">
                                <i class="fas fa-users"></i> A Grupos
                            </a>
                        </li>
                        {% if user_permissions.can_send_mass_notifications %}
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-warning" href="{% url 'notificaciones:crear_notificacion_masiva' %}">
                                <i class="fas fa-broadcast-tower"></i> Notificación Masiva
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_notificaciones }}</h4>
                            <p class="mb-0">Total Notificaciones</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ notificaciones_hoy }}</h4>
                            <p class="mb-0">Enviadas Hoy</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="usuarios-activos">-</h4>
                            <p class="mb-0">Usuarios Notificados</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="grupos-notificados">-</h4>
                            <p class="mb-0">Grupos Notificados</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter"></i> Filtros
            </h5>
        </div>
        <div class="card-body">
            {% crispy filtro_form %}
        </div>
    </div>

    <!-- Lista de Notificaciones -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Notificaciones Creadas
            </h5>
        </div>
        <div class="card-body">
            {% if notificaciones %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Mensaje</th>
                                <th>Tipo</th>
                                <th>Destinatarios</th>
                                <th>Fecha</th>
                                <th>Estado</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for notificacion in notificaciones %}
                            <tr>
                                <td>
                                    <span class="badge bg-secondary">#{{ notificacion.id }}</span>
                                </td>
                                <td>
                                    <div>
                                        {% if notificacion.titulo %}
                                            <strong>{{ notificacion.titulo }}</strong><br>
                                        {% endif %}
                                        <span class="text-muted">
                                            {{ notificacion.mensaje|truncatechars:60 }}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ notificacion.get_tipo_display_color }}">
                                        <i class="{{ notificacion.get_icono }}"></i>
                                        {{ notificacion.get_tipo_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        {% if notificacion.usuarios_notificados.count > 0 %}
                                            <small class="text-primary">
                                                <i class="fas fa-user"></i> 
                                                {{ notificacion.usuarios_notificados.count }} usuario(s)
                                            </small>
                                        {% endif %}
                                        {% if notificacion.grupos_notificados.count > 0 %}
                                            <small class="text-success">
                                                <i class="fas fa-users"></i> 
                                                {{ notificacion.grupos_notificados.count }} grupo(s)
                                            </small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ notificacion.fecha_creacion|date:"d/m/Y H:i" }}
                                    </small>
                                </td>
                                <td>
                                    {% if notificacion.is_active %}
                                        <span class="badge bg-success">Activa</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactiva</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'notificaciones:detalle_notificacion' notificacion.pk %}" 
                                           class="btn btn-outline-primary" title="Ver detalles">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user_permissions.can_manage_all_notifications or notificacion.creado_por == user %}
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmarDesactivar({{ notificacion.id }})" 
                                                    title="Desactivar">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginación -->
                {% if is_paginated %}
                <nav aria-label="Paginación de notificaciones">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.tipo %}&tipo={{ request.GET.tipo }}{% endif %}{% if request.GET.fecha_desde %}&fecha_desde={{ request.GET.fecha_desde }}{% endif %}{% if request.GET.fecha_hasta %}&fecha_hasta={{ request.GET.fecha_hasta }}{% endif %}{% if request.GET.creado_por %}&creado_por={{ request.GET.creado_por }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.tipo %}&tipo={{ request.GET.tipo }}{% endif %}{% if request.GET.fecha_desde %}&fecha_desde={{ request.GET.fecha_desde }}{% endif %}{% if request.GET.fecha_hasta %}&fecha_hasta={{ request.GET.fecha_hasta }}{% endif %}{% if request.GET.creado_por %}&creado_por={{ request.GET.creado_por }}{% endif %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.tipo %}&tipo={{ request.GET.tipo }}{% endif %}{% if request.GET.fecha_desde %}&fecha_desde={{ request.GET.fecha_desde }}{% endif %}{% if request.GET.fecha_hasta %}&fecha_hasta={{ request.GET.fecha_hasta }}{% endif %}{% if request.GET.creado_por %}&creado_por={{ request.GET.creado_por }}{% endif %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.tipo %}&tipo={{ request.GET.tipo }}{% endif %}{% if request.GET.fecha_desde %}&fecha_desde={{ request.GET.fecha_desde }}{% endif %}{% if request.GET.fecha_hasta %}&fecha_hasta={{ request.GET.fecha_hasta }}{% endif %}{% if request.GET.creado_por %}&creado_por={{ request.GET.creado_por }}{% endif %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No hay notificaciones</h5>
                    <p class="text-muted">No se encontraron notificaciones con los filtros aplicados.</p>
                    {% if user_permissions.can_create_notifications %}
                        <a href="{% url 'notificaciones:crear_notificacion_usuario' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Crear Primera Notificación
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Cargar estadísticas adicionales via AJAX
document.addEventListener('DOMContentLoaded', function() {
    fetch('{% url "notificaciones:estadisticas_ajax" %}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('usuarios-activos').textContent = data.total_usuarios_notificados;
            document.getElementById('grupos-notificados').textContent = data.total_grupos_notificados;
        })
        .catch(error => console.error('Error:', error));
});

// Función para confirmar desactivación
function confirmarDesactivar(notificacionId) {
    Swal.fire({
        title: '¿Desactivar notificación?',
        text: 'Esta acción desactivará la notificación pero no la eliminará.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sí, desactivar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            // Aquí iría la lógica para desactivar
            Swal.fire(
                'Desactivada',
                'La notificación ha sido desactivada.',
                'success'
            );
        }
    });
}
</script>
{% endblock %}
