"""
Template tags para el módulo de asignaciones.

Proporciona filtros y tags para verificar permisos y mostrar información
relacionada con asignaciones en los templates.
"""

from django import template
from django.contrib.auth import get_user_model
from asignaciones.models import AsignacionTicket

User = get_user_model()

register = template.Library()


@register.filter
def can_assign_ticket(user, ticket):
    """
    Verifica si un usuario puede asignar un ticket.
    
    Usage: {% if user|can_assign_ticket:ticket %}
    
    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket
        
    Returns:
        bool: True si puede asignar, False en caso contrario
    """
    if not user or not ticket:
        return False
    
    # Importar aquí para evitar circular imports
    from asignaciones.views import _puede_asignar_ticket
    return _puede_asignar_ticket(user, ticket)


@register.filter
def can_manage_assignment(user, asignacion):
    """
    Verifica si un usuario puede gestionar una asignación.
    
    Usage: {% if user|can_manage_assignment:asignacion %}
    
    Args:
        user: Usuario autenticado
        asignacion: Instancia de AsignacionTicket
        
    Returns:
        bool: True si puede gestionar, False en caso contrario
    """
    if not user or not asignacion:
        return False
    
    # Importar aquí para evitar circular imports
    from asignaciones.views import _puede_asignar_ticket
    return _puede_asignar_ticket(user, asignacion.ticket)


@register.filter
def is_assigned_to(ticket, user):
    """
    Verifica si un ticket está asignado a un usuario específico.
    
    Usage: {% if ticket|is_assigned_to:user %}
    
    Args:
        ticket: Instancia del ticket
        user: Usuario a verificar
        
    Returns:
        bool: True si está asignado, False en caso contrario
    """
    if not ticket or not user:
        return False
    
    return AsignacionTicket.objects.filter(
        ticket=ticket,
        usuario=user,
        is_active=True
    ).exists()


@register.filter
def get_assignment_for_user(ticket, user):
    """
    Obtiene la asignación activa de un ticket para un usuario específico.
    
    Usage: {% with assignment=ticket|get_assignment_for_user:user %}
    
    Args:
        ticket: Instancia del ticket
        user: Usuario a verificar
        
    Returns:
        AsignacionTicket: Asignación activa o None
    """
    if not ticket or not user:
        return None
    
    return AsignacionTicket.objects.filter(
        ticket=ticket,
        usuario=user,
        is_active=True
    ).first()


@register.filter
def count_active_assignments(ticket):
    """
    Cuenta las asignaciones activas de un ticket.
    
    Usage: {{ ticket|count_active_assignments }}
    
    Args:
        ticket: Instancia del ticket
        
    Returns:
        int: Número de asignaciones activas
    """
    if not ticket:
        return 0
    
    return AsignacionTicket.objects.filter(
        ticket=ticket,
        is_active=True
    ).count()


@register.filter
def get_active_assignments(ticket):
    """
    Obtiene todas las asignaciones activas de un ticket.
    
    Usage: {% for assignment in ticket|get_active_assignments %}
    
    Args:
        ticket: Instancia del ticket
        
    Returns:
        QuerySet: Asignaciones activas del ticket
    """
    if not ticket:
        return AsignacionTicket.objects.none()
    
    return AsignacionTicket.objects.filter(
        ticket=ticket,
        is_active=True
    ).select_related('usuario')


@register.simple_tag
def assignment_status_color(estado):
    """
    Retorna la clase CSS para el color del estado de asignación.
    
    Usage: {% assignment_status_color asignacion.estado %}
    
    Args:
        estado: Estado de la asignación (1-4)
        
    Returns:
        str: Clase CSS para el color
    """
    colores = {
        1: 'info',      # Asignado
        2: 'warning',   # En Progreso
        3: 'success',   # Finalizado
        4: 'danger'     # Cancelado
    }
    return colores.get(estado, 'secondary')


@register.simple_tag
def assignment_status_icon(estado):
    """
    Retorna el icono FontAwesome para el estado de asignación.
    
    Usage: {% assignment_status_icon asignacion.estado %}
    
    Args:
        estado: Estado de la asignación (1-4)
        
    Returns:
        str: Clase de icono FontAwesome
    """
    iconos = {
        1: 'fas fa-clipboard-list',  # Asignado
        2: 'fas fa-play-circle',     # En Progreso
        3: 'fas fa-check-circle',    # Finalizado
        4: 'fas fa-times-circle'     # Cancelado
    }
    return iconos.get(estado, 'fas fa-question-circle')


@register.inclusion_tag('asignaciones/tags/assignment_badge.html')
def assignment_badge(asignacion):
    """
    Renderiza un badge para mostrar el estado de una asignación.
    
    Usage: {% assignment_badge asignacion %}
    
    Args:
        asignacion: Instancia de AsignacionTicket
        
    Returns:
        dict: Contexto para el template del badge
    """
    return {
        'asignacion': asignacion,
        'color': assignment_status_color(asignacion.estado),
        'icon': assignment_status_icon(asignacion.estado)
    }


@register.inclusion_tag('asignaciones/tags/user_assignments_count.html')
def user_assignments_count(user, estado=None):
    """
    Muestra el contador de asignaciones de un usuario.
    
    Usage: {% user_assignments_count user %}
           {% user_assignments_count user estado=2 %}
    
    Args:
        user: Usuario a contar
        estado: Estado específico a contar (opcional)
        
    Returns:
        dict: Contexto con el contador
    """
    if not user:
        return {'count': 0, 'user': None}
    
    queryset = AsignacionTicket.objects.filter(usuario=user, is_active=True)
    
    if estado:
        queryset = queryset.filter(estado=estado)
    
    return {
        'count': queryset.count(),
        'user': user,
        'estado': estado
    }
