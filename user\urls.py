"""
user/urls.py
Configuración de URLs para el módulo de usuarios.
Incluye rutas para CRUD de usuarios y gestión de información relacionada.
"""
from django.urls import path
from . import views

app_name = "user"

urlpatterns = [
    # Rutas principales de usuario
    path("usuarios/", views.lista_usuarios, name="lista_usuarios"),
    path("usuarios/inactivos/", views.lista_usuarios_inactivos, name="lista_usuarios_inactivos"),
    path("usuarios/<int:pk>/", views.detalle_usuario, name="detalle_usuario"),
    path("usuarios/inactivos/<int:pk>/", views.detalle_usuario_inactivo, name="detalle_usuario_inactivo"),
    path("usuarios/<int:pk>/editar/", views.editar_usuario, name="editar_usuario"),
    path("usuarios/<int:pk>/desactivar/", views.desactivar_usuario, name="desactivar_usuario"),
    path("usuarios/<int:pk>/activar/", views.activar_usuario, name="activar_usuario"),
    path("usuarios/<int:pk>/desactivar-definitivo/", views.desactivar_usuario_definitivo, name="desactivar_usuario_definitivo"),

    # Creación por pasos
    path("usuarios/crear/paso1/", views.crear_usuario_paso1, name="crear_usuario_paso1"),
    path("usuarios/crear/paso2/", views.crear_usuario_paso2, name="crear_usuario_paso2"),
    path("usuarios/crear/paso3/", views.crear_usuario_paso3, name="crear_usuario_paso3"),

    # Mantener compatibilidad
    path("usuarios/crear/", views.crear_usuario_paso1, name="crear_usuario"),
    # Rutas para gestión de teléfonos
    path(
        "usuarios/<int:usuario_id>/telefonos/",
        views.gestionar_telefonos,
        name="gestionar_telefonos",
    ),
    path(
        "usuarios/<int:usuario_id>/telefonos/<int:telefono_id>/eliminar/",
        views.desactivar_telefono,
        name="desactivar_telefono",
    ),
    # Rutas para gestión de familiares
    path(
        "usuarios/<int:usuario_id>/familiares/",
        views.gestionar_familiares,
        name="gestionar_familiares",
    ),
    path(
        "usuarios/<int:usuario_id>/familiares/<int:familiar_id>/eliminar/",
        views.eliminar_familiar_usuario,
        name="eliminar_familiar_usuario",
    ),

    # URLs específicas para gestión de familiares (ver detalles y gestión de teléfonos)
    path(
        "usuarios/<int:usuario_id>/familiares/<int:familiar_id>/detalles-gestion/",
        views.familiar_detalles_gestion_ajax,
        name="familiar_detalles_gestion_ajax",
    ),
    path(
        "usuarios/<int:usuario_id>/familiares/<int:familiar_id>/telefonos-gestion/",
        views.familiar_telefonos_gestion_ajax,
        name="familiar_telefonos_gestion_ajax",
    ),
    path(
        "usuarios/<int:usuario_id>/familiares/<int:familiar_id>/editar/",
        views.editar_familiar,
        name="editar_familiar",
    ),
    path(
        "usuarios/<int:usuario_id>/familiares/<int:familiar_id>/eliminar/",
        views.desactivar_familiar,
        name="desactivar_familiar",
    ),
    # Rutas para gestión de cargos
    path("cargos/", views.lista_cargos, name="lista_cargos"),
    path("cargos/crear/", views.crear_cargo, name="crear_cargo"),
    path("cargos/<int:pk>/editar/", views.editar_cargo, name="editar_cargo"),
    path("cargos/<int:pk>/eliminar/", views.desactivar_cargo, name="desactivar_cargo"),
    # Rutas para API AJAX
    path("api/usuarios/buscar/", views.buscar_usuarios, name="buscar_usuarios"),
    path(
        "api/usuarios/<int:pk>/estado/",
        views.cambiar_estado_usuario,
        name="cambiar_estado_usuario",
    ),
    path("api/cargos/lista/", views.lista_cargos_json, name="lista_cargos_json"),

    # AJAX para gestión dinámica en formularios por pasos
    path("ajax/telefono/<int:telefono_id>/eliminar/", views.eliminar_telefono_ajax, name="eliminar_telefono_ajax"),
    path("ajax/familiar/<int:familiar_id>/eliminar/", views.eliminar_familiar_ajax, name="eliminar_familiar_ajax"),
    path("ajax/familiar/<int:familiar_id>/telefono-emergencia/", views.agregar_telefono_emergencia_ajax, name="agregar_telefono_emergencia_ajax"),
    path("ajax/telefono-emergencia/<int:celular_id>/eliminar/", views.eliminar_telefono_emergencia_ajax, name="eliminar_telefono_emergencia_ajax"),

    # Rutas AJAX para gestión de familiares (limpias, sin duplicación)
    path("familiar/<int:familiar_id>/detalles/", views.familiar_detalles_ajax, name="familiar_detalles_ajax"),
    path("familiar/<int:familiar_id>/telefonos/", views.familiar_telefonos_ajax, name="familiar_telefonos_ajax"),
    path("familiar/<int:familiar_id>/telefono/agregar/", views.telefono_agregar_ajax, name="telefono_agregar_ajax"),
    path("telefono/<int:telefono_id>/eliminar/", views.telefono_eliminar_ajax, name="telefono_eliminar_ajax"),
]

# Configuración adicional para servir archivos estáticos en desarrollo
from django.conf import settings
from django.conf.urls.static import static

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
