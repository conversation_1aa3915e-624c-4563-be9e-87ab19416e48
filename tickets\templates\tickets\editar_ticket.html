{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Editar Ticket #{{ ticket.id }}{% endblock %}

{% block extra_css %}
<!-- SweetAlert2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">
                                    Editar Ticket #{{ ticket.id }}
                                    <span class="badge bg-{{ ticket.get_estado_display_color }} ms-2">
                                        {{ ticket.get_estado_display }}
                                    </span>
                                </h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ rol_usuario }}
                                    <span class="ms-3">
                                        <i class="fas fa-calendar me-1"></i>Creado: {{ ticket.fecha_creacion|date:"d/m/Y H:i" }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'tickets:detalle_ticket' ticket.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Volver al Detalle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Editar Información del Ticket
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="form-editar-ticket" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Título -->
                        <div class="mb-3">
                            <label for="titulo" class="form-label">
                                Título del Ticket <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="titulo" name="titulo" 
                                   required minlength="5" maxlength="200" value="{{ ticket.titulo }}"
                                   placeholder="Descripción breve del problema o solicitud">
                            <div class="form-text">Mínimo 5 caracteres, máximo 200</div>
                        </div>

                        <!-- Descripción -->
                        <div class="mb-3">
                            <label for="descripcion" class="form-label">
                                Descripción Detallada <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="descripcion" name="descripcion" 
                                      rows="4" required minlength="10"
                                      placeholder="Describa detalladamente el problema, solicitud o situación...">{{ ticket.descripcion }}</textarea>
                            <div class="form-text">Mínimo 10 caracteres. Sea específico para una mejor atención</div>
                        </div>

                        <div class="row">
                            <!-- Área Responsable -->
                            <div class="col-md-6 mb-3">
                                <label for="area" class="form-label">
                                    Área Responsable <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="area" name="area" required>
                                    <option value="">Seleccione un área</option>
                                    {% for area in areas_disponibles %}
                                        <option value="{{ area.id }}" 
                                                {% if area.id == ticket.grupo.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Área que se encargará de resolver el ticket</div>
                            </div>

                            <!-- Prioridad -->
                            <div class="col-md-6 mb-3">
                                <label for="prioridad" class="form-label">
                                    Prioridad <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="prioridad" name="prioridad" required>
                                    {% for valor, display in prioridades_disponibles %}
                                        <option value="{{ valor }}" 
                                                {% if valor == ticket.prioridad %}selected{% endif %}>
                                            {{ display }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Nivel de urgencia del ticket</div>
                            </div>
                        </div>

                        <!-- Dirección -->
                        <div class="mb-3">
                            <label for="direccion" class="form-label">
                                Dirección Específica <span class="text-muted">(Opcional)</span>
                            </label>
                            <textarea class="form-control" id="direccion" name="direccion" 
                                      rows="2" placeholder="Dirección donde se requiere el servicio (si aplica)">{{ ticket.direccion }}</textarea>
                            <div class="form-text">Proporcione la dirección exacta si el servicio requiere visita física</div>
                        </div>

                        <!-- Imágenes del Ticket -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-images me-2"></i>Imágenes del Ticket
                                <span class="badge bg-info ms-2">{{ imagenes_actuales|length }}/3</span>
                            </h6>

                            <!-- Imágenes actuales -->
                            {% if imagenes_actuales %}
                                <div class="row g-3 mb-3" id="imagenes-actuales">
                                    {% for imagen in imagenes_actuales %}
                                        <div class="col-md-4" id="imagen-{{ imagen.id }}">
                                            <div class="card">
                                                <img src="{{ imagen.imagen.url }}"
                                                     class="card-img-top"
                                                     style="height: 150px; object-fit: cover;"
                                                     alt="{{ imagen.descripcion|default:'Imagen del ticket' }}">
                                                <div class="card-body p-2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            {{ imagen.descripcion|default:'Sin descripción' }}
                                                        </small>
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger"
                                                                onclick="eliminarImagen({{ imagen.id }})"
                                                                title="Eliminar imagen">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-calendar me-1"></i>{{ imagen.fecha_subida|date:"d/m/Y H:i" }}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-info" id="sin-imagenes">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Este ticket no tiene imágenes adjuntas.
                                </div>
                            {% endif %}

                            <!-- Agregar nuevas imágenes -->
                            {% if puede_agregar_imagenes %}
                                <div class="border rounded p-3 bg-light">
                                    <label for="nuevas_imagenes" class="form-label">
                                        <i class="fas fa-plus me-2"></i>Agregar Nuevas Imágenes
                                    </label>
                                    <input type="file"
                                           class="form-control"
                                           id="nuevas_imagenes"
                                           name="nuevas_imagenes"
                                           multiple
                                           accept="image/jpeg,image/jpg,image/png,image/webp">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Máximo 5MB cada una. Formatos: JPG, PNG, WebP
                                    </div>

                                    <!-- Preview de nuevas imágenes -->
                                    <div id="preview-nuevas-imagenes" class="mt-3" style="display: none;">
                                        <div class="row g-2" id="nuevas-imagenes-container"></div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Ya tienes el máximo de 3 imágenes. Elimina algunas para agregar nuevas.
                                </div>
                            {% endif %}
                        </div>

                        <!-- Botones -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'tickets:detalle_ticket' ticket.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary" id="btn-guardar">
                                <i class="fas fa-save me-2"></i>Guardar Cambios
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panel lateral -->
        <div class="col-lg-4">
            <!-- Información actual -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Información Actual
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Estado Actual:</small>
                        <div>
                            <span class="badge bg-{{ ticket.get_estado_display_color }}">
                                {{ ticket.get_estado_display }}
                            </span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Creado por:</small>
                        <div>{{ ticket.creado_por.get_full_name|default:ticket.creado_por.username }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Fecha de creación:</small>
                        <div>{{ ticket.fecha_creacion|date:"d/m/Y H:i" }}</div>
                    </div>
                    {% if ciudadano_actual %}
                        <div class="mb-3">
                            <small class="text-muted">Ciudadano actual:</small>
                            <div>
                                <strong>{{ ciudadano_actual.nombre_completo }}</strong><br>
                                <small>{{ ciudadano_actual.dpi }}</small>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Selección de ciudadano -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>Ciudadano Solicitante
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Ciudadano actual -->
                    {% if ciudadano_actual %}
                        <div id="ciudadano-seleccionado">
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="alert-heading mb-1">
                                            <i class="fas fa-user-check me-2"></i>Ciudadano Actual
                                        </h6>
                                        <p class="mb-0" style="color: #000;">
                                            <strong>{{ ciudadano_actual.nombre_completo }}</strong><br>
                                            <small>DPI: {{ ciudadano_actual.dpi }}</small>
                                        </p>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerCiudadano()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div id="ciudadano-seleccionado" style="display: none;">
                            <div class="alert alert-success">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="alert-heading mb-1">
                                            <i class="fas fa-user-check me-2"></i>Ciudadano Seleccionado
                                        </h6>
                                        <p class="mb-0" id="info-ciudadano-seleccionado" style="color: #000;"></p>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerCiudadano()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Botones de acción -->
                    <div class="d-grid gap-2 mb-3">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalBuscarCiudadano">
                            <i class="fas fa-search me-2"></i>{% if ciudadano_actual %}Cambiar{% else %}Buscar{% endif %} Ciudadano
                        </button>
                        <a href="{% url 'ciudadano:crear_ciudadano' %}?editar_ticket={{ ticket.id }}" class="btn btn-outline-success">
                            <i class="fas fa-user-plus me-2"></i>Registrar Nuevo Ciudadano
                        </a>
                    </div>

                    <!-- Ciudadanos recientes -->
                    {% if ciudadanos_recientes %}
                        <hr>
                        <h6 class="mb-2">
                            <i class="fas fa-clock me-2"></i>Recientes
                        </h6>
                        <div class="list-group list-group-flush">
                            {% for ciudadano in ciudadanos_recientes|slice:":3" %}
                                <div class="list-group-item px-0 py-2 border-0">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-0 fs-6" style="color: #000;">{{ ciudadano.nombre_completo|truncatechars:20 }}</h6>
                                            <small class="text-muted">{{ ciudadano.dpi }}</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="seleccionarCiudadano({{ ciudadano.id }}, '{{ ciudadano.nombre_completo }}', '{{ ciudadano.dpi }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Información de ayuda -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Consejos de Edición
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Los cambios se registrarán en el historial</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Se notificará a usuarios asignados</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Cambiar área puede requerir reasignación</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Solo edite si es necesario</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campo oculto para ciudadano seleccionado -->
<input type="hidden" id="ciudadano_id" name="ciudadano_id" form="form-editar-ticket" 
       value="{{ ciudadano_actual.id|default:'' }}">

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ffc107, #e0a800);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.avatar-sm {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.form-control:focus, .form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.list-group-item {
    transition: background-color 0.1s ease;
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.fs-6 {
    font-size: 0.875rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-editar-ticket');
    const btnGuardar = document.getElementById('btn-guardar');
    
    // Validación en tiempo real
    const titulo = document.getElementById('titulo');
    const descripcion = document.getElementById('descripcion');
    
    titulo.addEventListener('input', function() {
        if (this.value.length < 5) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    descripcion.addEventListener('input', function() {
        if (this.value.length < 10) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    // Envío del formulario
    form.addEventListener('submit', function(e) {
        btnGuardar.disabled = true;
        btnGuardar.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Guardando...';
    });
});

function seleccionarCiudadano(id, nombre, dpi) {
    document.getElementById('ciudadano_id').value = id;
    
    // Mostrar notificación
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show';
    alert.innerHTML = `
        <i class="fas fa-check me-2"></i>
        <strong>Ciudadano seleccionado:</strong> ${nombre} (${dpi})
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alert, container.firstChild);
    
    // Auto-dismiss después de 3 segundos
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}

function removerCiudadano() {
    document.getElementById('ciudadano_id').value = '';
    
    // Mostrar notificación
    const alert = document.createElement('div');
    alert.className = 'alert alert-warning alert-dismissible fade show';
    alert.innerHTML = `
        <i class="fas fa-user-minus me-2"></i>
        <strong>Ciudadano removido</strong> del ticket
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alert, container.firstChild);
    
    // Auto-dismiss después de 3 segundos
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}

// Función para eliminar imagen con SweetAlert2
function eliminarImagen(imagenId) {
    Swal.fire({
        title: '¿Eliminar imagen?',
        text: 'Esta acción no se puede deshacer',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i>Sí, eliminar',
        cancelButtonText: '<i class="fas fa-times me-2"></i>Cancelar',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // Mostrar loading
            Swal.fire({
                title: 'Eliminando imagen...',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/tickets/imagen/${imagenId}/eliminar/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remover la imagen del DOM con animación
                    const imagenElement = document.getElementById(`imagen-${imagenId}`);
                    if (imagenElement) {
                        imagenElement.style.transition = 'all 0.3s ease';
                        imagenElement.style.opacity = '0';
                        imagenElement.style.transform = 'scale(0.8)';

                        setTimeout(() => {
                            imagenElement.remove();

                            // Actualizar contador
                            actualizarContadorImagenes();

                            // Verificar si mostrar mensaje de "sin imágenes"
                            const imagenesActuales = document.querySelectorAll('#imagenes-actuales .col-md-4');
                            if (imagenesActuales.length === 0) {
                                const container = document.getElementById('imagenes-actuales');
                                if (container) {
                                    container.innerHTML = `
                                        <div class="alert alert-info" id="sin-imagenes">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Este ticket no tiene imágenes adjuntas.
                                        </div>
                                    `;
                                }
                            }

                            // Habilitar agregar imágenes si estaba deshabilitado
                            const warningAlert = document.querySelector('.alert-warning');
                            if (warningAlert && imagenesActuales.length < 3) {
                                location.reload(); // Recargar para actualizar la interfaz
                            }
                        }, 300);
                    }

                    // Mostrar mensaje de éxito
                    Swal.fire({
                        title: '¡Eliminada!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: data.error,
                        icon: 'error',
                        confirmButtonText: 'Entendido',
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        },
                        buttonsStyling: false
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error',
                    text: 'Error al eliminar la imagen',
                    icon: 'error',
                    confirmButtonText: 'Entendido',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    },
                    buttonsStyling: false
                });
            });
        }
    });
}

// Función para actualizar contador de imágenes
function actualizarContadorImagenes() {
    const imagenesActuales = document.querySelectorAll('#imagenes-actuales .col-md-4').length;
    const badge = document.querySelector('.badge.bg-info');
    if (badge) {
        badge.textContent = `${imagenesActuales}/3`;
    }
}

// Preview de nuevas imágenes
document.getElementById('nuevas_imagenes').addEventListener('change', function(e) {
    const files = e.target.files;
    const previewContainer = document.getElementById('preview-nuevas-imagenes');
    const imagenesContainer = document.getElementById('nuevas-imagenes-container');

    // Limpiar preview anterior
    imagenesContainer.innerHTML = '';

    if (files.length > 0) {
        // Validar número máximo de imágenes
        const imagenesActuales = document.querySelectorAll('#imagenes-actuales .col-md-4').length;
        const espacioDisponible = 3 - imagenesActuales;

        if (files.length > espacioDisponible) {
            Swal.fire({
                title: 'Límite excedido',
                text: `Solo puedes agregar ${espacioDisponible} imagen(es) más.`,
                icon: 'warning',
                confirmButtonText: 'Entendido',
                customClass: {
                    confirmButton: 'btn btn-primary'
                },
                buttonsStyling: false
            });
            e.target.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        previewContainer.style.display = 'block';

        // Crear preview para cada imagen
        Array.from(files).forEach((file, index) => {
            // Validar tamaño
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    title: 'Archivo muy grande',
                    text: `La imagen ${file.name} es muy grande (máximo 5MB).`,
                    icon: 'warning',
                    confirmButtonText: 'Entendido',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    },
                    buttonsStyling: false
                });
                return;
            }

            // Validar tipo
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                Swal.fire({
                    title: 'Formato no válido',
                    text: `La imagen ${file.name} no tiene un formato válido. Use JPG, PNG o WebP.`,
                    icon: 'warning',
                    confirmButtonText: 'Entendido',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    },
                    buttonsStyling: false
                });
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-md-4';

                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                        <div class="card-body p-2">
                            <small class="text-muted">${file.name}</small><br>
                            <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                        </div>
                    </div>
                `;

                imagenesContainer.appendChild(col);
            };
            reader.readAsDataURL(file);
        });
    } else {
        previewContainer.style.display = 'none';
    }
});

// Funciones para búsqueda de ciudadanos
let timeoutBusqueda;

document.getElementById('buscar-ciudadano-input').addEventListener('input', function(e) {
    const termino = e.target.value.trim();

    clearTimeout(timeoutBusqueda);

    if (termino.length < 2) {
        document.getElementById('resultados-busqueda').innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>Ingrese al menos 2 caracteres para buscar</p>
            </div>
        `;
        return;
    }

    timeoutBusqueda = setTimeout(() => {
        buscarCiudadanos(termino);
    }, 500);
});

function buscarCiudadanos(termino) {
    const loadingBusqueda = document.getElementById('loading-busqueda');
    const resultadosBusqueda = document.getElementById('resultados-busqueda');

    loadingBusqueda.style.display = 'block';
    resultadosBusqueda.style.display = 'none';

    fetch(`/ciudadano/buscar/?q=${encodeURIComponent(termino)}`)
        .then(response => response.json())
        .then(data => {
            loadingBusqueda.style.display = 'none';
            resultadosBusqueda.style.display = 'block';

            if (data.ciudadanos && data.ciudadanos.length > 0) {
                let html = '<div class="list-group">';
                data.ciudadanos.forEach(ciudadano => {
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">${ciudadano.nombre_completo}</h6>
                                    <small class="text-muted">DPI: ${ciudadano.dpi}</small>
                                    ${ciudadano.telefono ? `<br><small class="text-muted">Tel: ${ciudadano.telefono}</small>` : ''}
                                    <small class="text-info">
                                        <i class="fas fa-ticket-alt me-1"></i>${ciudadano.total_tickets} ticket(s)
                                        ${ciudadano.edad ? ` • ${ciudadano.edad} años` : ''}
                                    </small>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary"
                                        onclick="seleccionarCiudadanoModal(${ciudadano.id}, '${ciudadano.nombre_completo}', '${ciudadano.dpi}')">
                                    <i class="fas fa-check me-1"></i>Seleccionar
                                </button>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                resultadosBusqueda.innerHTML = html;
            } else {
                resultadosBusqueda.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-slash fa-2x mb-2"></i>
                        <p>No se encontraron ciudadanos</p>
                        <small>Intente con otro término de búsqueda</small>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            loadingBusqueda.style.display = 'none';
            resultadosBusqueda.style.display = 'block';
            resultadosBusqueda.innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>Error al buscar ciudadanos</p>
                </div>
            `;
        });
}

function seleccionarCiudadano(id, nombre, dpi) {
    document.getElementById('ciudadano_id').value = id;

    // Mostrar información del ciudadano seleccionado
    const ciudadanoSeleccionado = document.getElementById('ciudadano-seleccionado');

    ciudadanoSeleccionado.innerHTML = `
        <div class="alert alert-success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="alert-heading mb-1">
                        <i class="fas fa-user-check me-2"></i>Ciudadano Seleccionado
                    </h6>
                    <p class="mb-0" style="color: #000;">
                        <strong>${nombre}</strong><br>
                        <small>DPI: ${dpi}</small>
                    </p>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerCiudadano()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;

    ciudadanoSeleccionado.style.display = 'block';
}

function seleccionarCiudadanoModal(id, nombre, dpi) {
    seleccionarCiudadano(id, nombre, dpi);

    // Cerrar modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalBuscarCiudadano'));
    modal.hide();

    // Limpiar búsqueda
    document.getElementById('buscar-ciudadano-input').value = '';
    document.getElementById('resultados-busqueda').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>Ingrese un término de búsqueda</p>
        </div>
    `;
}

function removerCiudadano() {
    document.getElementById('ciudadano_id').value = '';
    document.getElementById('ciudadano-seleccionado').style.display = 'none';
}
</script>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
{% endblock %}

<!-- Modal Buscar Ciudadano -->
<div class="modal fade" id="modalBuscarCiudadano" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>Buscar Ciudadano
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Buscador -->
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="buscar-ciudadano-input"
                               placeholder="Buscar por nombre, DPI o teléfono..." autocomplete="off">
                    </div>
                    <div class="form-text">Ingrese al menos 2 caracteres para buscar</div>
                </div>

                <!-- Resultados -->
                <div id="resultados-busqueda">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>Ingrese un término de búsqueda</p>
                    </div>
                </div>

                <!-- Loading -->
                <div id="loading-busqueda" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Buscando...</span>
                    </div>
                    <p class="mt-2 text-muted">Buscando ciudadanos...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <a href="{% url 'ciudadano:crear_ciudadano' %}?editar_ticket={{ ticket.id }}" class="btn btn-success">
                    <i class="fas fa-user-plus me-2"></i>Registrar Nuevo
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Campo oculto para ciudadano seleccionado -->
<input type="hidden" id="ciudadano_id" name="ciudadano_id" form="form-editar-ticket"
       value="{% if ciudadano_actual %}{{ ciudadano_actual.id }}{% endif %}">
