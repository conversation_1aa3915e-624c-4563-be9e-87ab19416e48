{% extends 'Base/base.html' %}

{% block title %}Estadísticas de Seguridad{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-shield-alt text-danger"></i>
                        Estadísticas de Seguridad
                    </h2>
                    <p class="text-muted mb-0">Monitoreo de intentos de acceso no autorizado</p>
                </div>
                <div>
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> 
                        Última actualización: {{ last_updated|date:"d/m/Y H:i:s" }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas Generales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.total_attempts }}</h4>
                            <p class="mb-0">Total Intentos</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.attempts_24h }}</h4>
                            <p class="mb-0">Últimas 24h</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.attempts_7d }}</h4>
                            <p class="mb-0">Últimos 7 días</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.attempts_30d }}</h4>
                            <p class="mb-0">Últimos 30 días</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usuarios en Riesgo -->
    {% if users_at_risk %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Usuarios en Riesgo de Deslogueo
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <strong>¡Atención!</strong> Los siguientes usuarios tienen múltiples intentos recientes y podrían ser deslogueados automáticamente:
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Usuario</th>
                                    <th>Nombre Completo</th>
                                    <th>Intentos Recientes (30 min)</th>
                                    <th>Estado</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users_at_risk %}
                                <tr>
                                    <td><strong>{{ user.username }}</strong></td>
                                    <td>{{ user.full_name|default:"-" }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ user.recent_attempts }}</span>
                                    </td>
                                    <td>
                                        {% if user.recent_attempts >= 3 %}
                                            <span class="badge bg-danger">DESLOGUEADO</span>
                                        {% elif user.recent_attempts == 2 %}
                                            <span class="badge bg-warning">ADVERTENCIA FINAL</span>
                                        {% else %}
                                            <span class="badge bg-warning">EN RIESGO</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Análisis Detallado -->
    <div class="row">
        <!-- Top Usuarios -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i>
                        Usuarios con Más Intentos (7 días)
                    </h5>
                </div>
                <div class="card-body">
                    {% if top_users %}
                        <div class="list-group list-group-flush">
                            {% for user in top_users %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong class="text-dark">{{ user.user__username }}</strong>
                                    {% if user.user__first_name %}
                                        <br><small class="text-muted">{{ user.user__first_name }} {{ user.user__last_name }}</small>
                                    {% endif %}
                                </div>
                                <span class="badge bg-danger-subtle text-danger rounded-pill">{{ user.attempt_count }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No hay intentos en los últimos 7 días.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top IPs -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-network-wired"></i>
                        IPs con Más Intentos (7 días)
                    </h5>
                </div>
                <div class="card-body">
                    {% if top_ips %}
                        <div class="list-group list-group-flush">
                            {% for ip in top_ips %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <code class="text-dark">{{ ip.ip_address }}</code>
                                <span class="badge bg-warning-subtle text-warning rounded-pill">{{ ip.attempt_count }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No hay intentos en los últimos 7 días.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top URLs -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-link"></i>
                        URLs Más Intentadas (7 días)
                    </h5>
                </div>
                <div class="card-body">
                    {% if top_urls %}
                        <div class="list-group list-group-flush">
                            {% for url in top_urls %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <small class="text-dark" style="word-break: break-all; flex: 1; margin-right: 10px;">{{ url.url_attempted }}</small>
                                <span class="badge bg-info-subtle text-info rounded-pill flex-shrink-0">{{ url.attempt_count }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No hay intentos en los últimos 7 días.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Intentos Recientes -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i>
                        Intentos Recientes (24 horas)
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_attempts %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Fecha/Hora</th>
                                        <th>Usuario</th>
                                        <th>IP</th>
                                        <th>URL Intentada</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attempt in recent_attempts %}
                                    <tr>
                                        <td>
                                            <small>{{ attempt.timestamp|date:"d/m/Y H:i:s" }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ attempt.user.username }}</strong>
                                            {% if attempt.user.get_full_name %}
                                                <br><small class="text-muted">{{ attempt.user.get_full_name }}</small>
                                            {% endif %}
                                        </td>
                                        <td><code>{{ attempt.ip_address }}</code></td>
                                        <td>
                                            <small class="text-dark d-block" style="word-break: break-all;">
                                                {{ attempt.url_attempted }}
                                            </small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No hay intentos recientes en las últimas 24 horas.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones de Administración -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools"></i>
                        Acciones
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        {% if user.is_superuser %}
                        <a href="/admin/Base/unauthorizedaccessattempt/" class="btn btn-primary" target="_blank">
                            <i class="fas fa-cog"></i> Ver en Admin
                        </a>
                        {% endif %}
                        <button class="btn btn-secondary" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Actualizar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh cada 30 segundos
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
