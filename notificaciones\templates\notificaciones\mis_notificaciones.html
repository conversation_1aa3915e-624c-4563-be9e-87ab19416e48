{% extends 'Base/base.html' %}

{% block title %}Mis Notificaciones{% endblock %}

{% block extra_css %}
<!-- SweetAlert2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-inbox text-primary"></i>
                        Mis Notificaciones
                    </h2>
                    <p class="text-muted mb-0">Revise las notificaciones que ha recibido</p>
                </div>
                <div>
                    {% if estadisticas.no_leidas > 0 %}
                    <form method="post" action="{% url 'notificaciones:marcar_todas_leidas' %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check-double"></i> Marcar Todas como Leídas
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ estadisticas.total }}</h4>
                            <p class="mb-0">Total Recibidas</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ estadisticas.no_leidas }}</h4>
                            <p class="mb-0">No Leídas</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope-open fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ estadisticas.leidas }}</h4>
                            <p class="mb-0">Leídas</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Notificaciones -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Notificaciones Recibidas
            </h5>
        </div>
        <div class="card-body p-0">
            {% if notificaciones %}
                <div class="list-group list-group-flush">
                    {% for notif in notificaciones %}
                    <div class="list-group-item {% if not notif.leida %}list-group-item-warning{% endif %}">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="d-flex align-items-center">
                                    {% if not notif.leida %}
                                        <span class="badge bg-warning me-2">NUEVA</span>
                                    {% endif %}
                                    <i class="{{ notif.notificacion.get_icono }} fa-2x text-{{ notif.notificacion.get_tipo_display_color }}"></i>
                                </div>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        {% if notif.notificacion.titulo %}
                                            <h6 class="mb-1 {% if not notif.leida %}fw-bold text-dark{% else %}text-dark{% endif %}">
                                                {{ notif.notificacion.titulo }}
                                            </h6>
                                        {% endif %}
                                        <p class="mb-1 {% if not notif.leida %}fw-bold text-dark{% else %}text-dark{% endif %}" style="color: #212529 !important;">
                                            {{ notif.notificacion.mensaje }}
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-user"></i> 
                                            {{ notif.notificacion.creado_por.get_full_name|default:notif.notificacion.creado_por.username }}
                                            • 
                                            <i class="fas fa-clock"></i> 
                                            {{ notif.fecha_envio|date:"d/m/Y H:i" }}
                                            {% if notif.leida %}
                                                • 
                                                <i class="fas fa-check text-success"></i> 
                                                Leída el {{ notif.fecha_lectura|date:"d/m/Y H:i" }}
                                            {% endif %}
                                        </small>
                                    </div>
                                    <div class="ms-3">
                                        <span class="badge bg-{{ notif.notificacion.get_tipo_display_color }}">
                                            {{ notif.notificacion.get_tipo_display }}
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Acciones -->
                                <div class="mt-2">
                                    {% if notif.notificacion.url_accion %}
                                        <a href="{{ notif.notificacion.url_accion }}" 
                                           class="btn btn-sm btn-outline-primary me-2"
                                           target="_blank">
                                            <i class="fas fa-external-link-alt"></i> Ver Acción
                                        </a>
                                    {% endif %}
                                    
                                    {% if not notif.leida %}
                                        <form method="post" 
                                              action="{% url 'notificaciones:marcar_leida' notif.id %}" 
                                              class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i> Marcar como Leída
                                            </button>
                                        </form>
                                    {% endif %}
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Paginación -->
                {% if is_paginated %}
                <div class="card-footer">
                    <nav aria-label="Paginación de notificaciones">
                        <ul class="pagination justify-content-center mb-0">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No hay notificaciones</h5>
                    <p class="text-muted">No ha recibido notificaciones aún.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Información
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-bell text-info"></i>
                            Las notificaciones nuevas aparecen resaltadas
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-info"></i>
                            Puede marcar notificaciones como leídas individualmente
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-link text-info"></i>
                            Algunas notificaciones incluyen enlaces de acción
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-history text-info"></i>
                            Se mantiene un historial completo de notificaciones
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb"></i> Consejos
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Revise regularmente sus notificaciones
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Marque como leídas las notificaciones procesadas
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Use el botón "Marcar todas" para limpiar la bandeja
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            Las notificaciones importantes aparecen primero
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh cada 30 segundos para nuevas notificaciones
    setInterval(function() {
        // Verificar si hay nuevas notificaciones
        fetch('{% url "notificaciones:contar_no_leidas" %}')
            .then(response => response.json())
            .then(data => {
                if (data.count > {{ estadisticas.no_leidas }}) {
                    // Mostrar notificación de nuevas notificaciones
                    const toast = document.createElement('div');
                    toast.className = 'toast position-fixed top-0 end-0 m-3';
                    toast.innerHTML = `
                        <div class="toast-header bg-primary text-white">
                            <i class="fas fa-bell me-2"></i>
                            <strong class="me-auto">Nueva Notificación</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">
                            Tiene nuevas notificaciones. <a href="#" onclick="location.reload()">Actualizar página</a>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.show();
                    
                    // Remover el toast después de que se oculte
                    toast.addEventListener('hidden.bs.toast', function() {
                        document.body.removeChild(toast);
                    });
                }
            })
            .catch(error => console.error('Error:', error));
    }, 30000);

    // Marcar como leída via AJAX
    document.querySelectorAll('form[action*="marcar-leida"]').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            // Extraer ID correctamente de la URL: /notificaciones/88/marcar-leida/
            const urlParts = form.action.split('/');
            const notificacionId = urlParts[urlParts.length - 3]; // Penúltimo elemento antes de 'marcar-leida/'
            
            fetch('{% url "notificaciones:marcar_leida_ajax" %}', {
                method: 'POST',
                body: new URLSearchParams({
                    'notificacion_id': notificacionId,
                    'csrfmiddlewaretoken': formData.get('csrfmiddlewaretoken')
                }),
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Actualizar la interfaz
                    const listItem = form.closest('.list-group-item');
                    listItem.classList.remove('list-group-item-warning');
                    
                    // Remover badge "NUEVA"
                    const badge = listItem.querySelector('.badge.bg-warning');
                    if (badge) badge.remove();
                    
                    // Remover el botón de marcar como leída
                    form.remove();
                    
                    // Actualizar contador
                    location.reload();
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'Entendido'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Fallback al envío normal del formulario
                form.submit();
            });
        });
    });
});
</script>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
{% endblock %}
