"""
tickets/forms.py
Formularios para la gestión de tickets.

Incluye:
- TicketForm: Formulario para crear/editar tickets
- TicketImagenForm: Formulario para subir imágenes
"""

from django import forms
from django.core.exceptions import ValidationError
from .models import Ticket, TicketImagen
from django.contrib.auth.models import Group
from ciudadano.models import Ciudadano


class MultipleFileInput(forms.ClearableFileInput):
    """
    Widget personalizado para subir múltiples archivos.
    """
    allow_multiple_selected = True


class MultipleFileField(forms.FileField):
    """
    Campo personalizado para manejar múltiples archivos.
    """
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result


class TicketForm(forms.ModelForm):
    """
    Formulario para crear y editar tickets.
    """
    
    # Campo para seleccionar ciudadano
    ciudadano = forms.ModelChoiceField(
        queryset=Ciudadano.objects.filter(is_active=True),
        required=False,
        empty_label="Seleccionar ciudadano (opcional)",
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'id_ciudadano'
        })
    )
    
    class Meta:
        model = Ticket
        fields = ['titulo', 'descripcion', 'prioridad', 'grupo', 'direccion']
        widgets = {
            'titulo': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ingrese el título del ticket',
                'maxlength': '200'
            }),
            'descripcion': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Describa detalladamente el problema o solicitud',
                'rows': 4
            }),
            'prioridad': forms.Select(attrs={
                'class': 'form-select'
            }),
            'grupo': forms.Select(attrs={
                'class': 'form-select'
            }),
            'direccion': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Dirección específica donde se requiere el servicio (opcional)',
                'rows': 2
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Filtrar grupos disponibles según el usuario
        if user:
            if hasattr(user, 'cargo') and user.cargo and user.cargo.nombre == 'Administrador':
                # Administrador puede asignar a cualquier grupo
                self.fields['grupo'].queryset = Group.objects.all()
            elif user.groups.filter(name='Secretaria').exists():
                # Secretaria puede asignar a cualquier grupo
                self.fields['grupo'].queryset = Group.objects.all()
            elif user.is_supervisor:
                # Supervisor solo puede asignar a su grupo
                user_groups = user.groups.all()
                self.fields['grupo'].queryset = user_groups
            else:
                # Empleados no pueden crear tickets (no deberían llegar aquí)
                self.fields['grupo'].queryset = Group.objects.none()
        
        # Filtrar ciudadanos recientes (últimos 50)
        self.fields['ciudadano'].queryset = Ciudadano.objects.filter(
            is_active=True
        ).order_by('-fecha_registro')[:50]


class TicketImagenForm(forms.ModelForm):
    """
    Formulario para subir imágenes de tickets.
    """
    
    class Meta:
        model = TicketImagen
        fields = ['imagen', 'descripcion']
        widgets = {
            'imagen': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/jpg,image/png,image/webp',
                'multiple': False
            }),
            'descripcion': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Descripción de la imagen (opcional)',
                'maxlength': '200'
            }),
        }
    
    def clean_imagen(self):
        """
        Valida la imagen subida.
        """
        imagen = self.cleaned_data.get('imagen')
        
        if imagen:
            # Validar tamaño (máximo 5MB)
            if imagen.size > 5 * 1024 * 1024:  # 5MB
                raise ValidationError('La imagen no puede ser mayor a 5MB.')
            
            # Validar tipo de archivo
            if hasattr(imagen, 'content_type'):
                allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
                if imagen.content_type not in allowed_types:
                    raise ValidationError('Solo se permiten archivos JPG, PNG y WebP.')
            
            # Validar extensión
            if hasattr(imagen, 'name'):
                ext = imagen.name.split('.')[-1].lower()
                if ext not in ['jpg', 'jpeg', 'png', 'webp']:
                    raise ValidationError('Solo se permiten archivos JPG, PNG y WebP.')
        
        return imagen


class MultipleImageUploadForm(forms.Form):
    """
    Formulario para subir múltiples imágenes a un ticket.
    """

    imagenes = MultipleFileField(
        widget=MultipleFileInput(attrs={
            'class': 'form-control',
            'accept': 'image/jpeg,image/jpg,image/png,image/webp',
        }),
        help_text='Seleccione hasta 3 imágenes (máximo 5MB cada una)',
        required=False
    )
    
    def clean_imagenes(self):
        """
        Valida las imágenes subidas.
        """
        imagenes = self.files.getlist('imagenes')
        
        if imagenes:
            # Validar número máximo de imágenes
            if len(imagenes) > 3:
                raise ValidationError('Solo se pueden subir máximo 3 imágenes.')
            
            # Validar cada imagen
            for imagen in imagenes:
                # Validar tamaño
                if imagen.size > 5 * 1024 * 1024:  # 5MB
                    raise ValidationError(f'La imagen {imagen.name} no puede ser mayor a 5MB.')
                
                # Validar tipo
                if hasattr(imagen, 'content_type'):
                    allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
                    if imagen.content_type not in allowed_types:
                        raise ValidationError(f'La imagen {imagen.name} debe ser JPG, PNG o WebP.')
                
                # Validar extensión
                if hasattr(imagen, 'name'):
                    ext = imagen.name.split('.')[-1].lower()
                    if ext not in ['jpg', 'jpeg', 'png', 'webp']:
                        raise ValidationError(f'La imagen {imagen.name} debe tener extensión JPG, PNG o WebP.')
        
        return imagenes


class BusquedaCiudadanoForm(forms.Form):
    """
    Formulario para buscar ciudadanos por DPI o nombre.
    """
    
    busqueda = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Buscar por DPI o nombre del ciudadano',
            'id': 'busqueda_ciudadano'
        }),
        help_text='Ingrese el DPI o nombre del ciudadano'
    )
    
    def clean_busqueda(self):
        """
        Valida el término de búsqueda.
        """
        busqueda = self.cleaned_data.get('busqueda', '').strip()
        
        if len(busqueda) < 3:
            raise ValidationError('Ingrese al menos 3 caracteres para buscar.')
        
        return busqueda
