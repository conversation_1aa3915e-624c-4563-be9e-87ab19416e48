{% extends 'Base/base.html' %}
{% load static %}

{% block title %}{{ ciudadano.nombre_completo }} - Detalle{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header del ciudadano -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">{{ ciudadano.nombre_completo }}</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-id-card me-1"></i>DPI: {{ ciudadano.dpi }}
                                    <span class="ms-3">
                                        <i class="fas fa-calendar me-1"></i>Registrado: {{ ciudadano.fecha_registro|date:"d/m/Y" }}
                                    </span>
                                    {% if stats.edad %}
                                        <span class="ms-3">
                                            <i class="fas fa-birthday-cake me-1"></i>{{ stats.edad }} años
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>Volver
                            </a>
                            {% if puede_crear_ticket %}
                                <a href="{% url 'tickets:crear_ticket' %}?ciudadano_id={{ ciudadano.id }}" class="btn btn-success me-2">
                                    <i class="fas fa-ticket-alt me-2"></i>Crear Ticket
                                </a>
                            {% endif %}
                            {% if puede_editar %}
                                <a href="{% url 'ciudadano:editar_ciudadano' ciudadano.id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>Editar
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Información del ciudadano -->
        <div class="col-lg-4">
            <!-- Datos personales -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Información Personal
                    </h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">DPI:</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-secondary">{{ ciudadano.dpi }}</span>
                        </dd>
                        
                        {% if ciudadano.telefono %}
                            <dt class="col-sm-4">Teléfono:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-info">
                                    <i class="fas fa-phone me-1"></i>{{ ciudadano.telefono }}
                                </span>
                            </dd>
                        {% endif %}
                        
                        {% if ciudadano.email %}
                            <dt class="col-sm-4">Email:</dt>
                            <dd class="col-sm-8">
                                <a href="mailto:{{ ciudadano.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>{{ ciudadano.email }}
                                </a>
                            </dd>
                        {% endif %}
                        
                        {% if ciudadano.fecha_nacimiento %}
                            <dt class="col-sm-4">Nacimiento:</dt>
                            <dd class="col-sm-8">
                                {{ ciudadano.fecha_nacimiento|date:"d/m/Y" }}
                                {% if stats.edad %}
                                    <small class="text-muted">({{ stats.edad }} años)</small>
                                {% endif %}
                            </dd>
                        {% endif %}
                        
                        {% if ciudadano.genero %}
                            <dt class="col-sm-4">Género:</dt>
                            <dd class="col-sm-8">{{ ciudadano.get_genero_display }}</dd>
                        {% endif %}
                        
                        <dt class="col-sm-4">Registrado:</dt>
                        <dd class="col-sm-8">{{ ciudadano.fecha_registro|date:"d/m/Y H:i" }}</dd>
                    </dl>
                    
                    {% if ciudadano.direccion %}
                        <div class="mt-3">
                            <strong>Dirección:</strong>
                            <p class="text-muted mb-0">{{ ciudadano.direccion }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Estadísticas -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Estadísticas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ stats.total_tickets }}</h4>
                                <small class="text-muted">Total Tickets</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning mb-1">{{ stats.tickets_activos }}</h4>
                            <small class="text-muted">Activos</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h4 class="text-success mb-1">{{ stats.tickets_cerrados }}</h4>
                            <small class="text-muted">Tickets Cerrados</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tickets del ciudadano -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-ticket-alt me-2"></i>Tickets del Ciudadano
                        </h5>
                        {% if puede_crear_ticket %}
                            <a href="{% url 'tickets:crear_ticket' %}?ciudadano_id={{ ciudadano.id }}" 
                               class="btn btn-sm btn-success">
                                <i class="fas fa-plus me-2"></i>Nuevo Ticket
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if tickets_ciudadano %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="80">#</th>
                                        <th>Título</th>
                                        <th width="120">Estado</th>
                                        <th width="100">Prioridad</th>
                                        <th width="120">Fecha</th>
                                        <th width="100">Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for ticket_ciudadano in tickets_ciudadano %}
                                        {% with ticket=ticket_ciudadano.ticket %}
                                            <tr>
                                                <td>
                                                    <span class="badge bg-primary">#{{ ticket.id }}</span>
                                                </td>
                                                <td>
                                                    <h6 class="mb-1">
                                                        <a href="{% url 'tickets:detalle_ticket' ticket.id %}" 
                                                           class="text-decoration-none">
                                                            {{ ticket.titulo|truncatechars:40 }}
                                                        </a>
                                                    </h6>
                                                    <small class="text-muted">
                                                        {{ ticket.descripcion|truncatechars:60 }}
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ ticket.get_estado_display_color }}">
                                                        {{ ticket.get_estado_display }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ ticket.get_prioridad_display_color }}">
                                                        {{ ticket.get_prioridad_display|title }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        {{ ticket.fecha_creacion|date:"d/m/Y" }}<br>
                                                        {{ ticket.fecha_creacion|time:"H:i" }}
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="{% url 'tickets:detalle_ticket' ticket.id %}" 
                                                       class="btn btn-sm btn-outline-info" title="Ver ticket">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endwith %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if stats.total_tickets > 10 %}
                            <div class="text-center mt-3">
                                <a href="{% url 'tickets:lista_tickets' %}?ciudadano={{ ciudadano.id }}" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>Ver Todos los Tickets ({{ stats.total_tickets }})
                                </a>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Sin Tickets Registrados</h5>
                            <p class="text-muted">Este ciudadano no ha creado ningún ticket aún</p>
                            {% if puede_crear_ticket %}
                                <a href="{% url 'tickets:crear_ticket' %}?ciudadano_id={{ ciudadano.id }}" 
                                   class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Crear Primer Ticket
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #17a2b8, #138496);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.badge {
    font-weight: 500;
}

.table h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.table small {
    font-size: 0.8rem;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

dl.row dt {
    font-weight: 600;
    color: #495057;
}

dl.row dd {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}
