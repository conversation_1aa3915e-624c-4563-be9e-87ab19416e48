"""
ciudadano/views.py
Vistas para la gestión completa de ciudadanos del sistema.

Implementa funcionalidades para:
- Listar ciudadanos con búsqueda y filtros
- Crear y editar ciudadanos
- Ver detalles y tickets asociados
- Búsqueda AJAX para selección en tickets

Todas las vistas incluyen:
- Decoradores de seguridad apropiados
- Validaciones de permisos por rol
- Optimizaciones de consultas
- Documentación detallada
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, HttpResponseForbidden
from django.core.paginator import Paginator
from django.db.models import Q, Count, Case, When, IntegerField
from django.template.loader import render_to_string
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from datetime import datetime

# Usar el sistema centralizado de permisos
from permissions.decorators import can_manage_citizens_required

from .models import Ciudadano, CiudadanoTicket
from tickets.models import Ticket

# ============================================================================
# FUNCIONES AUXILIARES PARA PERMISOS
# ============================================================================

def _puede_gestionar_ciudadanos(user):
    """Verifica si el usuario puede gestionar ciudadanos (secretarias y administradores)."""
    return (user.groups.filter(name='Secretaria').exists() or
            (user.cargo and user.cargo.nombre == 'Administrador') or
            user.is_superuser)


def _puede_ver_ticket_ciudadano(user, ticket):
    """
    Verifica si el usuario puede ver un ticket específico en el detalle del ciudadano.
    Usa la misma lógica que el sistema principal de tickets.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        bool: True si puede ver el ticket, False en caso contrario
    """
    # Usar las mismas funciones que el sistema principal
    from tickets.views import _es_administrador, _es_secretaria, _es_supervisor, _get_area_usuario

    # Administradores pueden ver todos los tickets
    if _es_administrador(user):
        return True

    # Secretarias pueden ver todos los tickets
    if _es_secretaria(user):
        return True

    # Supervisores pueden ver tickets de su área
    if _es_supervisor(user):
        user_area = _get_area_usuario(user)
        return user_area and user_area.id == ticket.grupo.id

    # Empleados solo pueden ver tickets donde están asignados
    return ticket.asignaciones.filter(usuario=user, is_active=True).exists()


def _puede_crear_tickets(user):
    """
    Verifica si el usuario puede crear tickets desde el detalle del ciudadano.
    Usa la misma lógica que el sistema principal.

    Args:
        user: Usuario autenticado

    Returns:
        bool: True si puede crear tickets, False en caso contrario
    """
    # Usar las mismas funciones que el sistema principal
    from tickets.views import _es_administrador, _es_secretaria

    # Solo Administradores y Secretarias pueden crear tickets
    return _es_administrador(user) or _es_secretaria(user)


# ============================================================================
# VISTAS PRINCIPALES DE CIUDADANOS
# ============================================================================

@can_manage_citizens_required
def lista_ciudadanos(request):
    """
    Lista paginada de ciudadanos con búsqueda y filtros.

    Solo secretarias y administradores pueden acceder.
    Incluye búsqueda por nombre, DPI, teléfono y email.

    Args:
        request: HttpRequest object con información del usuario autenticado

    Returns:
        HttpResponse: Render del template con lista de ciudadanos
        JsonResponse: Para peticiones AJAX (scroll infinito)

    Security:
        - Requiere autenticación (@login_required)
        - Solo secretarias y administradores pueden acceder
    """
    user = request.user

    # Query base optimizada
    queryset = Ciudadano.objects.filter(is_active=True).annotate(
        total_tickets=Count('tickets'),
        tickets_activos=Count('tickets', filter=Q(tickets__ticket__is_active=True))
    )

    # Aplicar búsqueda
    busqueda = request.GET.get('q', '').strip()
    if busqueda:
        queryset = queryset.filter(
            Q(nombre_completo__icontains=busqueda) |
            Q(dpi__icontains=busqueda) |
            Q(telefono__icontains=busqueda) |
            Q(email__icontains=busqueda)
        )

    # Filtros adicionales
    con_tickets = request.GET.get('con_tickets')
    if con_tickets == 'si':
        queryset = queryset.filter(total_tickets__gt=0)
    elif con_tickets == 'no':
        queryset = queryset.filter(total_tickets=0)

    # Ordenamiento
    queryset = queryset.order_by('-fecha_registro')

    # Paginación
    paginator = Paginator(queryset, 20)
    page = request.GET.get('page', 1)
    ciudadanos = paginator.get_page(page)

    # Respuesta AJAX para scroll infinito
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        html = render_to_string(
            'ciudadano/parciales/lista_ciudadanos.html',
            {'ciudadanos': ciudadanos},
            request=request
        )
        return JsonResponse({
            'html': html,
            'has_next': ciudadanos.has_next()
        })

    # Contexto para template principal
    context = {
        'ciudadanos': ciudadanos,
        'total_ciudadanos': queryset.count(),
        'busqueda': busqueda,
        'con_tickets': con_tickets,
        'puede_crear': _puede_gestionar_ciudadanos(user)
    }

    return render(request, 'ciudadano/lista_ciudadanos.html', context)

@can_manage_citizens_required
@require_http_methods(["GET"])
def buscar_ciudadano(request):
    """
    Búsqueda AJAX de ciudadanos para selección en tickets.

    Permite búsqueda por nombre, DPI, teléfono con resultados en tiempo real.

    Args:
        request: HttpRequest object con parámetro 'q' para búsqueda

    Returns:
        JsonResponse: Lista de ciudadanos encontrados

    Security:
        - Requiere autenticación (@login_required)
        - Solo métodos GET (@require_http_methods)
    """
    user = request.user

    # Verificar permisos básicos (cualquier usuario autenticado puede buscar para tickets)
    if not user.is_authenticated:
        return JsonResponse({'error': 'No autorizado'}, status=403)

    busqueda = request.GET.get('q', '').strip()

    if len(busqueda) < 2:
        return JsonResponse({
            'ciudadanos': [],
            'mensaje': 'Ingrese al menos 2 caracteres para buscar'
        })

    # Búsqueda optimizada
    ciudadanos = Ciudadano.objects.filter(
        Q(nombre_completo__icontains=busqueda) |
        Q(dpi__icontains=busqueda) |
        Q(telefono__icontains=busqueda),
        is_active=True
    ).annotate(
        total_tickets=Count('tickets')
    ).order_by('nombre_completo')[:15]  # Máximo 15 resultados

    # Formatear resultados
    resultados = []
    for ciudadano in ciudadanos:
        resultados.append({
            'id': ciudadano.id,
            'nombre_completo': ciudadano.nombre_completo,
            'dpi': ciudadano.dpi,
            'telefono': ciudadano.telefono or 'Sin teléfono',
            'email': ciudadano.email or 'Sin email',
            'total_tickets': ciudadano.total_tickets,
            'edad': ciudadano.get_edad()
        })

    return JsonResponse({
        'ciudadanos': resultados,
        'total': len(resultados),
        'busqueda': busqueda
    })

@can_manage_citizens_required
@csrf_protect
def crear_ciudadano(request):
    """
    Crea un nuevo ciudadano en el sistema.

    Solo secretarias y administradores pueden crear ciudadanos.
    Incluye validaciones completas de DPI, email y teléfono.

    Args:
        request: HttpRequest object con datos del formulario

    Returns:
        HttpResponse: Render del formulario o redirección tras crear

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo secretarias y administradores pueden acceder
    """
    user = request.user

    if request.method == 'POST':
        return _procesar_creacion_ciudadano(request)

    # GET: Mostrar formulario
    context = {
        'crear_ticket_despues': request.GET.get('crear_ticket', False)
    }

    return render(request, 'ciudadano/crear_ciudadano.html', context)


def _procesar_creacion_ciudadano(request):
    """
    Procesa la creación de un nuevo ciudadano.

    Args:
        request: HttpRequest object con datos del formulario

    Returns:
        HttpResponse: Redirección o render con errores
    """
    # Obtener datos del formulario
    dpi = request.POST.get('dpi', '').strip()
    nombre_completo = request.POST.get('nombre_completo', '').strip()
    direccion = request.POST.get('direccion', '').strip()
    telefono = request.POST.get('telefono', '').strip()
    email = request.POST.get('email', '').strip()
    fecha_nacimiento = request.POST.get('fecha_nacimiento', '').strip()
    genero = request.POST.get('genero', '')
    crear_ticket_despues = request.POST.get('crear_ticket_despues') == 'on'

    # Validaciones
    errores = _validar_datos_ciudadano(dpi, nombre_completo, telefono, email, fecha_nacimiento, genero)
    if errores:
        for error in errores:
            messages.error(request, error)
        return redirect('ciudadano:crear_ciudadano')

    try:
        # Procesar fecha de nacimiento
        fecha_nacimiento_obj = None
        if fecha_nacimiento:
            try:
                fecha_nacimiento_obj = datetime.strptime(fecha_nacimiento, '%Y-%m-%d').date()
            except ValueError:
                messages.error(request, 'Formato de fecha inválido.')
                return redirect('ciudadano:crear_ciudadano')

        # Procesar género
        genero_int = None
        if genero:
            try:
                genero_int = int(genero)
                if genero_int not in [1, 2]:
                    genero_int = None
            except ValueError:
                genero_int = None

        # Crear el ciudadano
        ciudadano = Ciudadano.objects.create(
            dpi=dpi,
            nombre_completo=nombre_completo,
            direccion=direccion,
            telefono=telefono,
            email=email,
            fecha_nacimiento=fecha_nacimiento_obj,
            genero=genero_int
        )

        messages.success(request, f'Ciudadano "{ciudadano.nombre_completo}" creado exitosamente.')

        # Redirigir según la opción seleccionada
        if crear_ticket_despues:
            return redirect(f'/tickets/crear/?ciudadano_id={ciudadano.id}')
        else:
            return redirect('ciudadano:detalle_ciudadano', ciudadano_id=ciudadano.id)

    except Exception as e:
        messages.error(request, f'Error al crear el ciudadano: {str(e)}')
        return redirect('ciudadano:crear_ciudadano')


def _validar_datos_ciudadano(dpi, nombre_completo, telefono, email, fecha_nacimiento, genero):
    """
    Valida los datos del formulario de ciudadano.

    Args:
        dpi: DPI del ciudadano
        nombre_completo: Nombre completo
        telefono: Teléfono (opcional)
        email: Email (opcional)
        fecha_nacimiento: Fecha de nacimiento (opcional)
        genero: Género (opcional)

    Returns:
        list: Lista de errores encontrados
    """
    errores = []

    # Validar DPI
    if not dpi:
        errores.append('El DPI es obligatorio.')
    elif len(dpi) != 13 or not dpi.isdigit():
        errores.append('El DPI debe tener exactamente 13 dígitos numéricos.')
    elif Ciudadano.objects.filter(dpi=dpi, is_active=True).exists():
        errores.append('Ya existe un ciudadano con este DPI.')

    # Validar nombre
    if not nombre_completo:
        errores.append('El nombre completo es obligatorio.')
    elif len(nombre_completo) < 3:
        errores.append('El nombre debe tener al menos 3 caracteres.')

    # Validar teléfono (opcional)
    if telefono and (len(telefono) < 8 or len(telefono) > 15):
        errores.append('El teléfono debe tener entre 8 y 15 caracteres.')

    # Validar email (opcional)
    if email:
        try:
            validate_email(email)
        except ValidationError:
            errores.append('El formato del email no es válido.')

    return errores

@can_manage_citizens_required
def detalle_ciudadano(request, ciudadano_id):
    """
    Muestra el detalle completo de un ciudadano y sus tickets asociados.

    Args:
        request: HttpRequest object
        ciudadano_id: ID del ciudadano a mostrar

    Returns:
        HttpResponse: Render del template con detalles del ciudadano

    Security:
        - Requiere autenticación (@login_required)
        - Solo secretarias y administradores pueden acceder
    """
    user = request.user

    # Obtener ciudadano con datos relacionados optimizados
    ciudadano = get_object_or_404(
        Ciudadano.objects.prefetch_related(
            'tickets__ticket'
        ),
        id=ciudadano_id,
        is_active=True
    )

    # Obtener todos los tickets del ciudadano filtrados por permisos
    todos_tickets_ciudadano = CiudadanoTicket.objects.filter(
        ciudadano=ciudadano,
        is_active=True
    ).select_related('ticket', 'ticket__grupo')

    # Filtrar tickets según permisos del usuario
    tickets_permitidos = []
    for ciudadano_ticket in todos_tickets_ciudadano:
        ticket = ciudadano_ticket.ticket
        if _puede_ver_ticket_ciudadano(user, ticket):
            tickets_permitidos.append(ciudadano_ticket)

    # Obtener solo los primeros 10 para mostrar
    tickets_ciudadano = tickets_permitidos[:10]

    # Estadísticas del ciudadano (solo tickets que el usuario puede ver)
    tickets_cerrados_permitidos = [ct for ct in tickets_permitidos if ct.ticket.estado == 3]
    tickets_activos_permitidos = [ct for ct in tickets_permitidos if ct.ticket.estado != 3]

    stats = {
        'total_tickets': len(tickets_permitidos),
        'tickets_activos': len(tickets_activos_permitidos),
        'tickets_cerrados': len(tickets_cerrados_permitidos),
        'edad': ciudadano.get_edad()
    }

    context = {
        'ciudadano': ciudadano,
        'tickets_ciudadano': tickets_ciudadano,
        'stats': stats,
        'puede_editar': _puede_gestionar_ciudadanos(user),
        'puede_crear_ticket': _puede_crear_tickets(user)
    }

    return render(request, 'ciudadano/detalle_ciudadano.html', context)

@can_manage_citizens_required
@csrf_protect
def editar_ciudadano(request, ciudadano_id):
    """
    Edita un ciudadano existente.

    Solo secretarias y administradores pueden editar ciudadanos.

    Args:
        request: HttpRequest object con datos del formulario
        ciudadano_id: ID del ciudadano a editar

    Returns:
        HttpResponse: Render del formulario o redirección tras editar

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo secretarias y administradores pueden acceder
    """
    user = request.user

    # Verificar permisos
    if not _puede_gestionar_ciudadanos(user):
        messages.error(request, 'No tienes permisos para editar ciudadanos.')
        return redirect('ciudadano:lista_ciudadanos')

    # Obtener ciudadano
    ciudadano = get_object_or_404(Ciudadano, id=ciudadano_id, is_active=True)

    if request.method == 'POST':
        return _procesar_edicion_ciudadano(request, ciudadano)

    # GET: Mostrar formulario con datos actuales
    context = {
        'ciudadano': ciudadano,
        'editando': True
    }

    return render(request, 'ciudadano/editar_ciudadano.html', context)


def _procesar_edicion_ciudadano(request, ciudadano):
    """
    Procesa la edición de un ciudadano existente.

    Args:
        request: HttpRequest object con datos del formulario
        ciudadano: Instancia del ciudadano a editar

    Returns:
        HttpResponse: Redirección o render con errores
    """
    # Obtener datos del formulario
    dpi = request.POST.get('dpi', '').strip()
    nombre_completo = request.POST.get('nombre_completo', '').strip()
    direccion = request.POST.get('direccion', '').strip()
    telefono = request.POST.get('telefono', '').strip()
    email = request.POST.get('email', '').strip()
    fecha_nacimiento = request.POST.get('fecha_nacimiento', '').strip()
    genero = request.POST.get('genero', '')

    # Validaciones (excluyendo DPI si no cambió)
    errores = []

    # Validar DPI solo si cambió
    if dpi != ciudadano.dpi:
        if not dpi:
            errores.append('El DPI es obligatorio.')
        elif len(dpi) != 13 or not dpi.isdigit():
            errores.append('El DPI debe tener exactamente 13 dígitos numéricos.')
        elif Ciudadano.objects.filter(dpi=dpi, is_active=True).exclude(id=ciudadano.id).exists():
            errores.append('Ya existe otro ciudadano con este DPI.')

    # Validar otros campos
    if not nombre_completo:
        errores.append('El nombre completo es obligatorio.')
    elif len(nombre_completo) < 3:
        errores.append('El nombre debe tener al menos 3 caracteres.')

    if telefono and (len(telefono) < 8 or len(telefono) > 15):
        errores.append('El teléfono debe tener entre 8 y 15 caracteres.')

    if email:
        try:
            validate_email(email)
        except ValidationError:
            errores.append('El formato del email no es válido.')

    if errores:
        for error in errores:
            messages.error(request, error)
        return redirect('ciudadano:editar_ciudadano', ciudadano_id=ciudadano.id)

    try:
        # Procesar fecha de nacimiento
        fecha_nacimiento_obj = None
        if fecha_nacimiento:
            try:
                fecha_nacimiento_obj = datetime.strptime(fecha_nacimiento, '%Y-%m-%d').date()
            except ValueError:
                messages.error(request, 'Formato de fecha inválido.')
                return redirect('ciudadano:editar_ciudadano', ciudadano_id=ciudadano.id)

        # Procesar género
        genero_int = None
        if genero:
            try:
                genero_int = int(genero)
                if genero_int not in [1, 2]:
                    genero_int = None
            except ValueError:
                genero_int = None

        # Actualizar el ciudadano
        ciudadano.dpi = dpi
        ciudadano.nombre_completo = nombre_completo
        ciudadano.direccion = direccion
        ciudadano.telefono = telefono
        ciudadano.email = email
        ciudadano.fecha_nacimiento = fecha_nacimiento_obj
        ciudadano.genero = genero_int
        ciudadano.save()

        messages.success(request, f'Ciudadano "{ciudadano.nombre_completo}" actualizado exitosamente.')
        return redirect('ciudadano:detalle_ciudadano', ciudadano_id=ciudadano.id)

    except Exception as e:
        messages.error(request, f'Error al actualizar el ciudadano: {str(e)}')
        return redirect('ciudadano:editar_ciudadano', ciudadano_id=ciudadano.id)

def tickets_ciudadano(request, ciudadano_id):
    return HttpResponse(f"Tickets del ciudadano {ciudadano_id} - En desarrollo")

def historial_ciudadano(request, ciudadano_id):
    return HttpResponse(f"Historial del ciudadano {ciudadano_id} - En desarrollo")

def buscar_por_dpi_ajax(request):
    return HttpResponse("Buscar por DPI AJAX - En desarrollo")

def validar_dpi_ajax(request):
    return HttpResponse("Validar DPI AJAX - En desarrollo")

def autocompletar_ciudadano(request):
    return HttpResponse("Autocompletar ciudadano - En desarrollo")
