"""
Base/captcha.py
Sistema de captcha alternativo simple para Django.

Implementa un captcha matemático básico como medida de seguridad
gratuita contra bots y ataques automatizados.
"""

import random
import hashlib
from django import forms
from django.core.cache import cache
from django.utils.translation import gettext_lazy as _


class SimpleMathCaptcha:
    """
    Generador de captcha matemático simple.
    Crea operaciones básicas de suma y resta.
    """
    
    @staticmethod
    def generate_captcha():
        """
        Genera una operación matemática simple y su respuesta.
        
        Returns:
            tuple: (pregunta_str, respuesta_int, token_str)
        """
        # Generar números aleatorios
        num1 = random.randint(1, 20)
        num2 = random.randint(1, 20)
        
        # Elegir operación aleatoria
        operations = ['+', '-']
        operation = random.choice(operations)
        
        if operation == '+':
            question = f"{num1} + {num2}"
            answer = num1 + num2
        else:
            # As<PERSON><PERSON>r que el resultado no sea negativo
            if num1 < num2:
                num1, num2 = num2, num1
            question = f"{num1} - {num2}"
            answer = num1 - num2
        
        # Generar token único para esta pregunta
        token = hashlib.md5(f"{question}_{answer}_{random.randint(1000, 9999)}".encode()).hexdigest()
        
        # Guardar en cache por 10 minutos
        cache.set(f"captcha_{token}", answer, 600)
        
        return question, answer, token
    
    @staticmethod
    def verify_captcha(token, user_answer):
        """
        Verifica si la respuesta del usuario es correcta.
        
        Args:
            token (str): Token único del captcha
            user_answer (str): Respuesta proporcionada por el usuario
            
        Returns:
            bool: True si la respuesta es correcta, False en caso contrario
        """
        try:
            # Obtener respuesta correcta del cache
            correct_answer = cache.get(f"captcha_{token}")
            
            if correct_answer is None:
                return False  # Token expirado o inválido
            
            # Convertir respuesta del usuario a entero
            user_answer_int = int(user_answer)
            
            # Verificar respuesta
            is_correct = user_answer_int == correct_answer
            
            # Eliminar token del cache después de usar (una sola vez)
            cache.delete(f"captcha_{token}")
            
            return is_correct
            
        except (ValueError, TypeError):
            return False


class CaptchaField(forms.CharField):
    """
    Campo de formulario personalizado para captcha matemático.
    """
    
    def __init__(self, *args, **kwargs):
        # Generar captcha
        self.question, self.answer, self.token = SimpleMathCaptcha.generate_captcha()
        
        # Configurar el campo
        kwargs.update({
            'label': f'Resuelve: {self.question}',
            'help_text': 'Resuelve la operación matemática para continuar',
            'widget': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ingresa tu respuesta',
                'autocomplete': 'off',
            }),
            'required': True,
        })
        
        super().__init__(*args, **kwargs)
    
    def clean(self, value):
        """
        Valida la respuesta del captcha.
        
        Args:
            value: Valor ingresado por el usuario
            
        Returns:
            str: Valor limpio si es válido
            
        Raises:
            forms.ValidationError: Si la respuesta es incorrecta
        """
        value = super().clean(value)
        
        if not value:
            raise forms.ValidationError(_('Este campo es obligatorio.'))
        
        # Verificar captcha
        if not SimpleMathCaptcha.verify_captcha(self.token, value):
            raise forms.ValidationError(
                _('Respuesta incorrecta. Por favor, resuelve la operación matemática.')
            )
        
        return value


class CaptchaWidget(forms.Widget):
    """
    Widget personalizado para mostrar el captcha con mejor presentación.
    """
    
    def __init__(self, attrs=None):
        super().__init__(attrs)
        self.question, self.answer, self.token = SimpleMathCaptcha.generate_captcha()
    
    def render(self, name, value, attrs=None, renderer=None):
        """
        Renderiza el widget del captcha.
        
        Args:
            name: Nombre del campo
            value: Valor actual
            attrs: Atributos HTML adicionales
            renderer: Renderizador de templates
            
        Returns:
            str: HTML del widget
        """
        if attrs is None:
            attrs = {}
        
        attrs.update({
            'class': 'form-control',
            'placeholder': 'Ingresa tu respuesta',
            'autocomplete': 'off',
        })
        
        html = f'''
        <div class="captcha-container">
            <div class="captcha-question mb-2">
                <strong>Resuelve: {self.question} = ?</strong>
            </div>
            <input type="text" name="{name}" {self._format_attrs(attrs)} />
            <input type="hidden" name="{name}_token" value="{self.token}" />
            <small class="form-text text-muted">
                Resuelve la operación matemática para continuar
            </small>
        </div>
        '''
        
        return html
    
    def _format_attrs(self, attrs):
        """
        Formatea los atributos HTML.
        
        Args:
            attrs: Diccionario de atributos
            
        Returns:
            str: Atributos formateados
        """
        return ' '.join([f'{k}="{v}"' for k, v in attrs.items()])


def add_captcha_to_form(form_class):
    """
    Decorador para añadir captcha a cualquier formulario.
    
    Args:
        form_class: Clase del formulario a decorar
        
    Returns:
        class: Formulario con captcha añadido
    """
    # Añadir campo captcha al formulario
    form_class.captcha = CaptchaField()
    
    # Sobrescribir método clean si existe
    original_clean = getattr(form_class, 'clean', None)
    
    def clean_with_captcha(self):
        cleaned_data = super(form_class, self).clean() if original_clean else self.cleaned_data
        
        # El captcha ya se valida en su propio método clean
        # Aquí solo nos aseguramos de que se ejecute
        
        return cleaned_data
    
    form_class.clean = clean_with_captcha
    
    return form_class
