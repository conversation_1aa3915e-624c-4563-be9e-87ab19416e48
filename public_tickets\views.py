"""
public_tickets/views.py
Vistas públicas para consulta de tickets sin autenticación.

Permite a los ciudadanos consultar el estado de sus tickets
usando únicamente el token de seguridad.
"""

from django.shortcuts import render, get_object_or_404
from django.http import Http404
from django.views.decorators.cache import cache_page
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
from tickets.models import Ticket
import logging

# Configurar logger para auditoría
logger = logging.getLogger('public_tickets')


@require_http_methods(["GET"])
# Removido el cache para que siempre muestre el estado actual
def consultar_ticket(request, token):
    """
    Vista pública para consultar un ticket por su token.

    Esta vista es accesible sin autenticación y muestra información
    básica del ticket para ciudadanos.

    Args:
        request: HttpRequest object
        token: Token único del ticket

    Returns:
        HttpResponse: Template con información del ticket
        Http404: Si el token no existe o el ticket no está activo

    Security:
        - Solo filtra por token (nunca por ID)
        - Muestra información limitada
        - Registra accesos para auditoría
        - Rate limiting aplicado por middleware
    """
    try:
        # Buscar ticket SOLO por token (nunca por ID)
        ticket = get_object_or_404(
            Ticket.objects.select_related(
                'creado_por', 'grupo'
            ).prefetch_related(
                'ciudadanos__ciudadano',
                'imagenes'
            ),
            token=token,
            is_active=True
        )

        # Obtener ciudadano asociado
        ciudadano = ticket.get_ciudadano()

        # Obtener imágenes activas
        imagenes = ticket.imagenes.filter(is_active=True).order_by('orden', 'fecha_subida')

        # Registrar acceso para auditoría
        logger.info(f"Acceso público a ticket {ticket.id} con token {token[:8]}... desde IP {_get_client_ip(request)}")

        # Contexto con información limitada
        context = {
            'ticket': ticket,
            'ciudadano': ciudadano,
            'imagenes': imagenes,
            'estado_display': ticket.get_estado_display(),
            'prioridad_display': ticket.get_prioridad_display(),
            'estado_color': ticket.get_estado_display_color(),
            'prioridad_color': ticket.get_prioridad_display_color(),
        }

        return render(request, 'public_tickets/consultar_ticket.html', context)

    except Exception as e:
        # Log del error sin exponer información sensible
        logger.warning(f"Intento de acceso con token inválido: {token[:8]}... desde IP {_get_client_ip(request)}")
        raise Http404("Ticket no encontrado")


def _get_client_ip(request):
    """
    Obtiene la IP real del cliente considerando proxies.

    Args:
        request: HttpRequest object

    Returns:
        str: Dirección IP del cliente
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@require_http_methods(["GET"])
def ticket_no_encontrado(request):
    """
    Vista para mostrar cuando un ticket no se encuentra.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Template de error 404 personalizado
    """
    return render(request, 'public_tickets/ticket_no_encontrado.html', status=404)
