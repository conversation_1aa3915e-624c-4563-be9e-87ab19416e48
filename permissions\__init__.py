"""
permissions/
Sistema centralizado de permisos para el sistema de tickets municipal.

Este módulo centraliza toda la lógica de permisos, eliminando duplicaciones
y conflictos entre diferentes partes del sistema.

Módulos:
- core.py: PermissionHelper centralizado con toda la lógica de roles
- decorators.py: Decoradores de permisos para vistas
- context.py: Context processor único para templates
- utils.py: Utilidades auxiliares para permisos
"""

# Importar las clases principales para facilitar el uso
from .core import PermissionHelper
from .context import add_permission_context
from .decorators import (
    admin_required,
    secretaria_or_admin_required,
    supervisor_or_admin_required,
    can_create_tickets_required,
    can_manage_users_required,
    can_assign_tickets_required,
    ticket_access_required
)

__all__ = [
    'PermissionHelper',
    'add_permission_context',
    'admin_required',
    'secretaria_or_admin_required',
    'supervisor_or_admin_required',
    'can_create_tickets_required',
    'can_manage_users_required',
    'can_assign_tickets_required',
    'ticket_access_required',
]
