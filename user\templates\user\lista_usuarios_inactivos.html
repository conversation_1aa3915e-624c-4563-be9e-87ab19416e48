{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Usuarios Inactivos{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con estadísticas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user-slash"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Usuarios Inactivos</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-users me-1"></i>{{ total_usuarios }} usuario{{ total_usuarios|pluralize }} inactivo{{ total_usuarios|pluralize }}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-success">
                                <i class="fas fa-users me-2"></i>Ver Activos
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros y búsqueda -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="q" class="form-label">Buscar Usuario</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="q" name="q" 
                                       value="{{ busqueda }}" placeholder="Nombre, usuario o DPI...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="cargo" class="form-label">Filtrar por Cargo</label>
                            <select class="form-select" id="cargo" name="cargo">
                                <option value="">Todos los cargos</option>
                                {% for cargo in cargos %}
                                    <option value="{{ cargo.id }}" 
                                            {% if cargo.id|stringformat:"s" == cargo_seleccionado %}selected{% endif %}>
                                        {{ cargo.nombre }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>Filtrar
                            </button>
                            <a href="{% url 'user:lista_usuarios_inactivos' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Limpiar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de usuarios inactivos -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-user-slash me-2"></i>Lista de Usuarios Inactivos
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if usuarios %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th><i class="fas fa-user me-1"></i>Usuario</th>
                                        <th><i class="fas fa-id-card me-1"></i>DPI</th>
                                        <th><i class="fas fa-briefcase me-1"></i>Cargo</th>
                                        <th><i class="fas fa-phone me-1"></i>Teléfonos</th>
                                        <th><i class="fas fa-users me-1"></i>Familiares</th>
                                        <th width="150"><i class="fas fa-cogs me-1"></i>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="usuarios-container">
                                    {% include 'user/parciales/lista_usuarios_inactivos.html' %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Loading indicator -->
                        <div id="loading-indicator" class="text-center py-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2 text-muted">Cargando más usuarios...</p>
                        </div>

                        <!-- Paginación -->
                        {% if usuarios.has_next %}
                            <div class="card-footer bg-white border-top">
                                <div class="text-center">
                                    <button id="load-more" class="btn btn-outline-primary" data-page="{{ usuarios.next_page_number }}">
                                        <i class="fas fa-chevron-down me-2"></i>Cargar más usuarios
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-user-check fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">¡Excelente!</h5>
                            <p class="text-muted">No hay usuarios inactivos en el sistema</p>
                            <a href="{% url 'user:lista_usuarios' %}" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>Ver Usuarios Activos
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #dc3545, #c82333);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.table tbody tr {
    transition: background-color 0.1s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.badge {
    font-size: 0.75rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

#loading-indicator {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.getElementById('load-more');
    const loadingIndicator = document.getElementById('loading-indicator');
    const usuariosContainer = document.getElementById('usuarios-container');
    
    // Scroll infinito optimizado
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const page = this.dataset.page;
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            
            // Mostrar loading
            loadingIndicator.style.display = 'block';
            this.style.display = 'none';
            
            fetch(url.toString(), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Agregar nuevos usuarios
                usuariosContainer.insertAdjacentHTML('beforeend', data.html);
                
                // Ocultar loading
                loadingIndicator.style.display = 'none';
                
                // Actualizar botón o ocultarlo
                if (data.has_next) {
                    this.dataset.page = parseInt(page) + 1;
                    this.style.display = 'block';
                } else {
                    this.parentElement.parentElement.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.style.display = 'none';
                this.style.display = 'block';
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudieron cargar más usuarios'
                });
            });
        });
    }
});

// Función para activar usuario
function activarUsuario(userId, nombre) {
    Swal.fire({
        title: '¿Activar usuario?',
        text: `Se activará a ${nombre} y podrá acceder al sistema`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, activar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/usuarios/usuarios/${userId}/activar/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remover fila de la tabla
                    document.getElementById(`usuario-${userId}`).remove();
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'Usuario activado',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    // Recargar si no quedan usuarios
                    if (document.querySelectorAll('#usuarios-container tr').length === 0) {
                        setTimeout(() => location.reload(), 2000);
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}
</script>

{% csrf_token %}
{% endblock %}
