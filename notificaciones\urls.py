"""
notificaciones/urls.py
URLs para el sistema de notificaciones manuales.

Incluye rutas para:
- Listado y gestión de notificaciones
- Creación de notificaciones (usuarios, grupos, masivas)
- Visualización de notificaciones recibidas
- Acciones sobre notificaciones (marcar como leídas)
- AJAX para actualizaciones en tiempo real
"""

from django.urls import path
from . import views

app_name = 'notificaciones'

urlpatterns = [
    # ========================================================================
    # GESTIÓN DE NOTIFICACIONES (Para administradores y usuarios con permisos)
    # ========================================================================

    # Listado y gestión de notificaciones creadas
    path('', views.lista_notificaciones, name='lista_notificaciones'),
    path('<int:pk>/detalle/', views.detalle_notificacion, name='detalle_notificacion'),

    # Crear notificaciones
    path('crear/usuario/', views.crear_notificacion_usuario, name='crear_notificacion_usuario'),
    path('crear/grupo/', views.crear_notificacion_grupo, name='crear_notificacion_grupo'),
    path('crear/masiva/', views.crear_notificacion_masiva, name='crear_notificacion_masiva'),

    # ========================================================================
    # NOTIFICACIONES RECIBIDAS (Para todos los usuarios)
    # ========================================================================

    # Mis notificaciones recibidas
    path('mis-notificaciones/', views.mis_notificaciones, name='mis_notificaciones'),

    # Acciones sobre notificaciones recibidas
    path('<int:notificacion_id>/marcar-leida/', views.marcar_leida, name='marcar_leida'),
    path('marcar-todas-leidas/', views.marcar_todas_leidas, name='marcar_todas_leidas'),

    # ========================================================================
    # ENDPOINTS AJAX
    # ========================================================================

    # Funcionalidad en tiempo real
    path('ajax/contar-no-leidas/', views.contar_no_leidas_ajax, name='contar_no_leidas'),
    path('ajax/obtener-recientes/', views.obtener_recientes_ajax, name='obtener_recientes'),
    path('ajax/marcar-leida/', views.marcar_leida_ajax, name='marcar_leida_ajax'),
    path('ajax/estadisticas/', views.estadisticas_notificaciones_ajax, name='estadisticas_ajax'),
    path('ajax/obtener-usuarios/', views.obtener_usuarios_ajax, name='obtener_usuarios'),
    path('ajax/obtener-grupos/', views.obtener_grupos_ajax, name='obtener_grupos'),
    path('ajax/estadisticas-sistema/', views.estadisticas_sistema_ajax, name='estadisticas_sistema'),

    # ========================================================================
    # ALIAS PARA COMPATIBILIDAD
    # ========================================================================

    # Mantener compatibilidad con URLs existentes
    path('no-leidas/', views.notificaciones_no_leidas, name='no_leidas'),
    path('crear/', views.crear_notificacion, name='crear_notificacion'),
    path('enviar-masiva/', views.enviar_notificacion_masiva, name='enviar_masiva'),

    # Debug (temporal)
    path('debug-permisos/', views.debug_permisos, name='debug_permisos'),
]
