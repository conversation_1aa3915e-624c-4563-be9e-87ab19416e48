{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Tickets - Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --tertiary-color: #3F51B5;
        }

        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: var(--primary-color);
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus {
            border-color: var(--tertiary-color);
            box-shadow: 0 0 0 0.2rem rgba(63, 81, 181, 0.25);
        }

        .captcha-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 2px solid #dee2e6;
        }

        .captcha-question {
            font-size: 1.1em;
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 10px;
        }

        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container d-flex justify-content-center align-items-center vh-100">
        <div class="card login-card" style="width: 28rem;">
            <div class="card-body text-center p-4">
                <img src="{% static 'img/logo.png' %}" alt="Logo" class="mb-4" style="width: 18em; max-width: 100%;">
                <h4 class="card-title mb-2" style="color: var(--primary-color);">
                    <i class="fas fa-ticket-alt me-2"></i>Sistema de Tickets
                </h4>
                <p class="card-text text-muted mb-4">Por favor, inicia sesión para continuar</p>

                <!-- Mostrar mensajes -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Aviso de rate limiting -->
                {% if was_limited %}
                    <div class="security-notice">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Medida de seguridad activada:</strong> Se han detectado múltiples intentos de acceso.
                    </div>
                {% endif %}

                <form method="post" class="text-start">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2"></i>Usuario
                        </label>
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="Ingresa tu usuario" required autocomplete="username">
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>Contraseña
                        </label>
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="Ingresa tu contraseña" required autocomplete="current-password">
                    </div>

                    <!-- Captcha si es necesario -->
                    {% if needs_captcha and captcha_data %}
                        <div class="captcha-container">
                            <div class="captcha-question">
                                <i class="fas fa-calculator me-2"></i>
                                <strong>Resuelve: {{ captcha_data.question }} = ?</strong>
                            </div>
                            <input type="text" class="form-control" name="captcha"
                                   placeholder="Ingresa tu respuesta" required autocomplete="off"
                                   pattern="[0-9\-]+" title="Solo números y signo menos">
                            <input type="hidden" name="captcha_token" value="{{ captcha_data.token }}">
                            <small class="text-muted d-block text-center mt-2">
                                <i class="fas fa-shield-alt me-1"></i>
                                Medida de seguridad: resuelve la operación matemática
                            </small>
                        </div>
                    {% elif needs_captcha %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error generando captcha. Por favor, recarga la página.
                        </div>
                    {% endif %}

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                    </button>
                </form>

                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Conexión segura y protegida
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>