{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Crear Notificación para Grupos{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users text-success"></i>
                        Crear Notificación para Grupos
                    </h2>
                    <p class="text-muted mb-0">Envíe notificaciones a grupos completos del sistema</p>
                </div>
                <div>
                    <a href="{% url 'notificaciones:lista_notificaciones' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/inicio/">
                    <i class="fas fa-home"></i> Inicio
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'notificaciones:lista_notificaciones' %}">Notificaciones</a>
            </li>
            <li class="breadcrumb-item active">Crear para Grupos</li>
        </ol>
    </nav>

    <!-- Formulario -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Formulario de Notificación para Grupos
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <!-- Input hidden para almacenar IDs seleccionados -->
                        <input type="hidden" id="grupos-selected" name="grupos_selected" value="">
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Ventajas de Notificaciones por Grupo
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Envío eficiente a múltiples usuarios
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Gestión centralizada por roles
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Notificaciones individuales automáticas
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            Seguimiento por grupo e individual
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs"></i> Configuración Avanzada
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-toggle-on text-info"></i>
                            <strong>Crear individuales:</strong> Genera notificaciones personales
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-info"></i>
                            <strong>Solo usuarios activos:</strong> Filtra automáticamente
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-chart-bar text-info"></i>
                            <strong>Estadísticas:</strong> Seguimiento de lectura por usuario
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-history text-info"></i>
                            <strong>Historial:</strong> Registro completo de envíos
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Grupos disponibles -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-layer-group"></i> Grupos Disponibles en el Sistema
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row" id="grupos-info">
                        <!-- Se llenará dinámicamente con JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Proceso de envío -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-route"></i> Proceso de Envío
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <div class="p-3 border rounded bg-light">
                                <i class="fas fa-edit fa-2x text-primary mb-2"></i>
                                <h6>1. Crear</h6>
                                <small class="text-muted">Se crea la notificación base</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="p-3 border rounded bg-light">
                                <i class="fas fa-paper-plane fa-2x text-success mb-2"></i>
                                <h6>2. Enviar a Grupo</h6>
                                <small class="text-muted">Se asocia con el grupo seleccionado</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="p-3 border rounded bg-light">
                                <i class="fas fa-user-friends fa-2x text-info mb-2"></i>
                                <h6>3. Expandir</h6>
                                <small class="text-muted">Se crean notificaciones individuales</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="p-3 border rounded bg-light">
                                <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                                <h6>4. Notificar</h6>
                                <small class="text-muted">Los usuarios reciben la notificación</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación del formulario
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Contador de caracteres para el mensaje
    const mensajeField = document.querySelector('textarea[name="mensaje"]');
    if (mensajeField) {
        const maxLength = 500;
        const counter = document.createElement('small');
        counter.className = 'form-text text-muted';
        counter.id = 'mensaje-counter';
        mensajeField.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - mensajeField.value.length;
            counter.textContent = `${mensajeField.value.length}/${maxLength} caracteres`;
            
            if (remaining < 50) {
                counter.className = 'form-text text-warning';
            } else if (remaining < 0) {
                counter.className = 'form-text text-danger';
            } else {
                counter.className = 'form-text text-muted';
            }
        }

        mensajeField.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Mostrar información de grupos seleccionados
    const gruposCheckboxes = document.querySelectorAll('input[name="grupos"]');
    const selectedCount = document.createElement('div');
    selectedCount.className = 'alert alert-info mt-2';
    selectedCount.style.display = 'none';
    
    if (gruposCheckboxes.length > 0) {
        gruposCheckboxes[0].closest('.form-group').appendChild(selectedCount);
        
        gruposCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selected = document.querySelectorAll('input[name="grupos"]:checked');
                
                if (selected.length > 0) {
                    let totalUsuarios = 0;
                    let gruposNombres = [];
                    
                    selected.forEach(cb => {
                        const label = cb.nextElementSibling.textContent;
                        gruposNombres.push(label.split(' (')[0]);
                        
                        // Extraer número de miembros del label
                        const match = label.match(/\((\d+) miembros/);
                        if (match) {
                            totalUsuarios += parseInt(match[1]);
                        }
                    });
                    
                    selectedCount.style.display = 'block';
                    selectedCount.innerHTML = `
                        <i class="fas fa-layer-group"></i> 
                        ${selected.length} grupo(s) seleccionado(s): <strong>${gruposNombres.join(', ')}</strong><br>
                        <small>Aproximadamente ${totalUsuarios} usuario(s) recibirán la notificación</small>
                    `;
                } else {
                    selectedCount.style.display = 'none';
                }
            });
        });
    }

    // Cargar información de grupos
    loadGruposInfo();
});

function loadGruposInfo() {
    // Simular carga de información de grupos
    const gruposInfo = document.getElementById('grupos-info');
    if (gruposInfo) {
        gruposInfo.innerHTML = `
            <div class="col-md-3 mb-3">
                <div class="card border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                        <h6>Admin</h6>
                        <small class="text-muted">Administradores del sistema</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-user-tie fa-2x text-success mb-2"></i>
                        <h6>Secretaria</h6>
                        <small class="text-muted">Personal de secretaría</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-user-cog fa-2x text-info mb-2"></i>
                        <h6>Empleado</h6>
                        <small class="text-muted">Empleados operativos</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-users-cog fa-2x text-warning mb-2"></i>
                        <h6>Supervisores</h6>
                        <small class="text-muted">Personal supervisor</small>
                    </div>
                </div>
            </div>
        `;
    }
}
</script>

<!-- Modal para seleccionar grupos -->
<div class="modal fade" id="gruposModal" tabindex="-1" aria-labelledby="gruposModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="gruposModalLabel">
                    <i class="fas fa-layer-group"></i> Seleccionar Grupos
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Buscador -->
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control search-input" placeholder="Buscar grupos por nombre...">
                    </div>
                </div>

                <!-- Controles -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-success select-all-filtered">
                            <i class="fas fa-check-double"></i> Seleccionar todos los filtrados
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary clear-selection">
                            <i class="fas fa-times"></i> Limpiar selección
                        </button>
                    </div>
                    <div>
                        <span class="badge bg-success">
                            <span class="selected-count">0</span> seleccionado(s)
                        </span>
                    </div>
                </div>

                <!-- Lista de grupos -->
                <div class="items-list border rounded" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success confirm-selection" disabled>
                    <i class="fas fa-check"></i> Confirmar Selección
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'notificaciones/js/selector-modal.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación adicional del formulario
    const form = document.querySelector('form[method="post"]');
    console.log('Formulario de grupos encontrado:', form);
    console.log('Tipo de formulario:', typeof form);
    console.log('¿Es HTMLFormElement?', form instanceof HTMLFormElement);

    let isSubmitting = false; // Bandera para evitar bucle infinito

    if (form && form instanceof HTMLFormElement) {
        form.addEventListener('submit', function(e) {
            // Si ya estamos enviando, permitir el envío normal
            if (isSubmitting) {
                console.log('Envío en progreso, permitiendo...');
                return true;
            }
            const gruposInput = document.getElementById('grupos-selected');
            const gruposValue = gruposInput ? gruposInput.value.trim() : '';

            console.log('Validando formulario de grupos...');
            console.log('Valor del input grupos-selected:', gruposValue);

            if (!gruposValue) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Grupos requeridos',
                    text: 'Debe seleccionar al menos un grupo antes de enviar la notificación.',
                    confirmButtonText: 'Entendido'
                });
                return false;
            }

            // Validar otros campos requeridos
            const titulo = form.querySelector('input[name="titulo"]');
            const mensaje = form.querySelector('textarea[name="mensaje"]');

            if (titulo && !titulo.value.trim()) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Título requerido',
                    text: 'Debe ingresar un título para la notificación.',
                    confirmButtonText: 'Entendido'
                });
                titulo.focus();
                return false;
            }

            if (mensaje && !mensaje.value.trim()) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Mensaje requerido',
                    text: 'Debe ingresar un mensaje para la notificación.',
                    confirmButtonText: 'Entendido'
                });
                mensaje.focus();
                return false;
            }

            // Mostrar confirmación antes de enviar
            e.preventDefault();
            const gruposCount = gruposValue.split(',').filter(id => id.trim()).length;

            Swal.fire({
                title: '¿Confirmar envío?',
                text: `Se enviará la notificación a ${gruposCount} grupo(s).`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Sí, enviar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Mostrar loading con timeout automático
                    const loadingAlert = Swal.fire({
                        title: 'Enviando notificación...',
                        text: 'Por favor espere...',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Cerrar el loading después de 3 segundos máximo
                    setTimeout(() => {
                        Swal.close();
                    }, 3000);

                    // Marcar que estamos enviando y enviar el formulario
                    isSubmitting = true;

                    // Enviar el formulario directamente sin interceptar
                    try {
                        console.log('Enviando formulario de grupos...');
                        // Remover temporalmente el event listener para evitar bucle
                        form.removeEventListener('submit', arguments.callee);

                        // Enviar usando el método nativo
                        if (typeof form.submit === 'function') {
                            form.submit();
                        } else {
                            // Método alternativo: crear un botón submit temporal
                            const submitBtn = document.createElement('input');
                            submitBtn.type = 'submit';
                            submitBtn.style.display = 'none';
                            form.appendChild(submitBtn);
                            submitBtn.click();
                            form.removeChild(submitBtn);
                        }
                    } catch (error) {
                        console.error('Error enviando formulario:', error);
                        isSubmitting = false;
                        Swal.close();
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Hubo un problema al enviar el formulario. Por favor, intente nuevamente.'
                        });
                    }
                }
            });
        });
    }
});
</script>
{% endblock %}
