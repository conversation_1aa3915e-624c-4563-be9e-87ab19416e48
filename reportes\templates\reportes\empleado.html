{% extends 'reportes/base_reportes.html' %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Por Empleado</li>
{% endblock %}

{% block page_title %}Reportes por Empleado{% endblock %}
{% block page_description %}Genere reportes detallados de tickets por empleado{% endblock %}

{% block report_content %}
<div class="row">
    <div class="col-12">
        <form class="report-form" action="{% url 'reportes:generar_reporte_empleado' %}" method="post">
            {% csrf_token %}
            
            <!-- Selección de Empleados -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-user-tie me-2"></i>Selección de Empleados
                </h5>
                
                <div class="row">
                    <div class="col-12">
                        <label for="empleados" class="form-label">Empleados *</label>
                        <select name="empleados" id="empleados" class="form-select select2" multiple required>
                            {% for empleado in empleados %}
                            <option value="{{ empleado.id }}" 
                                    data-cargo="{{ empleado.cargo.nombre|default:'Sin cargo' }}"
                                    data-areas="{% for grupo in empleado.groups.all %}{% if grupo.name in user_permissions.areas or grupo.name in 'Administración,Secretaría,Fontanería,Electricidad,Mantenimiento,Limpieza,Jardinería,Seguridad,Obras Públicas,Servicios Generales' %}{{ grupo.name }}{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %}">
                                {{ empleado.get_full_name|default:empleado.username }}
                                {% if empleado.cargo %} - {{ empleado.cargo.nombre }}{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            Puede seleccionar múltiples empleados. Use el buscador para encontrar empleados específicos.
                        </div>
                    </div>
                </div>
                
                <!-- Acciones rápidas -->
                <div class="row mt-3">
                    <div class="col-12">
                        <label class="form-label">Acciones Rápidas</label>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="seleccionarTodos()">
                                <i class="fas fa-check-all me-1"></i>Seleccionar Todos
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="limpiarSeleccion()">
                                <i class="fas fa-times me-1"></i>Limpiar Selección
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filtros de Fecha -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-calendar-alt me-2"></i>Filtros de Fecha
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <label for="fecha_inicio" class="form-label">Fecha de Inicio</label>
                        <input type="date" name="fecha_inicio" id="fecha_inicio" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir desde el inicio.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="fecha_fin" class="form-label">Fecha de Fin</label>
                        <input type="date" name="fecha_fin" id="fecha_fin" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir hasta la fecha actual.</div>
                    </div>
                </div>
            </div>
            
            <!-- Formato del Reporte -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-export me-2"></i>Formato del Reporte
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_pdf" value="pdf" checked>
                            <label class="form-check-label" for="formato_pdf">
                                <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                            </label>
                        </div>
                        <div class="form-text">Formato ideal para visualización e impresión.</div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_excel" value="excel">
                            <label class="form-check-label" for="formato_excel">
                                <i class="fas fa-file-excel text-success me-2"></i>Excel
                            </label>
                        </div>
                        <div class="form-text">Formato ideal para análisis de datos.</div>
                    </div>
                </div>
            </div>
            
            <!-- Botones de Acción -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-generate me-3">
                    <i class="fas fa-download me-2"></i>Generar Reporte
                </button>
                <a href="{% url 'reportes:index' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Información sobre el reporte -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Información del Reporte por Empleado</h6>
            <p class="mb-2"><strong>Contenido del reporte:</strong></p>
            <ul class="mb-0">
                <li>Información básica del empleado (nombre, cargo, áreas)</li>
                <li>Tabla de resumen con conteo de tickets por estado</li>
                <li>Listado detallado de tickets organizados por estado</li>
                <li>Si selecciona múltiples empleados, cada uno tendrá su propia sección</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_report_js %}
<script>
$(document).ready(function() {
    // Configurar Select2 con búsqueda
    $('#empleados').select2({
        theme: 'bootstrap-5',
        placeholder: 'Seleccione empleados...',
        allowClear: true,
        templateResult: formatEmpleado,
        templateSelection: formatEmpleadoSelection
    });
    

});

function formatEmpleado(empleado) {
    if (!empleado.id) {
        return empleado.text;
    }
    
    const $empleado = $(empleado.element);
    const cargo = $empleado.data('cargo');
    const areas = $empleado.data('areas');
    
    let html = '<div>';
    html += '<strong>' + empleado.text + '</strong>';
    if (cargo && cargo !== 'Sin cargo') {
        html += '<br><small class="text-muted">Cargo: ' + cargo + '</small>';
    }
    if (areas) {
        html += '<br><small class="text-info">Áreas: ' + areas + '</small>';
    }
    html += '</div>';
    
    return $(html);
}

function formatEmpleadoSelection(empleado) {
    return empleado.text;
}

function seleccionarTodos() {
    $('#empleados option').prop('selected', true);
    $('#empleados').trigger('change');
}

function limpiarSeleccion() {
    $('#empleados').val(null).trigger('change');
}
</script>
{% endblock %}
