"""
Comando de Django para probar el sistema de advertencias de acceso no autorizado.

Uso:
    python manage.py test_unauthorized_system --username empleado1
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from Base.models import UnauthorizedAccessAttempt
from django.utils import timezone

User = get_user_model()


class Command(BaseCommand):
    help = 'Prueba el sistema de advertencias de acceso no autorizado'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            required=True,
            help='Nombre de usuario para probar'
        )
        parser.add_argument(
            '--attempts',
            type=int,
            default=3,
            help='Número de intentos a simular (default: 3)'
        )

    def handle(self, *args, **options):
        username = options['username']
        attempts = options['attempts']
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Usuario "{username}" no encontrado')
            )
            return
        
        self.stdout.write(f'Simulando {attempts} intentos para usuario: {user.username}')
        
        for i in range(1, attempts + 1):
            # Crear intento simulado
            attempt = UnauthorizedAccessAttempt.objects.create(
                user=user,
                ip_address='127.0.0.1',
                url_attempted=f'http://localhost:8000/test-url-{i}/',
                user_agent='Test User Agent',
                session_key='test-session'
            )
            
            # Obtener conteo actual
            recent_count = UnauthorizedAccessAttempt.get_recent_attempts_count(user)
            
            self.stdout.write(
                f'  Intento #{i} creado - Total recientes: {recent_count}'
            )
            
            # Mostrar qué pasaría
            if recent_count >= 3:
                self.stdout.write(
                    self.style.ERROR(
                        f'    ⚠️  Usuario sería DESLOGUEADO automáticamente'
                    )
                )
            elif recent_count == 2:
                self.stdout.write(
                    self.style.WARNING(
                        f'    🚨 ADVERTENCIA FINAL - Próximo intento = deslogueo'
                    )
                )
            elif recent_count == 1:
                self.stdout.write(
                    self.style.WARNING(
                        f'    ⚠️  Primera advertencia mostrada'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSimulación completada. Revisa los intentos en el admin.'
            )
        )
