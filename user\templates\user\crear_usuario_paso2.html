{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Crear <PERSON>uario - Paso 2 - Teléfonos{% endblock %}

{% block content %}
<style>
    :root {
        --primary-color: #1A237E;
        --secondary-color: #283593;
        --success-color: #28a745;
        --light-gray: #e9ecef;
        --dark-gray: #6c757d;
    }

    .progress-container {
        margin-bottom: 3rem;
        padding: 0 2rem;
    }

    .progress-bar-custom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        margin-bottom: 1rem;
    }

    .progress-line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 4px;
        background-color: var(--light-gray);
        z-index: 1;
        border-radius: 2px;
    }

    .progress-line-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
        transition: width 0.3s ease;
    }

    .step-item {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: white;
        padding: 0.5rem;
    }

    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        border: 3px solid var(--light-gray);
        background: white;
        color: var(--dark-gray);
    }

    .step-item.active .step-circle {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
    }

    .step-item.completed .step-circle {
        background: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }

    .step-label {
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
        color: var(--dark-gray);
        transition: color 0.3s ease;
    }

    .step-item.active .step-label {
        color: var(--primary-color);
        font-weight: 600;
    }

    .step-item.completed .step-label {
        color: var(--success-color);
        font-weight: 600;
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 2.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
        border: 1px solid rgba(26, 35, 126, 0.1);
    }
    
    .telefono-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        animation: slideIn 0.3s ease;
    }

    .telefono-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-color: var(--primary-color);
    }

    .telefono-info {
        flex-grow: 1;
    }

    .telefono-actions {
        margin-left: 1rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--dark-gray);
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px dashed #dee2e6;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
        color: var(--primary-color);
    }

    .telefono-form-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .telefono-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .progress-container {
            padding: 0 1rem;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .step-label {
            font-size: 0.8rem;
        }

        .form-section {
            padding: 1.5rem;
        }
    }
</style>



<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Barra de progreso moderna -->
            <div class="progress-container">
                <div class="progress-bar-custom">
                    <div class="progress-line">
                        <div class="progress-line-fill" style="width: 66.66%;"></div>
                    </div>

                    <div class="step-item completed">
                        <div class="step-circle"><i class="fas fa-check"></i></div>
                        <div class="step-label">Información<br>Básica</div>
                    </div>

                    <div class="step-item active">
                        <div class="step-circle">2</div>
                        <div class="step-label">Teléfonos</div>
                    </div>

                    <div class="step-item">
                        <div class="step-circle">3</div>
                        <div class="step-label">Familiares</div>
                    </div>
                </div>
            </div>
            
            <!-- Información del usuario -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="alert alert-info">
                        <i class="fas fa-user me-2"></i>
                        <strong>Usuario:</strong> {{ usuario.get_full_name|default:usuario.username }}
                        <span class="ms-3"><strong>DPI:</strong> {{ usuario.dpi }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Formulario para agregar teléfonos -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="form-section">
                        <h4><i class="fas fa-phone me-2"></i>Agregar Teléfonos del Usuario</h4>
                        <p class="text-muted">Agrega los números de teléfono del usuario. Puedes agregar múltiples números.</p>

                        <!-- Formulario para agregar teléfono -->
                        <div class="telefono-form-card">
                            <h6><i class="fas fa-plus-circle me-2"></i>Nuevo Teléfono</h6>
                            <form id="telefono-form" method="post">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-5">
                                        <label for="numero" class="form-label">Número de Teléfono</label>
                                        <input type="text" class="form-control" id="numero" name="numero"
                                               placeholder="12345678 o +502 1234-5678"
                                               pattern="[0-9+\-\s()]{8,15}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="tipo" class="form-label">Tipo</label>
                                        <select class="form-select" id="tipo" name="tipo" required>
                                            <option value="">Selecciona...</option>
                                            {% for value, label in tipos_telefono %}
                                                <option value="{{ value }}">{{ label }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="estado" class="form-label">Estado</label>
                                        <select class="form-select" id="estado" name="is_active">
                                            <option value="true" selected>Activo</option>
                                            <option value="false">Inactivo</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Tabla de teléfonos agregados -->
                        <div class="mt-4">
                            <h5><i class="fas fa-list me-2"></i>Teléfonos Agregados</h5>
                            <div class="telefono-table">
                                {% if telefonos %}
                                    <table class="table table-hover mb-0" id="telefonos-tabla">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Número</th>
                                                <th>Tipo</th>
                                                <th>Estado</th>
                                                <th width="100">Acciones</th>
                                            </tr>
                                        </thead>
                                        <tbody id="telefonos-tbody">
                                            {% for telefono in telefonos %}
                                                <tr data-telefono-id="{{ telefono.id }}">
                                                    <td><strong>{{ telefono.numero }}</strong></td>
                                                    <td>
                                                        <span class="badge bg-info">{{ telefono.get_tipo_display }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{% if telefono.is_active %}success{% else %}secondary{% endif %}">
                                                            {% if telefono.is_active %}Activo{% else %}Inactivo{% endif %}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-outline-danger eliminar-telefono"
                                                                data-telefono-id="{{ telefono.id }}" title="Eliminar">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                {% else %}
                                    <div class="empty-state" id="empty-telefonos">
                                        <i class="fas fa-phone-slash"></i>
                                        <p><strong>No hay teléfonos agregados aún</strong></p>
                                        <small>Agrega números de teléfono usando el formulario de arriba.</small>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Botones de navegación -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'user:crear_usuario_paso1' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Anterior
                            </a>
                            
                            <div>
                                <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-secondary me-2">
                                    Cancelar
                                </a>
                                <form method="post" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" name="continuar" class="btn btn-primary">
                                        Continuar <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('telefono-form');
    const telefonosLista = document.querySelector('.telefono-table');
    const emptyState = document.getElementById('empty-telefonos');
    
    // Manejar envío del formulario de teléfonos
    if (form) {
        console.log('Formulario encontrado:', form);
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Formulario enviado');

            const formData = new FormData(form);
            console.log('Datos del formulario:', Object.fromEntries(formData));
            
            fetch('', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => {
                console.log('Respuesta recibida:', response);
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);
                if (data.success) {
                    // Agregar teléfono a la lista
                    agregarTelefonoALista(data.celular);
                    
                    // Limpiar formulario
                    form.reset();
                    
                    // Ocultar estado vacío
                    if (emptyState) {
                        emptyState.style.display = 'none';
                    }
                    
                    // Mostrar mensaje de éxito con SweetAlert
                    Swal.fire({
                        icon: 'success',
                        title: '¡Éxito!',
                        text: data.message || 'Teléfono agregado correctamente',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    // Mostrar mensaje de error con SweetAlert
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Error al agregar teléfono'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        });
    }
    
    // Manejar eliminación de teléfonos
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('eliminar-telefono') || e.target.closest('.eliminar-telefono')) {
            const button = e.target.classList.contains('eliminar-telefono') ? e.target : e.target.closest('.eliminar-telefono');
            const telefonoId = button.dataset.telefonoId;

            // Usar SweetAlert para confirmación
            Swal.fire({
                title: '¿Estás seguro?',
                text: 'Esta acción eliminará el teléfono permanentemente',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    eliminarTelefono(telefonoId);
                }
            });
        }
    });
    
    function agregarTelefonoALista(celular) {
        // Si no existe la tabla, crearla
        let tabla = document.getElementById('telefonos-tabla');
        if (!tabla) {
            // Ocultar estado vacío
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            // Crear tabla
            const telefonoTable = document.querySelector('.telefono-table');
            telefonoTable.innerHTML = `
                <table class="table table-hover mb-0" id="telefonos-tabla">
                    <thead class="table-light">
                        <tr>
                            <th>Número</th>
                            <th>Tipo</th>
                            <th>Estado</th>
                            <th width="100">Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="telefonos-tbody">
                    </tbody>
                </table>
            `;
        }

        // Agregar fila a la tabla
        const tbody = document.getElementById('telefonos-tbody');
        const row = document.createElement('tr');
        row.dataset.telefonoId = celular.id;

        const estadoBadge = celular.is_active ? 'success' : 'secondary';
        const estadoTexto = celular.is_active ? 'Activo' : 'Inactivo';

        row.innerHTML = `
            <td><strong>${celular.numero}</strong></td>
            <td>
                <span class="badge bg-info">${celular.tipo}</span>
            </td>
            <td>
                <span class="badge bg-${estadoBadge}">${estadoTexto}</span>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger eliminar-telefono"
                        data-telefono-id="${celular.id}" title="Eliminar">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        tbody.appendChild(row);
    }
    
    function eliminarTelefono(telefonoId) {
        const formData = new FormData();
        formData.append('eliminar_telefono', 'true');
        formData.append('telefono_id', telefonoId);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch('', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remover fila de la tabla
                const telefonoRow = document.querySelector(`tr[data-telefono-id="${telefonoId}"]`);
                if (telefonoRow) {
                    telefonoRow.remove();
                }

                // Verificar si quedan teléfonos
                const tbody = document.getElementById('telefonos-tbody');
                if (tbody && tbody.children.length === 0) {
                    // Mostrar estado vacío
                    const telefonoTable = document.querySelector('.telefono-table');
                    telefonoTable.innerHTML = `
                        <div class="empty-state" id="empty-telefonos">
                            <i class="fas fa-phone-slash"></i>
                            <p><strong>No hay teléfonos agregados aún</strong></p>
                            <small>Agrega números de teléfono usando el formulario de arriba.</small>
                        </div>
                    `;
                }

                // Mostrar mensaje de éxito con SweetAlert
                Swal.fire({
                    icon: 'success',
                    title: '¡Eliminado!',
                    text: data.message || 'Teléfono eliminado correctamente',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                // Mostrar mensaje de error con SweetAlert
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message || 'Error al eliminar teléfono'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            mostrarMensaje('Error de conexión', 'error');
        });
    }
    
    // Función mostrarMensaje eliminada - ahora usamos SweetAlert
});
</script>
{% endblock %}
