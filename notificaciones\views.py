"""
notificaciones/views.py
Vistas para el sistema de notificaciones manuales.

Incluye:
- Vistas basadas en clases para CRUD de notificaciones
- Vistas para envío a usuarios y grupos
- Endpoints AJAX para funcionalidad en tiempo real
- Control de permisos según roles de usuario
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.views.generic import ListView, CreateView, DetailView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.db.models import Q, Count, Prefetch
from django.utils import timezone
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model

from permissions.core import PermissionHelper
from .models import Notificacion, NotificacionUsuario, NotificacionGrupo
import logging

# Logger para eventos de seguridad
security_logger = logging.getLogger('security')
from .forms import (
    NotificacionUsuarioForm,
    NotificacionGrupoForm,
    NotificacionMasivaForm,
    FiltroNotificacionesForm
)

User = get_user_model()


class NotificationPermissionMixin(UserPassesTestMixin):
    """
    Mixin para verificar permisos de notificaciones.
    """

    def test_func(self):
        return PermissionHelper.can_create_notifications(self.request.user)

    def handle_no_permission(self):
        messages.error(
            self.request,
            'No tiene permisos para acceder a esta funcionalidad.'
        )
        return redirect('/inicio/')


class MassNotificationPermissionMixin(UserPassesTestMixin):
    """
    Mixin para verificar permisos de notificaciones masivas.
    """

    def test_func(self):
        return PermissionHelper.can_send_mass_notifications(self.request.user)

    def handle_no_permission(self):
        messages.error(
            self.request,
            'No tiene permisos para enviar notificaciones masivas.'
        )
        return redirect('notificaciones:lista_notificaciones')


class ListaNotificacionesView(NotificationPermissionMixin, LoginRequiredMixin, ListView):
    """
    Vista para listar notificaciones con filtros y paginación.
    Solo accesible para usuarios con permisos de gestión de notificaciones.
    """
    model = Notificacion
    template_name = 'notificaciones/lista_notificaciones.html'
    context_object_name = 'notificaciones'
    paginate_by = 20

    def get_queryset(self):
        """Filtra notificaciones según permisos del usuario."""
        queryset = Notificacion.objects.select_related('creado_por', 'ticket').prefetch_related(
            'usuarios_notificados__usuario',
            'grupos_notificados__grupo'
        )

        # Filtrar según permisos
        if not PermissionHelper.can_manage_all_notifications(self.request.user):
            # Solo ver notificaciones propias
            queryset = queryset.filter(creado_por=self.request.user)

        # Aplicar filtros del formulario
        form = FiltroNotificacionesForm(self.request.GET)
        if form.is_valid():
            if form.cleaned_data.get('tipo'):
                queryset = queryset.filter(tipo=form.cleaned_data['tipo'])

            if form.cleaned_data.get('fecha_desde'):
                queryset = queryset.filter(fecha_creacion__date__gte=form.cleaned_data['fecha_desde'])

            if form.cleaned_data.get('fecha_hasta'):
                queryset = queryset.filter(fecha_creacion__date__lte=form.cleaned_data['fecha_hasta'])

            if form.cleaned_data.get('creado_por'):
                queryset = queryset.filter(creado_por=form.cleaned_data['creado_por'])

        return queryset.filter(is_active=True).order_by('-fecha_creacion')

    def get_context_data(self, **kwargs):
        """Añade contexto adicional."""
        context = super().get_context_data(**kwargs)
        context['filtro_form'] = FiltroNotificacionesForm(self.request.GET)

        # Estadísticas
        # Calcular el rango de hoy en UTC para evitar problemas de timezone
        ahora = timezone.now()
        inicio_dia = ahora.replace(hour=0, minute=0, second=0, microsecond=0)
        fin_dia = ahora.replace(hour=23, minute=59, second=59, microsecond=999999)

        if PermissionHelper.can_manage_all_notifications(self.request.user):
            context['total_notificaciones'] = Notificacion.objects.filter(is_active=True).count()
            context['notificaciones_hoy'] = Notificacion.objects.filter(
                fecha_creacion__gte=inicio_dia,
                fecha_creacion__lte=fin_dia,
                is_active=True
            ).count()
        else:
            context['total_notificaciones'] = Notificacion.objects.filter(
                creado_por=self.request.user,
                is_active=True
            ).count()
            context['notificaciones_hoy'] = Notificacion.objects.filter(
                creado_por=self.request.user,
                fecha_creacion__gte=inicio_dia,
                fecha_creacion__lte=fin_dia,
                is_active=True
            ).count()

        return context


class CrearNotificacionUsuarioView(LoginRequiredMixin, NotificationPermissionMixin, CreateView):
    """
    Vista para crear notificaciones dirigidas a usuarios específicos.
    """
    model = Notificacion
    form_class = NotificacionUsuarioForm
    template_name = 'notificaciones/crear_notificacion_usuario.html'
    success_url = reverse_lazy('notificaciones:lista_notificaciones')

    def get_form_kwargs(self):
        """Pasa el usuario actual al formulario."""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        """Procesa el formulario válido."""
        response = super().form_valid(form)

        # Mostrar mensaje de éxito
        usuarios_count = getattr(self.object, '_usuarios_notificados', 0)
        messages.success(
            self.request,
            f'Notificación creada y enviada exitosamente a {usuarios_count} usuario(s).'
        )

        return response


class CrearNotificacionGrupoView(LoginRequiredMixin, NotificationPermissionMixin, CreateView):
    """
    Vista para crear notificaciones dirigidas a grupos específicos.
    """
    model = Notificacion
    form_class = NotificacionGrupoForm
    template_name = 'notificaciones/crear_notificacion_grupo.html'
    success_url = reverse_lazy('notificaciones:lista_notificaciones')

    def get_form_kwargs(self):
        """Pasa el usuario actual al formulario."""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        """Procesa el formulario válido."""
        response = super().form_valid(form)

        # Mostrar mensaje de éxito
        grupos_count = getattr(self.object, '_grupos_notificados', 0)
        individuales_count = getattr(self.object, '_individuales_creadas', 0)

        mensaje = f'Notificación creada y enviada a {grupos_count} grupo(s).'
        if individuales_count > 0:
            mensaje += f' Se crearon {individuales_count} notificaciones individuales.'

        messages.success(self.request, mensaje)
        return response


class CrearNotificacionMasivaView(LoginRequiredMixin, MassNotificationPermissionMixin, CreateView):
    """
    Vista para crear notificaciones masivas.
    """
    model = Notificacion
    form_class = NotificacionMasivaForm
    template_name = 'notificaciones/crear_notificacion_masiva.html'
    success_url = reverse_lazy('notificaciones:lista_notificaciones')

    def get_form_kwargs(self):
        """Pasa el usuario actual al formulario."""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        """Procesa el formulario válido."""
        response = super().form_valid(form)

        # Mostrar mensaje de éxito
        usuarios_count = getattr(self.object, '_usuarios_notificados', 0)
        grupos_count = getattr(self.object, '_grupos_notificados', 0)

        mensaje = f'Notificación masiva enviada exitosamente a {usuarios_count} usuario(s)'
        if grupos_count > 0:
            mensaje += f' en {grupos_count} grupo(s)'
        mensaje += '.'

        messages.success(self.request, mensaje)
        return response


class DetalleNotificacionView(NotificationPermissionMixin, LoginRequiredMixin, DetailView):
    """
    Vista para ver detalles de una notificación.
    Solo accesible para usuarios con permisos de gestión de notificaciones.
    """
    model = Notificacion
    template_name = 'notificaciones/detalle_notificacion.html'
    context_object_name = 'notificacion'

    def get_queryset(self):
        """Filtra según permisos del usuario."""
        queryset = Notificacion.objects.select_related('creado_por', 'ticket').prefetch_related(
            'usuarios_notificados__usuario',
            'grupos_notificados__grupo'
        )

        if not PermissionHelper.can_manage_all_notifications(self.request.user):
            queryset = queryset.filter(creado_por=self.request.user)

        return queryset

    def get_context_data(self, **kwargs):
        """Añade contexto adicional."""
        context = super().get_context_data(**kwargs)

        # Estadísticas de la notificación
        notificacion = self.object
        context['usuarios_notificados'] = notificacion.usuarios_notificados.select_related('usuario').order_by('usuario__first_name', 'usuario__last_name')
        context['grupos_notificados'] = notificacion.grupos_notificados.select_related('grupo').order_by('grupo__name')

        # Estadísticas de lectura
        total_usuarios = context['usuarios_notificados'].count()
        leidas = context['usuarios_notificados'].filter(leida=True).count()
        no_leidas = total_usuarios - leidas

        context['estadisticas'] = {
            'total_usuarios': total_usuarios,
            'leidas': leidas,
            'no_leidas': no_leidas,
            'porcentaje_leidas': round((leidas / total_usuarios * 100) if total_usuarios > 0 else 0, 1)
        }

        return context


class MisNotificacionesView(LoginRequiredMixin, ListView):
    """
    Vista para que los usuarios vean sus notificaciones recibidas.
    """
    model = NotificacionUsuario
    template_name = 'notificaciones/mis_notificaciones.html'
    context_object_name = 'notificaciones'
    paginate_by = 15

    def get_queryset(self):
        """Obtiene las notificaciones del usuario actual."""
        return NotificacionUsuario.objects.filter(
            usuario=self.request.user,
            notificacion__is_active=True
        ).select_related('notificacion', 'notificacion__creado_por').order_by('-fecha_envio')

    def get_context_data(self, **kwargs):
        """Añade contexto adicional."""
        context = super().get_context_data(**kwargs)

        # Estadísticas del usuario
        total = self.get_queryset().count()
        no_leidas = self.get_queryset().filter(leida=False).count()

        context['estadisticas'] = {
            'total': total,
            'no_leidas': no_leidas,
            'leidas': total - no_leidas
        }

        return context


@login_required
@require_POST
def marcar_leida(request, notificacion_id):
    """
    Marca una notificación como leída.
    """
    notif_usuario = get_object_or_404(
        NotificacionUsuario,
        id=notificacion_id,
        usuario=request.user
    )

    notif_usuario.marcar_como_leida()

    messages.success(request, 'Notificación marcada como leída.')
    return redirect('notificaciones:mis_notificaciones')


@login_required
@require_POST
def marcar_todas_leidas(request):
    """
    Marca todas las notificaciones del usuario como leídas.
    """
    notificaciones_no_leidas = NotificacionUsuario.objects.filter(
        usuario=request.user,
        leida=False,
        notificacion__is_active=True
    )

    count = 0
    for notif in notificaciones_no_leidas:
        notif.marcar_como_leida()
        count += 1

    messages.success(request, f'{count} notificación(es) marcada(s) como leída(s).')
    return redirect('notificaciones:mis_notificaciones')


# ============================================================================
# VISTAS AJAX
# ============================================================================

@login_required
def contar_no_leidas_ajax(request):
    """
    Endpoint AJAX para contar notificaciones no leídas del usuario.
    Incluye validaciones de seguridad.
    """
    try:
        # Solo contar notificaciones del usuario autenticado
        count = NotificacionUsuario.objects.filter(
            usuario=request.user,
            leida=False,
            notificacion__is_active=True
        ).count()

        # Validar que el count sea razonable (prevenir ataques DoS)
        if count > 9999:
            count = 9999  # Limitar para evitar problemas de UI

        return JsonResponse({
            'count': int(count),  # Asegurar que sea entero
            'has_notifications': count > 0
        })

    except Exception as e:
        # En caso de error, devolver 0 notificaciones
        return JsonResponse({
            'count': 0,
            'has_notifications': False
        })


@login_required
def obtener_recientes_ajax(request):
    """
    Endpoint AJAX para obtener notificaciones recientes del usuario.
    Incluye validaciones de seguridad contra ataques.
    """
    try:
        # Validación 1: Sanitizar parámetro limit
        limit_param = request.GET.get('limit', '5')
        try:
            limit = int(limit_param)
        except (ValueError, TypeError):
            limit = 5  # Valor por defecto seguro

        # Validación 2: Limitar rango para prevenir ataques DoS
        if limit < 1:
            limit = 1
        elif limit > 50:  # Máximo razonable
            limit = 50

        # Validación 3: Solo notificaciones del usuario autenticado
        notificaciones = NotificacionUsuario.objects.filter(
            usuario=request.user,
            notificacion__is_active=True
        ).select_related(
            'notificacion',
            'notificacion__creado_por'
        ).order_by('-fecha_envio')[:limit]

        data = []
        for notif in notificaciones:
            # Validación 4: Sanitizar datos de salida
            try:
                # Escapar contenido potencialmente peligroso
                mensaje = notif.notificacion.mensaje or ''
                titulo = notif.notificacion.titulo or ''

                # Validar URL de acción
                url_accion = notif.notificacion.url_accion
                if url_accion and not url_accion.startswith(('/', 'http://', 'https://')):
                    url_accion = None  # Rechazar URLs potencialmente maliciosas

                data.append({
                    'id': notif.id,
                    'mensaje': mensaje[:500],  # Limitar longitud
                    'titulo': titulo[:100],    # Limitar longitud
                    'tipo': notif.notificacion.tipo,
                    'tipo_display': notif.notificacion.get_tipo_display(),
                    'tipo_color': notif.notificacion.get_tipo_display_color(),
                    'icono': notif.notificacion.get_icono(),
                    'fecha_envio': notif.fecha_envio.strftime('%d/%m/%Y %H:%M'),
                    'leida': bool(notif.leida),
                    'url_accion': url_accion,
                    'creado_por': (notif.notificacion.creado_por.get_full_name()
                                 if notif.notificacion.creado_por else 'Sistema')[:50]
                })
            except Exception:
                # Si hay error con una notificación específica, saltarla
                continue

        return JsonResponse({
            'notificaciones': data,
            'total': len(data)
        })

    except Exception as e:
        # No exponer detalles internos del error por seguridad
        return JsonResponse({
            'error': 'Error interno del servidor'
        }, status=500)


@login_required
@require_POST
def marcar_leida_ajax(request):
    """
    Endpoint AJAX para marcar una notificación como leída.
    Incluye validaciones de seguridad contra ataques.
    """
    try:
        notificacion_id = request.POST.get('notificacion_id')

        # Validación 1: Verificar que se proporcione el ID
        if not notificacion_id:
            return JsonResponse({
                'success': False,
                'message': 'ID de notificación requerido'
            }, status=400)

        # Validación 2: Verificar que el ID sea numérico (prevenir inyección SQL)
        try:
            notificacion_id = int(notificacion_id)
        except (ValueError, TypeError):
            # Log de seguridad: intento de inyección
            security_logger.warning(
                f'Intento de inyección SQL detectado - Usuario: {request.user.username}, '
                f'IP: {request.META.get("REMOTE_ADDR")}, '
                f'ID inválido: {request.POST.get("notificacion_id")}'
            )
            return JsonResponse({
                'success': False,
                'message': 'ID de notificación inválido'
            }, status=400)

        # Validación 3: Verificar que el ID sea positivo
        if notificacion_id <= 0:
            return JsonResponse({
                'success': False,
                'message': 'ID de notificación inválido'
            }, status=400)

        # Validación 4: Verificar que la notificación existe Y pertenece al usuario
        # Esto previene ataques de acceso no autorizado
        notif_usuario = get_object_or_404(
            NotificacionUsuario,
            id=notificacion_id,
            usuario=request.user
        )

        # Validación 5: Verificar que no esté ya marcada como leída (prevenir spam)
        if notif_usuario.leida:
            return JsonResponse({
                'success': False,
                'message': 'La notificación ya está marcada como leída'
            }, status=400)

        notif_usuario.marcar_como_leida()

        return JsonResponse({
            'success': True,
            'message': 'Notificación marcada como leída'
        })

    except NotificacionUsuario.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Notificación no encontrada o sin permisos'
        }, status=404)
    except Exception as e:
        # No exponer detalles internos del error por seguridad
        return JsonResponse({
            'success': False,
            'message': 'Error interno del servidor'
        }, status=500)


@login_required
def estadisticas_notificaciones_ajax(request):
    """
    Endpoint AJAX para obtener estadísticas de notificaciones.
    """
    if not PermissionHelper.can_create_notifications(request.user):
        return JsonResponse({'error': 'Sin permisos'}, status=403)

    # Calcular el rango de hoy en UTC para evitar problemas de timezone
    ahora = timezone.now()
    inicio_dia = ahora.replace(hour=0, minute=0, second=0, microsecond=0)
    fin_dia = ahora.replace(hour=23, minute=59, second=59, microsecond=999999)

    # Estadísticas según permisos
    if PermissionHelper.can_manage_all_notifications(request.user):
        # Admin ve todas las estadísticas
        total_notificaciones = Notificacion.objects.filter(is_active=True).count()
        total_usuarios_notificados = NotificacionUsuario.objects.count()
        total_grupos_notificados = NotificacionGrupo.objects.count()

        # Notificaciones de hoy
        notificaciones_hoy = Notificacion.objects.filter(
            fecha_creacion__gte=inicio_dia,
            fecha_creacion__lte=fin_dia,
            is_active=True
        ).count()

        # Notificaciones por tipo
        por_tipo = {}
        for tipo_code, tipo_name in Notificacion.TIPO_CHOICES:
            count = Notificacion.objects.filter(tipo=tipo_code, is_active=True).count()
            por_tipo[tipo_name] = count

        # Notificaciones recientes (últimos 7 días)
        fecha_limite = timezone.now() - timezone.timedelta(days=7)
        recientes = Notificacion.objects.filter(
            fecha_creacion__gte=fecha_limite,
            is_active=True
        ).count()

    else:
        # Otros usuarios ven solo sus estadísticas
        total_notificaciones = Notificacion.objects.filter(
            creado_por=request.user,
            is_active=True
        ).count()

        # Notificaciones de hoy (solo propias)
        notificaciones_hoy = Notificacion.objects.filter(
            creado_por=request.user,
            fecha_creacion__gte=inicio_dia,
            fecha_creacion__lte=fin_dia,
            is_active=True
        ).count()

        total_usuarios_notificados = NotificacionUsuario.objects.filter(
            notificacion__creado_por=request.user
        ).count()

        total_grupos_notificados = NotificacionGrupo.objects.filter(
            notificacion__creado_por=request.user
        ).count()

        # Notificaciones por tipo (solo propias)
        por_tipo = {}
        for tipo_code, tipo_name in Notificacion.TIPO_CHOICES:
            count = Notificacion.objects.filter(
                tipo=tipo_code,
                creado_por=request.user,
                is_active=True
            ).count()
            por_tipo[tipo_name] = count

        # Notificaciones recientes (últimos 7 días, solo propias)
        fecha_limite = timezone.now() - timezone.timedelta(days=7)
        recientes = Notificacion.objects.filter(
            fecha_creacion__gte=fecha_limite,
            creado_por=request.user,
            is_active=True
        ).count()

    return JsonResponse({
        'total_notificaciones': total_notificaciones,
        'notificaciones_hoy': notificaciones_hoy,
        'total_usuarios_notificados': total_usuarios_notificados,
        'total_grupos_notificados': total_grupos_notificados,
        'por_tipo': por_tipo,
        'recientes': recientes
    })


@login_required
def obtener_usuarios_ajax(request):
    """
    Endpoint AJAX para obtener lista de usuarios para el selector modal.
    """
    if not PermissionHelper.can_create_notifications(request.user):
        return JsonResponse({'error': 'Sin permisos'}, status=403)

    usuarios = User.objects.filter(is_active=True).select_related('cargo').prefetch_related('groups')

    data = []
    for usuario in usuarios:
        data.append({
            'id': usuario.id,
            'username': usuario.username,
            'full_name': usuario.get_full_name() or usuario.username,
            'email': usuario.email or '',
            'cargo': usuario.cargo.nombre if usuario.cargo else '',
            'groups_count': usuario.groups.count(),
            'is_active': usuario.is_active
        })

    return JsonResponse({
        'items': data,
        'total': len(data)
    })


@login_required
def obtener_grupos_ajax(request):
    """
    Endpoint AJAX para obtener lista de grupos para el selector modal.
    """
    if not PermissionHelper.can_create_notifications(request.user):
        return JsonResponse({'error': 'Sin permisos'}, status=403)

    grupos = Group.objects.all().annotate(
        members_count=Count('user', filter=Q(user__is_active=True))
    ).order_by('name')

    data = []
    for grupo in grupos:
        data.append({
            'id': grupo.id,
            'name': grupo.name,
            'members_count': grupo.members_count
        })

    return JsonResponse({
        'items': data,
        'total': len(data)
    })


@login_required
def estadisticas_sistema_ajax(request):
    """
    Endpoint AJAX para obtener estadísticas del sistema.
    """
    if not PermissionHelper.can_send_mass_notifications(request.user):
        return JsonResponse({'error': 'Sin permisos'}, status=403)

    usuarios_activos = User.objects.filter(is_active=True).count()

    return JsonResponse({
        'usuarios_activos': usuarios_activos
    })


# ============================================================================
# VISTAS DE UTILIDAD
# ============================================================================

lista_notificaciones = ListaNotificacionesView.as_view()
crear_notificacion_usuario = CrearNotificacionUsuarioView.as_view()
crear_notificacion_grupo = CrearNotificacionGrupoView.as_view()
crear_notificacion_masiva = CrearNotificacionMasivaView.as_view()
detalle_notificacion = DetalleNotificacionView.as_view()
mis_notificaciones = MisNotificacionesView.as_view()

# Alias para compatibilidad con URLs existentes
notificaciones_no_leidas = mis_notificaciones
crear_notificacion = crear_notificacion_usuario
enviar_notificacion_masiva = crear_notificacion_masiva


@login_required
def debug_permisos(request):
    """
    Vista temporal para debug de permisos.
    """
    return render(request, 'notificaciones/debug_permisos.html')
