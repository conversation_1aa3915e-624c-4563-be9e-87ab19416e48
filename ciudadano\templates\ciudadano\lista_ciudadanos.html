{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Lista de Ciudadanos{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con estadísticas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Gestión de Ciudadanos</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ total_ciudadanos }} ciudadano{{ total_ciudadanos|pluralize }} registrado{{ total_ciudadanos|pluralize }}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'tickets:crear_ticket' %}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-ticket-alt me-2"></i>Nuevo Ticket
                            </a>
                            {% if puede_crear %}
                                <a href="{% url 'ciudadano:crear_ciudadano' %}" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Nuevo Ciudadano
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros y búsqueda -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="q" class="form-label">Buscar Ciudadano</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="q" name="q" 
                                       value="{{ busqueda }}" 
                                       placeholder="Nombre, DPI, teléfono o email...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="con_tickets" class="form-label">Filtrar por Tickets</label>
                            <select class="form-select" id="con_tickets" name="con_tickets">
                                <option value="">Todos</option>
                                <option value="si" {% if con_tickets == "si" %}selected{% endif %}>Con tickets</option>
                                <option value="no" {% if con_tickets == "no" %}selected{% endif %}>Sin tickets</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>Filtrar
                            </button>
                            <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Limpiar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de ciudadanos -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Lista de Ciudadanos
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if ciudadanos %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Ciudadano</th>
                                        <th width="150">DPI</th>
                                        <th width="120">Teléfono</th>
                                        <th width="100">Tickets</th>
                                        <th width="120">Registrado</th>
                                        <th width="150">Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="ciudadanos-container">
                                    {% include 'ciudadano/parciales/lista_ciudadanos.html' %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Loading indicator -->
                        <div id="loading-indicator" class="text-center py-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2 text-muted">Cargando más ciudadanos...</p>
                        </div>

                        <!-- Paginación -->
                        {% if ciudadanos.has_next %}
                            <div class="card-footer bg-white border-top">
                                <div class="text-center">
                                    <button id="load-more" class="btn btn-outline-primary" data-page="{{ ciudadanos.next_page_number }}">
                                        <i class="fas fa-chevron-down me-2"></i>Cargar más ciudadanos
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay ciudadanos registrados</h5>
                            <p class="text-muted">
                                {% if busqueda %}
                                    No se encontraron ciudadanos con el criterio "{{ busqueda }}"
                                {% else %}
                                    Registra el primer ciudadano para comenzar
                                {% endif %}
                            </p>
                            {% if puede_crear %}
                                <a href="{% url 'ciudadano:crear_ciudadano' %}" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Registrar Primer Ciudadano
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #17a2b8, #138496);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.table tbody tr {
    transition: background-color 0.1s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

#loading-indicator {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.badge {
    font-weight: 500;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.getElementById('load-more');
    const loadingIndicator = document.getElementById('loading-indicator');
    const ciudadanosContainer = document.getElementById('ciudadanos-container');
    
    // Scroll infinito optimizado
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const page = this.dataset.page;
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            
            // Mostrar loading
            loadingIndicator.style.display = 'block';
            this.style.display = 'none';
            
            fetch(url.toString(), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Agregar nuevos ciudadanos
                ciudadanosContainer.insertAdjacentHTML('beforeend', data.html);
                
                // Ocultar loading
                loadingIndicator.style.display = 'none';
                
                // Actualizar botón o ocultarlo
                if (data.has_next) {
                    this.dataset.page = parseInt(page) + 1;
                    this.style.display = 'block';
                } else {
                    this.parentElement.parentElement.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.style.display = 'none';
                this.style.display = 'block';
                
                alert('Error al cargar más ciudadanos');
            });
        });
    }
});
</script>
{% endblock %}
