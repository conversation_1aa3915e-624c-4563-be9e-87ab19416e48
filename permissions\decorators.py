"""
permissions/decorators.py
Decoradores centralizados para control de acceso.

Contiene todos los decoradores de permisos del sistema, usando
el PermissionHelper centralizado y logging de seguridad.
"""

from functools import wraps
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.core.exceptions import PermissionDenied
from django.http import HttpResponseForbidden

from .core import PermissionHelper
from Base.error_handlers import log_permission_denied


def admin_required(view_func):
    """
    Decorador que requiere que el usuario sea Admin o Superadmin.
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.is_admin(request.user):
            log_permission_denied(request, "Acceso denegado - Se requiere rol Admin")
            raise PermissionDenied("Se requiere rol de Administrador para acceder a esta página.")
        return view_func(request, *args, **kwargs)
    return wrapper


def secretaria_or_admin_required(view_func):
    """
    Decorador que requiere que el usuario sea Secretaria o Admin.
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not (PermissionHelper.is_admin(request.user) or PermissionHelper.is_secretaria(request.user)):
            log_permission_denied(request, "Acceso denegado - Se requiere rol Admin o Secretaria")
            raise PermissionDenied("Se requiere rol de Administrador o Secretaria para acceder a esta página.")
        return view_func(request, *args, **kwargs)
    return wrapper


def supervisor_or_admin_required(view_func):
    """
    Decorador que requiere que el usuario sea Supervisor o Admin.
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not (PermissionHelper.is_admin(request.user) or PermissionHelper.is_supervisor(request.user)):
            log_permission_denied(request, "Acceso denegado - Se requiere rol Admin o Supervisor")
            raise PermissionDenied("Se requiere rol de Administrador o Supervisor para acceder a esta página.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_create_tickets_required(view_func):
    """
    Decorador que requiere permisos para crear tickets.
    
    Pueden crear tickets: Admin, Secretaria
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.can_create_tickets(request.user):
            log_permission_denied(request, "Acceso denegado - Sin permisos para crear tickets")
            raise PermissionDenied("No tienes permisos para crear tickets.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_manage_users_required(view_func):
    """
    Decorador que requiere permisos para gestionar usuarios.
    
    Pueden gestionar usuarios: Solo Admin
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.can_manage_users(request.user):
            log_permission_denied(request, "Acceso denegado - Sin permisos para gestionar usuarios")
            raise PermissionDenied("No tienes permisos para gestionar usuarios.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_assign_tickets_required(view_func):
    """
    Decorador que requiere permisos para asignar tickets.
    
    Pueden asignar tickets: Admin, Secretaria, Supervisor
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.can_assign_tickets(request.user):
            log_permission_denied(request, "Acceso denegado - Sin permisos para asignar tickets")
            raise PermissionDenied("No tienes permisos para asignar tickets.")
        return view_func(request, *args, **kwargs)
    return wrapper


def ticket_access_required(view_func):
    """
    Decorador que verifica el acceso a un ticket específico.
    Requiere que la vista reciba ticket_id como parámetro.
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        from tickets.models import Ticket
        
        ticket_id = kwargs.get('ticket_id')
        if not ticket_id:
            raise ValueError("La vista debe recibir ticket_id como parámetro")
        
        ticket = get_object_or_404(Ticket, id=ticket_id, is_active=True)
        
        # Admin puede ver cualquier ticket
        if PermissionHelper.is_admin(request.user):
            return view_func(request, *args, **kwargs)
        
        # Secretaria puede ver tickets que creó
        if PermissionHelper.is_secretaria(request.user) and ticket.creado_por == request.user:
            return view_func(request, *args, **kwargs)
        
        # Supervisor puede ver tickets de su área
        if PermissionHelper.is_supervisor(request.user):
            user_areas = PermissionHelper.get_user_areas(request.user)
            if user_areas.filter(id=ticket.grupo.id).exists():
                return view_func(request, *args, **kwargs)
        
        # Empleado puede ver tickets asignados a él
        if ticket.asignaciones.filter(usuario=request.user, is_active=True).exists():
            return view_func(request, *args, **kwargs)
        
        # Si llegamos aquí, no tiene permisos
        log_permission_denied(
            request, 
            f"Acceso denegado a ticket #{ticket.id} - Usuario sin permisos"
        )
        raise PermissionDenied(f"No tienes permisos para acceder al ticket #{ticket.id}.")
    
    return wrapper


def can_view_reports_required(view_func):
    """
    Decorador que requiere permisos para ver reportes.
    
    Pueden ver reportes: Admin, Supervisor
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not (PermissionHelper.is_admin(request.user) or PermissionHelper.is_supervisor(request.user)):
            log_permission_denied(request, "Acceso denegado - Sin permisos para ver reportes")
            raise PermissionDenied("No tienes permisos para ver reportes.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_manage_citizens_required(view_func):
    """
    Decorador que requiere permisos para gestionar ciudadanos.
    
    Pueden gestionar ciudadanos: Admin, Secretaria, Supervisor
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not (PermissionHelper.is_admin(request.user) or 
                PermissionHelper.is_secretaria(request.user) or 
                PermissionHelper.is_supervisor(request.user)):
            log_permission_denied(request, "Acceso denegado - Sin permisos para gestionar ciudadanos")
            raise PermissionDenied("No tienes permisos para gestionar ciudadanos.")
        return view_func(request, *args, **kwargs)
    return wrapper
