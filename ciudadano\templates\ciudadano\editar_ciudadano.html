{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Editar {{ ciudadano.nombre_completo }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Editar Ciudadano</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ ciudadano.nombre_completo }}
                                    <span class="ms-3">
                                        <i class="fas fa-id-card me-1"></i>DPI: {{ ciudadano.dpi }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'ciudadano:detalle_ciudadano' ciudadano.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Volver al Detalle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Editar Información del Ciudadano
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="form-editar-ciudadano">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- DPI -->
                            <div class="col-md-6 mb-3">
                                <label for="dpi" class="form-label">
                                    DPI <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="dpi" name="dpi" 
                                       required maxlength="13" pattern="[0-9]{13}" value="{{ ciudadano.dpi }}"
                                       placeholder="1234567890123">
                                <div class="form-text">13 dígitos numéricos</div>
                            </div>

                            <!-- Nombre Completo -->
                            <div class="col-md-6 mb-3">
                                <label for="nombre_completo" class="form-label">
                                    Nombre Completo <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="nombre_completo" name="nombre_completo" 
                                       required minlength="3" maxlength="200" value="{{ ciudadano.nombre_completo }}"
                                       placeholder="Nombres y apellidos completos">
                                <div class="form-text">Mínimo 3 caracteres</div>
                            </div>
                        </div>

                        <!-- Dirección -->
                        <div class="mb-3">
                            <label for="direccion" class="form-label">
                                Dirección <span class="text-muted">(Opcional)</span>
                            </label>
                            <textarea class="form-control" id="direccion" name="direccion" 
                                      rows="2" placeholder="Dirección completa del ciudadano">{{ ciudadano.direccion }}</textarea>
                        </div>

                        <div class="row">
                            <!-- Teléfono -->
                            <div class="col-md-6 mb-3">
                                <label for="telefono" class="form-label">
                                    Teléfono <span class="text-muted">(Opcional)</span>
                                </label>
                                <input type="tel" class="form-control" id="telefono" name="telefono" 
                                       maxlength="15" value="{{ ciudadano.telefono }}" placeholder="12345678">
                                <div class="form-text">Número de contacto</div>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    Email <span class="text-muted">(Opcional)</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       maxlength="100" value="{{ ciudadano.email }}" placeholder="<EMAIL>">
                                <div class="form-text">Correo electrónico</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Fecha de Nacimiento -->
                            <div class="col-md-6 mb-3">
                                <label for="fecha_nacimiento" class="form-label">
                                    Fecha de Nacimiento <span class="text-muted">(Opcional)</span>
                                </label>
                                <input type="date" class="form-control" id="fecha_nacimiento" name="fecha_nacimiento"
                                       value="{% if ciudadano.fecha_nacimiento %}{{ ciudadano.fecha_nacimiento|date:'Y-m-d' }}{% endif %}">
                                <div class="form-text">Para calcular la edad</div>
                            </div>

                            <!-- Género -->
                            <div class="col-md-6 mb-3">
                                <label for="genero" class="form-label">
                                    Género <span class="text-muted">(Opcional)</span>
                                </label>
                                <select class="form-select" id="genero" name="genero">
                                    <option value="">Seleccione género</option>
                                    <option value="1" {% if ciudadano.genero == 1 %}selected{% endif %}>Hombre</option>
                                    <option value="2" {% if ciudadano.genero == 2 %}selected{% endif %}>Mujer</option>
                                </select>
                            </div>
                        </div>

                        <!-- Botones -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'ciudadano:detalle_ciudadano' ciudadano.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary" id="btn-guardar">
                                <i class="fas fa-save me-2"></i>Guardar Cambios
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panel lateral -->
        <div class="col-lg-4">
            <!-- Información actual -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Información Actual
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Registrado:</small>
                        <div>{{ ciudadano.fecha_registro|date:"d/m/Y H:i" }}</div>
                    </div>
                    {% if ciudadano.get_total_tickets > 0 %}
                        <div class="mb-3">
                            <small class="text-muted">Total de tickets:</small>
                            <div>
                                <span class="badge bg-info">{{ ciudadano.get_total_tickets }}</span>
                            </div>
                        </div>
                    {% endif %}
                    {% if ciudadano.get_edad %}
                        <div class="mb-3">
                            <small class="text-muted">Edad actual:</small>
                            <div>{{ ciudadano.get_edad }} años</div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Información de ayuda -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Consejos de Edición
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Los cambios se guardarán inmediatamente</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>El DPI debe seguir siendo único</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Los tickets existentes no se afectan</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Solo edite si es necesario</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ffc107, #e0a800);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.form-control:focus, .form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-editar-ciudadano');
    const btnGuardar = document.getElementById('btn-guardar');
    const dpiInput = document.getElementById('dpi');
    const nombreInput = document.getElementById('nombre_completo');
    
    // Validación de DPI en tiempo real
    dpiInput.addEventListener('input', function() {
        // Solo permitir números
        this.value = this.value.replace(/[^0-9]/g, '');
        
        if (this.value.length === 13) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (this.value.length > 0) {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
        }
    });
    
    // Validación de nombre en tiempo real
    nombreInput.addEventListener('input', function() {
        if (this.value.length >= 3) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (this.value.length > 0) {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
        }
    });
    
    // Envío del formulario
    form.addEventListener('submit', function(e) {
        btnGuardar.disabled = true;
        btnGuardar.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Guardando...';
    });
});
</script>
{% endblock %}
