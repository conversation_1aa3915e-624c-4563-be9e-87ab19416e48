<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitud Incorrecta - Sistema de Tickets</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .error-icon {
            font-size: 6rem;
            color: var(--warning-color);
            margin-bottom: 1rem;
            animation: swing 2s infinite;
        }

        @keyframes swing {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(10deg); }
            75% { transform: rotate(-10deg); }
        }

        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: var(--warning-color);
            margin-bottom: 0.5rem;
        }

        .error-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .error-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn-outline-secondary {
            border: 2px solid #6c757d;
            color: #6c757d;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-2px);
        }

        .help-info {
            background: #f8f9fa;
            border-left: 4px solid var(--warning-color);
            padding: 1rem;
            margin-top: 2rem;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }

        .help-info h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .help-info ul {
            margin: 0;
            padding-left: 1.2rem;
            color: #666;
        }

        .help-info li {
            margin-bottom: 0.3rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-circle"></i>
        </div>
        
        <div class="error-code">400</div>
        <h1 class="error-title">Solicitud Incorrecta</h1>
        
        <p class="error-description">
            {{ error_message|default:"La solicitud enviada contiene datos incorrectos o incompletos. Por favor, verifica la información e intenta nuevamente." }}
        </p>

        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver Atrás
            </a>
            <a href="/inicio/" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Ir al Dashboard
            </a>
        </div>

        <div class="help-info">
            <h6><i class="fas fa-question-circle me-2"></i>Posibles Causas</h6>
            <ul>
                <li>Datos de formulario incompletos o inválidos</li>
                <li>Parámetros de URL incorrectos</li>
                <li>Formato de archivo no soportado</li>
                <li>Tamaño de archivo excedido</li>
                <li>Caracteres especiales no permitidos</li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
