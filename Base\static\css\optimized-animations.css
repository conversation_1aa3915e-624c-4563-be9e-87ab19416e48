/* 
 * Optimizaciones de animaciones para mejorar la percepción de velocidad
 * Reduce las duraciones de las transiciones para hacer el sistema más ágil
 */

/* Animación principal del body - Reducida de 0.5s a 0.15s */
body {
    animation: fadeIn 0.15s ease-in !important;
}

/* Animación del sidebar heading - Reducida de 0.5s a 0.2s */
.sidebar-heading h3 {
    animation: slideIn 0.2s ease-out !important;
}

/* Transiciones del sidebar - Reducidas de 0.3s a 0.15s */
#sidebar-wrapper {
    transition: all 0.15s ease !important;
}

.main-content {
    transition: all 0.15s ease !important;
}

#sidebarToggle {
    transition: transform 0.15s ease !important;
}

/* Transiciones de los items del menú - Reducidas de 0.3s a 0.1s */
.list-group-item {
    transition: all 0.1s ease !important;
}

.dropdown-menu-custom {
    transition: max-height 0.15s ease !important;
}

.dropdown-item-custom {
    transition: all 0.1s ease !important;
}

.dropdown-toggle-custom::after {
    transition: transform 0.15s ease !important;
}

/* Animaciones de modales y elementos dinámicos */
.modal.fade .modal-dialog {
    transition: transform 0.2s ease-out !important;
}

.collapse {
    transition: height 0.2s ease !important;
}

.collapsing {
    transition: height 0.2s ease !important;
}

/* Animaciones de botones y elementos interactivos */
.btn {
    transition: all 0.1s ease !important;
}

.card {
    transition: all 0.15s ease !important;
}

.form-control, .form-select {
    transition: all 0.1s ease !important;
}

/* Animaciones de SweetAlert - Más rápidas */
.swal2-popup {
    animation-duration: 0.2s !important;
}

.swal2-backdrop-show {
    animation-duration: 0.15s !important;
}

.swal2-backdrop-hide {
    animation-duration: 0.1s !important;
}

/* Animaciones de carga y spinners - Mantener velocidad pero reducir delay */
.fa-spin {
    animation-duration: 1s !important;
}

/* Hover effects más rápidos */
*:hover {
    transition-duration: 0.1s !important;
}

/* Animaciones de aparición de elementos */
.fade-in {
    animation: fadeIn 0.15s ease-in !important;
}

.slide-in {
    animation: slideIn 0.2s ease-out !important;
}

/* Optimizaciones específicas para tablas y listas */
.table tbody tr {
    transition: background-color 0.1s ease !important;
}

.list-group-item {
    transition: all 0.1s ease !important;
}

/* Optimizaciones para elementos de navegación */
.nav-link {
    transition: all 0.1s ease !important;
}

.navbar-nav .nav-link {
    transition: color 0.1s ease !important;
}

/* Optimizaciones para elementos de formulario */
.form-check-input {
    transition: all 0.1s ease !important;
}

.form-range {
    transition: all 0.1s ease !important;
}

/* Optimizaciones para badges y elementos pequeños */
.badge {
    transition: all 0.1s ease !important;
}

.alert {
    transition: all 0.15s ease !important;
}

/* Optimizaciones para dropdowns */
.dropdown-menu {
    transition: all 0.15s ease !important;
}

.dropdown-toggle::after {
    transition: transform 0.1s ease !important;
}

/* Optimizaciones para elementos de progreso */
.progress-bar {
    transition: width 0.2s ease !important;
}

/* Optimizaciones para elementos de acordeón */
.accordion-button {
    transition: all 0.1s ease !important;
}

.accordion-collapse {
    transition: height 0.2s ease !important;
}

/* Optimizaciones para elementos de pestañas */
.nav-tabs .nav-link {
    transition: all 0.1s ease !important;
}

.tab-content {
    transition: all 0.15s ease !important;
}

/* Optimizaciones para elementos de paginación */
.page-link {
    transition: all 0.1s ease !important;
}

/* Optimizaciones para elementos de breadcrumb */
.breadcrumb-item {
    transition: all 0.1s ease !important;
}

/* Optimizaciones para elementos de tooltip */
.tooltip {
    transition: opacity 0.1s ease !important;
}

/* Optimizaciones para elementos de popover */
.popover {
    transition: opacity 0.15s ease !important;
}

/* Optimizaciones para elementos de offcanvas */
.offcanvas {
    transition: transform 0.2s ease !important;
}

/* Optimizaciones para elementos de toast */
.toast {
    transition: all 0.15s ease !important;
}

/* Optimizaciones para elementos de carousel */
.carousel-item {
    transition: transform 0.3s ease !important;
}

/* Optimizaciones para elementos de collapse */
.collapse {
    transition: height 0.2s ease !important;
}

/* Optimizaciones para elementos de modal */
.modal {
    transition: all 0.2s ease !important;
}

/* Optimizaciones para elementos de spinner */
.spinner-border {
    animation-duration: 0.8s !important;
}

.spinner-grow {
    animation-duration: 0.8s !important;
}
