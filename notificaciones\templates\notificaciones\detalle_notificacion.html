{% extends 'Base/base.html' %}

{% block title %}Detalle de Notificación #{{ notificacion.id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="{{ notificacion.get_icono }} text-{{ notificacion.get_tipo_display_color }}"></i>
                        Notificación #{{ notificacion.id }}
                    </h2>
                    <p class="text-muted mb-0">Detalles y estadísticas de la notificación</p>
                </div>
                <div>
                    <a href="{% url 'notificaciones:lista_notificaciones' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/inicio/">
                    <i class="fas fa-home"></i> Inicio
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'notificaciones:lista_notificaciones' %}">Notificaciones</a>
            </li>
            <li class="breadcrumb-item active">Detalle #{{ notificacion.id }}</li>
        </ol>
    </nav>

    <!-- Información principal -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-{{ notificacion.get_tipo_display_color }} text-white">
                    <h5 class="mb-0">
                        <i class="{{ notificacion.get_icono }}"></i>
                        {% if notificacion.titulo %}
                            {{ notificacion.titulo }}
                        {% else %}
                            {{ notificacion.get_tipo_display }}
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted">Mensaje:</h6>
                        <p class="lead">{{ notificacion.mensaje }}</p>
                    </div>
                    
                    {% if notificacion.url_accion %}
                    <div class="mb-3">
                        <h6 class="text-muted">URL de Acción:</h6>
                        <a href="{{ notificacion.url_accion }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> {{ notificacion.url_accion }}
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if notificacion.ticket %}
                    <div class="mb-3">
                        <h6 class="text-muted">Ticket Relacionado:</h6>
                        <a href="#" class="btn btn-outline-info">
                            <i class="fas fa-ticket-alt"></i> Ticket #{{ notificacion.ticket.id }} - {{ notificacion.ticket.titulo }}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Información de creación -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Información
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5"><strong>ID:</strong></div>
                        <div class="col-sm-7">#{{ notificacion.id }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-5"><strong>Tipo:</strong></div>
                        <div class="col-sm-7">
                            <span class="badge bg-{{ notificacion.get_tipo_display_color }}">
                                {{ notificacion.get_tipo_display }}
                            </span>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-5"><strong>Creado por:</strong></div>
                        <div class="col-sm-7">
                            {% if notificacion.creado_por %}
                                {{ notificacion.creado_por.get_full_name|default:notificacion.creado_por.username }}
                            {% else %}
                                Sistema
                            {% endif %}
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-sm-5"><strong>Fecha:</strong></div>
                        <div class="col-sm-7">{{ notificacion.fecha_creacion|date:"d/m/Y H:i" }}</div>
                    </div>
                    <div class="row mb-0">
                        <div class="col-sm-5"><strong>Estado:</strong></div>
                        <div class="col-sm-7">
                            {% if notificacion.is_active %}
                                <span class="badge bg-success">Activa</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactiva</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Estadísticas de lectura -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie"></i> Estadísticas de Lectura
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="row">
                            <div class="col-4">
                                <h4 class="text-primary">{{ estadisticas.total_usuarios }}</h4>
                                <small class="text-muted">Total</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-success">{{ estadisticas.leidas }}</h4>
                                <small class="text-muted">Leídas</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-warning">{{ estadisticas.no_leidas }}</h4>
                                <small class="text-muted">No Leídas</small>
                            </div>
                        </div>
                    </div>
                    
                    {% if estadisticas.total_usuarios > 0 %}
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" 
                             style="width: {{ estadisticas.porcentaje_leidas }}%">
                            {{ estadisticas.porcentaje_leidas }}%
                        </div>
                    </div>
                    <small class="text-muted">Porcentaje de lectura</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Destinatarios -->
    <div class="row">
        <!-- Usuarios Notificados -->
        {% if usuarios_notificados %}
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users"></i> Usuarios Notificados ({{ usuarios_notificados.count }})
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Usuario</th>
                                    <th>Enviado</th>
                                    <th>Estado</th>
                                    <th>Leído</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for notif_usuario in usuarios_notificados %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ notif_usuario.usuario.get_full_name|default:notif_usuario.usuario.username }}</strong>
                                            {% if notif_usuario.usuario.cargo %}
                                                <br><small class="text-muted">{{ notif_usuario.usuario.cargo.nombre }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ notif_usuario.fecha_envio|date:"d/m H:i" }}</small>
                                    </td>
                                    <td>
                                        {% if notif_usuario.leida %}
                                            <span class="badge bg-success">Leída</span>
                                        {% else %}
                                            <span class="badge bg-warning">No Leída</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if notif_usuario.fecha_lectura %}
                                            <small>{{ notif_usuario.fecha_lectura|date:"d/m H:i" }}</small>
                                        {% else %}
                                            <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Grupos Notificados -->
        {% if grupos_notificados %}
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-layer-group"></i> Grupos Notificados ({{ grupos_notificados.count }})
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Grupo</th>
                                    <th>Miembros</th>
                                    <th>Enviado</th>
                                    <th>Individuales</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for notif_grupo in grupos_notificados %}
                                <tr>
                                    <td>
                                        <strong>{{ notif_grupo.grupo.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ notif_grupo.grupo.user_set.count }} usuarios
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ notif_grupo.fecha_envio|date:"d/m H:i" }}</small>
                                    </td>
                                    <td>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-primary"
                                                onclick="expandirGrupo({{ notif_grupo.grupo.id }})">
                                            <i class="fas fa-expand"></i> Ver
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Si no hay destinatarios -->
    {% if not usuarios_notificados and not grupos_notificados %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Sin Destinatarios</h5>
                    <p class="text-muted">Esta notificación no ha sido enviada a ningún usuario o grupo.</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal para expandir grupo -->
<div class="modal fade" id="grupoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-users"></i> Miembros del Grupo
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="grupo-miembros">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function expandirGrupo(grupoId) {
    const modal = new bootstrap.Modal(document.getElementById('grupoModal'));
    const modalBody = document.getElementById('grupo-miembros');
    
    // Mostrar loading
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Simular carga de miembros del grupo
    setTimeout(() => {
        modalBody.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Esta funcionalidad mostraría los miembros específicos del grupo y su estado de lectura.
            </div>
            <p>Aquí se implementaría una llamada AJAX para obtener los miembros del grupo ${grupoId} y mostrar:</p>
            <ul>
                <li>Lista de usuarios del grupo</li>
                <li>Estado de lectura de cada usuario</li>
                <li>Fecha de lectura</li>
                <li>Información adicional del usuario</li>
            </ul>
        `;
    }, 1000);
}

// Auto-actualizar estadísticas cada 30 segundos
setInterval(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
