{% for ticket in tickets %}
    <tr id="ticket-{{ ticket.id }}">
        <td>
            <span class="badge bg-primary">#{{ ticket.id }}</span>
        </td>
        <td>
            <div class="d-flex align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">
                        <a href="{% url 'tickets:detalle_ticket' ticket.id %}" class="text-decoration-none">
                            {{ ticket.titulo|truncatechars:50 }}
                        </a>
                    </h6>
                    <small class="text-muted">
                        {{ ticket.descripcion|truncatechars:80 }}
                    </small>
                    {% if ticket.get_ciudadano %}
                        <div class="mt-1">
                            <small class="text-info">
                                <i class="fas fa-user me-1"></i>{{ ticket.get_ciudadano.nombre_completo }}
                            </small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </td>
        <td>
            <span class="badge bg-{{ ticket.get_estado_display_color }}">
                {{ ticket.get_estado_display }}
            </span>
        </td>
        <td>
            <span class="badge bg-{{ ticket.get_prioridad_display_color }}">
                {{ ticket.get_prioridad_display|title }}
            </span>
        </td>
        <td>
            <span class="badge bg-secondary">
                {{ ticket.grupo.name }}
            </span>
        </td>
        <td>
            <small class="text-muted">
                {{ ticket.fecha_creacion|date:"d/m/Y" }}<br>
                <span class="text-xs">{{ ticket.fecha_creacion|time:"H:i" }}</span>
            </small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <a href="{% url 'tickets:detalle_ticket' ticket.id %}" 
                   class="btn btn-sm btn-outline-info" title="Ver detalles">
                    <i class="fas fa-eye"></i>
                </a>
                
                {% comment %}
                TODO: Implementar estas acciones cuando se completen las vistas
                
                {% if ticket.puede_ser_editado_por:user %}
                    <a href="{% url 'tickets:editar_ticket' ticket.id %}" 
                       class="btn btn-sm btn-outline-primary" title="Editar">
                        <i class="fas fa-edit"></i>
                    </a>
                {% endif %}
                
                {% if user|can_assign_ticket:ticket %}
                    <button type="button" class="btn btn-sm btn-outline-success" 
                            onclick="mostrarModalAsignar({{ ticket.id }})" title="Asignar">
                        <i class="fas fa-user-plus"></i>
                    </button>
                {% endif %}
                
                {% if user|can_change_status:ticket %}
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-warning dropdown-toggle" 
                                data-bs-toggle="dropdown" title="Cambiar estado">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="cambiarEstado({{ ticket.id }}, 1)">
                                <i class="fas fa-circle text-primary me-2"></i>Abierto
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="cambiarEstado({{ ticket.id }}, 2)">
                                <i class="fas fa-circle text-warning me-2"></i>En Progreso
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="cambiarEstado({{ ticket.id }}, 3)">
                                <i class="fas fa-circle text-success me-2"></i>Cerrado
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="cambiarEstado({{ ticket.id }}, 4)">
                                <i class="fas fa-circle text-secondary me-2"></i>Pendiente
                            </a></li>
                        </ul>
                    </div>
                {% endif %}
                {% endcomment %}
            </div>
        </td>
    </tr>
{% endfor %}

<style>
.text-xs {
    font-size: 0.75rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-weight: 500;
}

.table td {
    vertical-align: middle;
}

.table h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.table small {
    font-size: 0.8rem;
}

.dropdown-menu {
    min-width: 150px;
}

.dropdown-item {
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}
</style>

<script>
// TODO: Implementar estas funciones cuando se completen las vistas correspondientes

function mostrarModalAsignar(ticketId) {
    // Implementar modal de asignación
    console.log('Asignar ticket:', ticketId);
}

function cambiarEstado(ticketId, nuevoEstado) {
    // Implementar cambio de estado
    console.log('Cambiar estado del ticket', ticketId, 'a estado', nuevoEstado);
}
</script>
