/*
 * notificaciones.js
 * JavaScript para funcionalidad de notificaciones en tiempo real
 * 
 * Incluye:
 * - Actualización automática de contadores
 * - Notificaciones toast en tiempo real
 * - Funciones AJAX para marcar como leídas
 * - Gestión de estado de notificaciones
 */

class NotificacionesManager {
    constructor() {
        this.updateInterval = 30000; // 30 segundos
        this.intervalId = null;
        this.lastCount = 0;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startAutoUpdate();
        this.updateNotificationCount();
    }

    setupEventListeners() {
        // Event listener para marcar como leída via AJAX
        document.addEventListener('click', (e) => {
            if (e.target.matches('.marcar-leida-ajax')) {
                e.preventDefault();
                this.marcarComoLeida(e.target.dataset.notificacionId);
            }
        });

        // Event listener para marcar todas como leídas
        document.addEventListener('click', (e) => {
            if (e.target.matches('.marcar-todas-leidas')) {
                e.preventDefault();
                this.marcarTodasComoLeidas();
            }
        });

        // Event listener para mostrar notificaciones recientes
        document.addEventListener('click', (e) => {
            if (e.target.matches('.mostrar-notificaciones-recientes')) {
                e.preventDefault();
                this.mostrarNotificacionesRecientes();
            }
        });
    }

    startAutoUpdate() {
        this.intervalId = setInterval(() => {
            this.updateNotificationCount();
        }, this.updateInterval);
    }

    stopAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    async updateNotificationCount() {
        try {
            const response = await fetch('/notificaciones/ajax/contar-no-leidas/');

            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            const data = await response.json();

            // Validar estructura de respuesta
            if (!data || typeof data !== 'object' || typeof data.count !== 'number') {
                throw new Error('Respuesta del servidor inválida');
            }

            this.updateCounterElements(data.count);

            // Si hay nuevas notificaciones, mostrar toast
            if (data.count > this.lastCount && this.lastCount >= 0) {
                this.showNewNotificationToast(data.count - this.lastCount);
            }

            this.lastCount = data.count;
        } catch (error) {
            console.error('Error al actualizar contador de notificaciones:', error);
        }
    }

    updateCounterElements(count) {
        // Actualizar todos los elementos con clase 'notification-count'
        const counters = document.querySelectorAll('.notification-count');
        counters.forEach(counter => {
            counter.textContent = count;
            
            // Mostrar/ocultar badge según el count
            if (count > 0) {
                counter.classList.remove('d-none');
                counter.classList.add('badge', 'bg-danger');
            } else {
                counter.classList.add('d-none');
            }
        });

        // Actualizar elementos con clase 'notification-indicator'
        const indicators = document.querySelectorAll('.notification-indicator');
        indicators.forEach(indicator => {
            if (count > 0) {
                indicator.classList.add('has-notifications');
            } else {
                indicator.classList.remove('has-notifications');
            }
        });
    }

    async marcarComoLeida(notificacionId) {
        try {
            // Validar que el ID sea numérico
            if (!notificacionId || isNaN(parseInt(notificacionId))) {
                throw new Error('ID de notificación inválido');
            }

            const csrfToken = this.getCSRFToken();

            const formData = new FormData();
            formData.append('notificacion_id', parseInt(notificacionId));
            formData.append('csrfmiddlewaretoken', csrfToken);

            const response = await fetch('/notificaciones/ajax/marcar-leida/', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            const data = await response.json();

            // Validar estructura de respuesta
            if (!data || typeof data !== 'object') {
                throw new Error('Respuesta del servidor inválida');
            }

            if (data.success === true) {
                this.showSuccessToast('Notificación marcada como leída');
                this.updateNotificationCount();

                // Actualizar elemento en la interfaz
                const element = document.querySelector(`[data-notificacion-id="${notificacionId}"]`);
                if (element) {
                    element.classList.remove('notification-unread');
                    element.classList.add('notification-read');

                    // Remover badge "NUEVA" si existe
                    const badge = element.querySelector('.badge-nueva, .badge.bg-warning');
                    if (badge) badge.remove();

                    // Remover botón "Marcar Leída" si existe
                    const button = element.querySelector('.marcar-leida-ajax');
                    if (button) button.remove();

                    // Si estamos en el dashboard, recargar las notificaciones
                    if (typeof cargarNotificacionesDashboard === 'function') {
                        setTimeout(() => {
                            cargarNotificacionesDashboard();
                        }, 1000);
                    }
                }
            } else {
                this.showErrorToast(data.message || 'Error al marcar como leída');
            }
        } catch (error) {
            console.error('Error al marcar como leída:', error);
            this.showErrorToast('Error de conexión');
        }
    }

    async marcarTodasComoLeidas() {
        try {
            const csrfToken = this.getCSRFToken();

            const response = await fetch('/notificaciones/marcar-todas-leidas/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            });

            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            this.showSuccessToast('Todas las notificaciones marcadas como leídas');
            this.updateNotificationCount();

            // Actualizar interfaz
            const unreadElements = document.querySelectorAll('.notification-unread');
            unreadElements.forEach(element => {
                element.classList.remove('notification-unread');
                element.classList.add('notification-read');
            });

            // Remover todos los badges "NUEVA"
            const badges = document.querySelectorAll('.badge-nueva, .badge.bg-warning');
            badges.forEach(badge => badge.remove());

            // Remover todos los botones "Marcar Leída"
            const buttons = document.querySelectorAll('.marcar-leida-ajax');
            buttons.forEach(button => button.remove());

        } catch (error) {
            console.error('Error al marcar todas como leídas:', error);
            this.showErrorToast('Error de conexión');
        }
    }

    async mostrarNotificacionesRecientes() {
        try {
            const response = await fetch('/notificaciones/ajax/obtener-recientes/?limit=5');
            const data = await response.json();
            
            this.renderNotificacionesDropdown(data.notificaciones);
        } catch (error) {
            console.error('Error al obtener notificaciones recientes:', error);
        }
    }

    // Función para escapar HTML y prevenir XSS
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Función para validar y sanitizar iconos CSS
    sanitizeIcon(icon) {
        if (!icon || typeof icon !== 'string') return 'fas fa-bell';
        // Solo permitir clases de Font Awesome válidas
        const validIconPattern = /^(fas|far|fab|fal|fad)\s+fa-[a-z0-9-]+$/;
        return validIconPattern.test(icon.trim()) ? icon.trim() : 'fas fa-bell';
    }

    // Función para validar colores CSS
    sanitizeColor(color) {
        if (!color || typeof color !== 'string') return 'primary';
        // Solo permitir colores Bootstrap válidos
        const validColors = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark'];
        return validColors.includes(color.trim()) ? color.trim() : 'primary';
    }

    renderNotificacionesDropdown(notificaciones) {
        const dropdown = document.getElementById('notificaciones-dropdown');
        if (!dropdown) return;

        if (notificaciones.length === 0) {
            dropdown.innerHTML = `
                <li class="dropdown-item-text text-center text-muted py-3">
                    <i class="fas fa-inbox"></i><br>
                    No hay notificaciones
                </li>
            `;
            return;
        }

        // Limpiar contenido anterior
        dropdown.innerHTML = '';

        notificaciones.forEach(notif => {
            // Validar y sanitizar datos
            const notifId = parseInt(notif.id);
            if (isNaN(notifId)) return; // Saltar notificaciones con ID inválido

            const isUnread = !notif.leida ? 'fw-bold' : '';
            const titulo = this.escapeHtml(notif.titulo || notif.tipo_display || 'Sin título');
            const mensaje = this.escapeHtml((notif.mensaje || '').substring(0, 50));
            const fechaEnvio = this.escapeHtml(notif.fecha_envio || '');
            const icono = this.sanitizeIcon(notif.icono);
            const tipoColor = this.sanitizeColor(notif.tipo_color);

            // Crear elementos DOM de forma segura
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = `dropdown-item ${isUnread}`;
            a.href = '#';
            a.setAttribute('data-notificacion-id', notifId);

            // Usar addEventListener en lugar de onclick
            a.addEventListener('click', (e) => {
                e.preventDefault();
                this.marcarComoLeida(notifId);
            });

            const flexDiv = document.createElement('div');
            flexDiv.className = 'd-flex align-items-start';

            const iconElement = document.createElement('i');
            iconElement.className = `${icono} text-${tipoColor} me-2 mt-1`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'flex-grow-1';

            const titleDiv = document.createElement('div');
            titleDiv.className = 'fw-bold';
            titleDiv.textContent = titulo;

            const messageDiv = document.createElement('div');
            messageDiv.className = 'text-muted small';
            messageDiv.textContent = mensaje + (mensaje.length >= 50 ? '...' : '');

            const dateDiv = document.createElement('div');
            dateDiv.className = 'text-muted small';
            const clockIcon = document.createElement('i');
            clockIcon.className = 'fas fa-clock';
            dateDiv.appendChild(clockIcon);
            dateDiv.appendChild(document.createTextNode(' ' + fechaEnvio));

            contentDiv.appendChild(titleDiv);
            contentDiv.appendChild(messageDiv);
            contentDiv.appendChild(dateDiv);

            flexDiv.appendChild(iconElement);
            flexDiv.appendChild(contentDiv);

            // Agregar badge si no está leída
            if (!notif.leida) {
                const badge = document.createElement('span');
                badge.className = 'badge bg-warning ms-2';
                badge.textContent = 'NUEVA';
                flexDiv.appendChild(badge);
            }

            a.appendChild(flexDiv);
            li.appendChild(a);
            dropdown.appendChild(li);
        });

        // Agregar separador y enlace final de forma segura
        const separatorLi = document.createElement('li');
        const hr = document.createElement('hr');
        hr.className = 'dropdown-divider';
        separatorLi.appendChild(hr);
        dropdown.appendChild(separatorLi);

        const linkLi = document.createElement('li');
        const linkA = document.createElement('a');
        linkA.className = 'dropdown-item text-center';
        linkA.href = '/notificaciones/mis-notificaciones/';

        const eyeIcon = document.createElement('i');
        eyeIcon.className = 'fas fa-eye';
        linkA.appendChild(eyeIcon);
        linkA.appendChild(document.createTextNode(' Ver todas las notificaciones'));

        linkLi.appendChild(linkA);
        dropdown.appendChild(linkLi);
    }

    showNewNotificationToast(count) {
        const toast = this.createToast(
            'Nueva Notificación',
            `Tiene ${count} nueva(s) notificación(es)`,
            'primary',
            'fas fa-bell'
        );
        
        this.showToast(toast);
    }

    showSuccessToast(message) {
        Swal.fire({
            title: '¡Éxito!',
            text: message,
            icon: 'success',
            timer: 2000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    }

    showErrorToast(message) {
        Swal.fire({
            title: 'Error',
            text: message,
            icon: 'error',
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    }

    createToast(title, message, type, icon) {
        const toastId = 'toast-' + Date.now();
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = 'toast';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="toast-header bg-${type} text-white">
                <i class="${icon} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        return toast;
    }

    showToast(toast) {
        // Crear contenedor de toasts si no existe
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        container.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 5000
        });
        
        bsToast.show();
        
        // Remover el toast del DOM después de ocultarlo
        toast.addEventListener('hidden.bs.toast', () => {
            container.removeChild(toast);
        });
    }

    getCSRFToken() {
        // Buscar token CSRF en diferentes lugares
        let token = document.querySelector('[name=csrfmiddlewaretoken]');
        if (token && token.value) return token.value;

        // Buscar en meta tag
        token = document.querySelector('meta[name="csrf-token"]');
        if (token && token.getAttribute('content')) return token.getAttribute('content');

        // Buscar en cookies
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken' && value) return value;
        }

        // Token CSRF es obligatorio - lanzar error si no se encuentra
        throw new Error('Token CSRF no encontrado. La página debe recargarse.');
    }

    // Método para integrar con el sidebar
    initSidebarIntegration() {
        const notificationBell = document.querySelector('.notification-bell');
        if (notificationBell) {
            notificationBell.addEventListener('click', (e) => {
                e.preventDefault();
                this.mostrarNotificacionesRecientes();
            });
        }
    }

    // Método para pausar actualizaciones cuando la página no está visible
    handleVisibilityChange() {
        if (document.hidden) {
            this.stopAutoUpdate();
        } else {
            this.startAutoUpdate();
            this.updateNotificationCount();
        }
    }
}

// Inicializar el manager cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    window.notificacionesManager = new NotificacionesManager();
    
    // Manejar cambios de visibilidad de la página
    document.addEventListener('visibilitychange', () => {
        window.notificacionesManager.handleVisibilityChange();
    });
    
    // Integrar con el sidebar si existe
    window.notificacionesManager.initSidebarIntegration();
});

// Limpiar intervalos cuando se cierre la página
window.addEventListener('beforeunload', function() {
    if (window.notificacionesManager) {
        window.notificacionesManager.stopAutoUpdate();
    }
});
