"""
Base/management/commands/actualizar_cargos.py
Comando para actualizar la lógica de cargos y grupos automáticos.

Uso: python manage.py actualizar_cargos
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from user.models import User, CargoUsuario


class Command(BaseCommand):
    help = 'Actualiza la lógica de cargos y asigna grupos automáticamente'

    def handle(self, *args, **options):
        """
        Actualiza todos los usuarios existentes para que tengan
        los grupos correctos según su cargo.
        """
        self.stdout.write(
            self.style.SUCCESS('Iniciando actualización de cargos y grupos...')
        )

        # Mapeo de cargos a grupos
        mapeo_cargos = {
            'administrador': 'Admin',
            'admin': 'Admin',
            'secretaria': 'Secretaria',
            'supervisor': 'Admin',  # Los supervisores tienen permisos de Admin
            'empleado': 'Empleado',
        }

        usuarios_actualizados = 0
        usuarios_sin_cargo = 0

        # Procesar todos los usuarios activos
        for usuario in User.objects.filter(is_active=True):
            if usuario.cargo:
                cargo_nombre = usuario.cargo.nombre.lower()
                grupo_nombre = mapeo_cargos.get(cargo_nombre)
                
                if grupo_nombre:
                    try:
                        grupo = Group.objects.get(name=grupo_nombre)
                        
                        # Limpiar grupos actuales y asignar el correcto
                        usuario.groups.clear()
                        usuario.groups.add(grupo)
                        
                        # Asignar is_supervisor automáticamente
                        usuario.is_supervisor = cargo_nombre in ['supervisor', 'administrador']
                        usuario.save()
                        
                        usuarios_actualizados += 1
                        self.stdout.write(f'✓ Usuario {usuario.username}: {cargo_nombre} → {grupo_nombre}')
                        
                    except Group.DoesNotExist:
                        self.stdout.write(
                            self.style.ERROR(f'✗ Grupo "{grupo_nombre}" no encontrado para {usuario.username}')
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'⚠ Cargo "{cargo_nombre}" no mapeado para {usuario.username}')
                    )
            else:
                usuarios_sin_cargo += 1
                self.stdout.write(
                    self.style.WARNING(f'⚠ Usuario {usuario.username} sin cargo asignado')
                )

        # Resumen
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS(f'✓ Usuarios actualizados: {usuarios_actualizados}')
        )
        self.stdout.write(
            self.style.WARNING(f'⚠ Usuarios sin cargo: {usuarios_sin_cargo}')
        )
        
        # Mostrar estadísticas por grupo
        self.stdout.write('\nEstadísticas por grupo:')
        for grupo in Group.objects.all():
            count = grupo.user_set.filter(is_active=True).count()
            self.stdout.write(f'  {grupo.name}: {count} usuarios')

        self.stdout.write(
            self.style.SUCCESS('\n¡Actualización completada exitosamente!')
        )
