{% extends 'reportes/base_reportes.html' %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Historial</li>
{% endblock %}

{% block page_title %}Historial de Reportes{% endblock %}
{% block page_description %}Consulte el historial de reportes generados{% endblock %}

{% block report_content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>Reportes Generados Recientemente
                </h5>
            </div>
            <div class="card-body">
                {% if reportes %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th><i class="fas fa-calendar me-1"></i>Fecha</th>
                                <th><i class="fas fa-file me-1"></i>Tipo</th>
                                <th><i class="fas fa-file-export me-1"></i>Formato</th>
                                <th><i class="fas fa-clock me-1"></i>Período</th>
                                <th><i class="fas fa-user me-1"></i>Generado por</th>
                                <th><i class="fas fa-info me-1"></i>Detalles</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for reporte in reportes %}
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ reporte.fecha_generacion|date:"d/m/Y" }}</span>
                                    <br>
                                    <small class="text-muted">{{ reporte.fecha_generacion|date:"H:i" }}</small>
                                </td>
                                <td>
                                    {% if reporte.tipo_reporte == 'empleado' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-user-tie me-1"></i>Por Empleado
                                        </span>
                                    {% elif reporte.tipo_reporte == 'area' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-building me-1"></i>Por Área
                                        </span>
                                    {% elif reporte.tipo_reporte == 'ciudadano' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-users me-1"></i>Por Ciudadano
                                        </span>
                                    {% elif reporte.tipo_reporte == 'general' %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-chart-bar me-1"></i>General
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if reporte.formato == 'pdf' %}
                                        <i class="fas fa-file-pdf text-danger me-1"></i>PDF
                                    {% else %}
                                        <i class="fas fa-file-excel text-success me-1"></i>Excel
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ reporte.get_rango_fechas_display }}</small>
                                </td>
                                <td>
                                    <strong>{{ reporte.generado_por.get_full_name|default:reporte.generado_por.username }}</strong>
                                    <br>
                                    <small class="text-muted">{{ reporte.generado_por.username }}</small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" 
                                            onclick="mostrarDetalles({{ reporte.id }}, '{{ reporte.tipo_reporte }}', {{ reporte.entidades_incluidas|safe }})">
                                        <i class="fas fa-eye me-1"></i>Ver
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox text-muted" style="font-size: 4rem;"></i>
                    <h5 class="text-muted mt-3">No hay reportes generados</h5>
                    <p class="text-muted">Aún no se han generado reportes en el sistema.</p>
                    <a href="{% url 'reportes:index' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Generar Primer Reporte
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Estadísticas rápidas -->
{% if reportes %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-chart-line text-primary" style="font-size: 2rem;"></i>
                <h4 class="mt-2">{{ reportes|length }}</h4>
                <p class="text-muted mb-0">Reportes Recientes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-file-pdf text-danger" style="font-size: 2rem;"></i>
                <h4 class="mt-2">
                    {% with pdf_count=reportes|length %}
                        {% for reporte in reportes %}
                            {% if reporte.formato == 'pdf' %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    {% endwith %}
                </h4>
                <p class="text-muted mb-0">Reportes PDF</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-file-excel text-success" style="font-size: 2rem;"></i>
                <h4 class="mt-2">
                    {% with excel_count=0 %}
                        {% for reporte in reportes %}
                            {% if reporte.formato == 'excel' %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    {% endwith %}
                </h4>
                <p class="text-muted mb-0">Reportes Excel</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-users text-warning" style="font-size: 2rem;"></i>
                <h4 class="mt-2">
                    {% regroup reportes by generado_por as reportes_por_usuario %}
                    {{ reportes_por_usuario|length }}
                </h4>
                <p class="text-muted mb-0">Usuarios Activos</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Botones de acción -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="{% url 'reportes:index' %}" class="btn btn-primary me-3">
            <i class="fas fa-plus me-2"></i>Generar Nuevo Reporte
        </a>
        <button class="btn btn-outline-secondary" onclick="location.reload()">
            <i class="fas fa-sync-alt me-2"></i>Actualizar
        </button>
    </div>
</div>
{% endblock %}

{% block extra_report_js %}
<script>
function mostrarDetalles(reporteId, tipoReporte, entidadesIncluidas) {
    let contenidoDetalle = '<div class="text-left">';
    contenidoDetalle += '<h6><i class="fas fa-info-circle text-primary"></i> Detalles del Reporte</h6>';
    contenidoDetalle += '<p><strong>ID:</strong> ' + reporteId + '</p>';
    contenidoDetalle += '<p><strong>Tipo:</strong> ' + getTipoReporteDisplay(tipoReporte) + '</p>';
    
    if (entidadesIncluidas && Object.keys(entidadesIncluidas).length > 0) {
        contenidoDetalle += '<h6 class="mt-3"><i class="fas fa-list text-info"></i> Entidades Incluidas</h6>';
        
        if (entidadesIncluidas.empleados && entidadesIncluidas.empleados.length > 0) {
            contenidoDetalle += '<p><strong>Empleados:</strong> ' + entidadesIncluidas.empleados.length + ' seleccionados</p>';
        }
        
        if (entidadesIncluidas.areas && entidadesIncluidas.areas.length > 0) {
            contenidoDetalle += '<p><strong>Áreas:</strong> ' + entidadesIncluidas.areas.length + ' seleccionadas</p>';
        }
        
        if (entidadesIncluidas.ciudadanos && entidadesIncluidas.ciudadanos.length > 0) {
            contenidoDetalle += '<p><strong>Ciudadanos:</strong> ' + entidadesIncluidas.ciudadanos.length + ' seleccionados</p>';
        }
    }
    
    contenidoDetalle += '</div>';
    
    Swal.fire({
        title: 'Detalles del Reporte',
        html: contenidoDetalle,
        width: 500,
        confirmButtonColor: '#1A237E',
        confirmButtonText: 'Cerrar'
    });
}

function getTipoReporteDisplay(tipo) {
    const tipos = {
        'empleado': 'Por Empleado',
        'area': 'Por Área',
        'ciudadano': 'Por Ciudadano',
        'general': 'General'
    };
    return tipos[tipo] || tipo;
}
</script>
{% endblock %}
