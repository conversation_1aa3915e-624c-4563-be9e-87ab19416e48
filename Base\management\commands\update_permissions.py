"""
Comando para actualizar permisos de grupos automáticamente.
Este comando se puede ejecutar en cualquier entorno (desarrollo, producción)
para asegurar que los permisos estén correctos.

IMPORTANTE: Este comando debe ejecutarse en producción después de hacer deploy
para asegurar que las secretarias tengan permisos de notificaciones.

Uso:
    python manage.py update_permissions
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.db import transaction
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Actualiza los permisos de todos los grupos automáticamente'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Fuerza la actualización incluso si los grupos ya existen',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔄 Actualizando permisos de grupos...')
        )
        
        # Definir permisos para cada grupo
        group_permissions = {
            'Admin': [
                # Tickets
                'add_ticket', 'view_ticket', 'change_ticket', 'delete_ticket',
                # Ciudadanos
                'add_ciudadano', 'view_ciudadano', 'change_ciudadano', 'delete_ciudadano',
                'add_ciudadanoticket', 'view_ciudadanoticket',
                # Asignaciones
                'add_asignacionticket', 'view_asignacionticket', 'change_asignacionticket', 'delete_asignacionticket',
                # Notificaciones
                'add_notificacion', 'view_notificacion', 'change_notificacion', 'delete_notificacion',
                'add_notificacionusuario', 'view_notificacionusuario', 'change_notificacionusuario',
                'add_notificaciongrupo', 'view_notificaciongrupo', 'change_notificaciongrupo',
                # Usuarios
                'add_user', 'view_user', 'change_user', 'delete_user',
            ],
            
            'Secretaria': [
                # Tickets
                'add_ticket', 'view_ticket', 'change_ticket',
                # Ciudadanos
                'add_ciudadano', 'view_ciudadano', 'change_ciudadano',
                'add_ciudadanoticket', 'view_ciudadanoticket',
                # Asignaciones (solo crear y ver)
                'add_asignacionticket', 'view_asignacionticket',
                # Notificaciones (NUEVO: crear y gestionar)
                'add_notificacion', 'view_notificacion', 'change_notificacion',
                'add_notificacionusuario', 'view_notificacionusuario',
                'add_notificaciongrupo', 'view_notificaciongrupo',
            ],
            
            'Supervisor': [
                # Tickets
                'view_ticket', 'change_ticket',
                # Ciudadanos
                'view_ciudadano', 'view_ciudadanoticket',
                # Asignaciones
                'add_asignacionticket', 'view_asignacionticket', 'change_asignacionticket',
                # Notificaciones
                'add_notificacion', 'view_notificacion', 'change_notificacion',
                'add_notificacionusuario', 'view_notificacionusuario',
                'add_notificaciongrupo', 'view_notificaciongrupo',
            ],
            
            'Empleado': [
                # Tickets (solo ver y cambiar estado)
                'view_ticket', 'change_ticket',
                # Asignaciones (ver y cambiar las propias)
                'view_asignacionticket', 'change_asignacionticket',
                # Ciudadanos (solo ver)
                'view_ciudadano', 'view_ciudadanoticket',
                # Notificaciones (solo ver)
                'view_notificacion', 'view_notificacionusuario',
            ],
        }
        
        total_updated = 0
        
        try:
            with transaction.atomic():
                # Actualizar permisos para cada grupo
                for group_name, permission_codenames in group_permissions.items():
                    try:
                        group, created = Group.objects.get_or_create(name=group_name)
                        
                        if created:
                            self.stdout.write(f"✅ Grupo '{group_name}' creado")
                        
                        # Obtener permisos existentes
                        permissions = Permission.objects.filter(codename__in=permission_codenames)
                        
                        # Asignar permisos al grupo
                        group.permissions.set(permissions)
                        
                        self.stdout.write(
                            self.style.SUCCESS(f"✅ {group_name}: {permissions.count()} permisos actualizados")
                        )
                        total_updated += 1
                        
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"❌ Error actualizando grupo {group_name}: {e}")
                        )
                        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error general en actualización de permisos: {e}")
            )
            return
            
        self.stdout.write(
            self.style.SUCCESS(f'🎯 Actualización completada. {total_updated} grupos actualizados.')
        )
        
        # Mostrar resumen de cambios importantes
        self.stdout.write('\n📋 Cambios importantes aplicados:')
        self.stdout.write('   • Secretarias ahora pueden crear notificaciones a usuarios y grupos')
        self.stdout.write('   • Supervisores mantienen permisos de notificaciones')
        self.stdout.write('   • Empleados solo pueden ver notificaciones')
        self.stdout.write('   • Administradores tienen permisos completos')
        
        self.stdout.write(
            self.style.WARNING('\n⚠️  IMPORTANTE: Ejecuta este comando en producción después del deploy.')
        )
