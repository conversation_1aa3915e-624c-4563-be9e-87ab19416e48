from django.contrib import admin
from .models import Ticket, HistorialTicket, TicketImagen


class TicketImagenInline(admin.TabularInline):
    """
    Inline para mostrar imágenes en el admin de tickets.
    """
    model = TicketImagen
    extra = 0
    readonly_fields = ('tamaño_original', 'tamaño_comprimido', 'ancho_original', 'alto_original', 'fecha_subida')
    fields = ('imagen', 'descripcion', 'orden', 'is_active', 'tamaño_original', 'tamaño_comprimido')


@admin.register(Ticket)
class TicketAdmin(admin.ModelAdmin):
    """
    Configuración del admin para Tickets.
    """
    list_display = ('id', 'titulo', 'estado', 'prioridad', 'grupo', 'creado_por', 'fecha_creacion', 'token')
    list_filter = ('estado', 'prioridad', 'grupo', 'fecha_creacion')
    search_fields = ('titulo', 'descripcion', 'token')
    readonly_fields = ('token', 'fecha_creacion', 'fecha_actualizacion', 'fecha_finalizacion')
    inlines = [TicketImagenInline]

    fieldsets = (
        ('Información Básica', {
            'fields': ('titulo', 'descripcion', 'estado', 'prioridad')
        }),
        ('Asignación', {
            'fields': ('grupo', 'creado_por')
        }),
        ('Ubicación', {
            'fields': ('direccion',)
        }),
        ('Fechas', {
            'fields': ('fecha_creacion', 'fecha_actualizacion', 'fecha_finalizacion'),
            'classes': ('collapse',)
        }),
        ('Control', {
            'fields': ('is_active', 'token'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TicketImagen)
class TicketImagenAdmin(admin.ModelAdmin):
    """
    Configuración del admin para imágenes de tickets.
    """
    list_display = ('id', 'ticket', 'descripcion', 'orden', 'get_tamaño_legible', 'fecha_subida', 'is_active')
    list_filter = ('fecha_subida', 'is_active')
    search_fields = ('ticket__titulo', 'descripcion')
    readonly_fields = ('tamaño_original', 'tamaño_comprimido', 'ancho_original', 'alto_original', 'fecha_subida')

    fieldsets = (
        ('Imagen', {
            'fields': ('ticket', 'imagen', 'descripcion', 'orden')
        }),
        ('Metadatos', {
            'fields': ('tamaño_original', 'tamaño_comprimido', 'ancho_original', 'alto_original'),
            'classes': ('collapse',)
        }),
        ('Control', {
            'fields': ('is_active', 'fecha_subida', 'subida_por'),
            'classes': ('collapse',)
        }),
    )


@admin.register(HistorialTicket)
class HistorialTicketAdmin(admin.ModelAdmin):
    """
    Configuración del admin para historial de tickets.
    """
    list_display = ('id', 'ticket', 'usuario', 'accion', 'fecha')
    list_filter = ('fecha', 'accion')
    search_fields = ('ticket__titulo', 'usuario__username', 'accion')
    readonly_fields = ('fecha',)
