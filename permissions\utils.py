"""
permissions/utils.py
Utilidades auxiliares para el sistema de permisos.

Contiene funciones helper y utilidades que complementan
el sistema centralizado de permisos.
"""

from django.db.models import Q
from .core import PermissionHelper


def filter_tickets_for_user(queryset, user):
    """
    Filtra tickets según los permisos del usuario.
    
    Args:
        queryset: QuerySet de tickets
        user: Usuario para filtrar
        
    Returns:
        QuerySet: Tickets filtrados según permisos
    """
    if not user.is_authenticated:
        return queryset.none()
    
    # Admin ve todos los tickets
    if PermissionHelper.is_admin(user):
        return queryset.filter(is_active=True)
    
    # Secretaria ve tickets que creó
    if PermissionHelper.is_secretaria(user):
        return queryset.filter(
            creado_por=user,
            is_active=True
        )
    
    # Supervisor ve tickets de su grupo
    if PermissionHelper.is_supervisor(user):
        user_groups = user.groups.all()
        return queryset.filter(
            grupo__in=user_groups,
            is_active=True
        )
    
    # Empleado ve tickets asignados a él
    return queryset.filter(
        asignaciones__usuario=user,
        asignaciones__is_active=True,
        is_active=True
    ).distinct()


def filter_users_for_user(queryset, user):
    """
    Filtra usuarios según los permisos del usuario que consulta.
    
    Args:
        queryset: QuerySet de usuarios
        user: Usuario que hace la consulta
        
    Returns:
        QuerySet: Usuarios filtrados según permisos
    """
    if not user.is_authenticated:
        return queryset.none()
    
    # Admin ve todos los usuarios
    if PermissionHelper.is_admin(user):
        return queryset.filter(is_active=True)
    
    # Supervisor ve usuarios de su área
    if PermissionHelper.is_supervisor(user):
        user_areas = PermissionHelper.get_user_areas(user)
        return queryset.filter(
            groups__in=user_areas,
            is_active=True
        ).distinct()
    
    # Otros usuarios solo se ven a sí mismos
    return queryset.filter(id=user.id)


def filter_notifications_for_user(queryset, user):
    """
    Filtra notificaciones según los permisos del usuario.
    
    Args:
        queryset: QuerySet de notificaciones
        user: Usuario para filtrar
        
    Returns:
        QuerySet: Notificaciones filtradas según permisos
    """
    if not user.is_authenticated:
        return queryset.none()
    
    # Admin ve todas las notificaciones
    if PermissionHelper.is_admin(user):
        return queryset.all()
    
    # Supervisor ve notificaciones que creó o de su área
    if PermissionHelper.is_supervisor(user):
        user_groups = user.groups.all()
        return queryset.filter(
            Q(creado_por=user) |
            Q(grupos__in=user_groups)
        ).distinct()
    
    # Otros usuarios solo ven notificaciones dirigidas a ellos
    user_groups = user.groups.all()
    return queryset.filter(
        Q(usuarios=user) |
        Q(grupos__in=user_groups)
    ).distinct()


def get_assignable_users_for_user(user, ticket=None):
    """
    Obtiene los usuarios a los que el usuario actual puede asignar tickets.
    
    Args:
        user: Usuario que quiere asignar
        ticket: Ticket a asignar (opcional, para filtrar por área)
        
    Returns:
        QuerySet: Usuarios a los que puede asignar
    """
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    if not user.is_authenticated:
        return User.objects.none()
    
    # Admin puede asignar a cualquier usuario activo
    if PermissionHelper.is_admin(user):
        return User.objects.filter(is_active=True)
    
    # Secretaria puede asignar a grupos (no usuarios individuales)
    # Esta función no aplica para secretarias
    if PermissionHelper.is_secretaria(user):
        return User.objects.none()
    
    # Supervisor puede asignar a usuarios de su área
    if PermissionHelper.is_supervisor(user):
        user_areas = PermissionHelper.get_user_areas(user)
        assignable_users = User.objects.filter(
            groups__in=user_areas,
            is_active=True
        ).distinct()
        
        # Si hay un ticket específico, filtrar por el área del ticket
        if ticket and hasattr(ticket, 'grupo'):
            assignable_users = assignable_users.filter(
                groups=ticket.grupo
            )
        
        return assignable_users
    
    # Empleados no pueden asignar tickets
    return User.objects.none()


def get_user_dashboard_context(user):
    """
    Obtiene el contexto específico para el dashboard según el rol del usuario.
    
    Args:
        user: Usuario para el que generar el contexto
        
    Returns:
        dict: Contexto específico del dashboard
    """
    if not user.is_authenticated:
        return {}
    
    base_context = {
        'user_role': PermissionHelper.get_user_role(user),
        'user_areas': list(PermissionHelper.get_user_areas(user).values_list('name', flat=True)),
        'permissions': PermissionHelper.get_sidebar_permissions(user),
        'quick_access': PermissionHelper.get_quick_access_permissions(user),
    }
    
    # Contexto específico por rol
    if PermissionHelper.is_admin(user):
        base_context.update({
            'dashboard_type': 'admin',
            'show_system_metrics': True,
            'show_all_areas': True,
        })
    elif PermissionHelper.is_supervisor(user):
        base_context.update({
            'dashboard_type': 'supervisor',
            'show_area_metrics': True,
            'managed_areas': list(PermissionHelper.get_user_areas(user).values_list('name', flat=True)),
        })
    elif PermissionHelper.is_secretaria(user):
        base_context.update({
            'dashboard_type': 'secretaria',
            'show_citizen_metrics': True,
            'show_ticket_creation': True,
        })
    else:  # Empleado
        base_context.update({
            'dashboard_type': 'empleado',
            'show_personal_metrics': True,
            'show_assigned_tickets': True,
        })
    
    return base_context


def check_ticket_access(user, ticket):
    """
    Verifica si un usuario tiene acceso a un ticket específico.
    
    Args:
        user: Usuario a verificar
        ticket: Ticket a verificar
        
    Returns:
        bool: True si tiene acceso, False en caso contrario
    """
    if not user.is_authenticated:
        return False
    
    # Admin puede ver cualquier ticket
    if PermissionHelper.is_admin(user):
        return True
    
    # Secretaria puede ver tickets que creó
    if PermissionHelper.is_secretaria(user) and ticket.creado_por == user:
        return True
    
    # Supervisor puede ver tickets de su área
    if PermissionHelper.is_supervisor(user):
        user_areas = PermissionHelper.get_user_areas(user)
        if user_areas.filter(id=ticket.grupo.id).exists():
            return True
    
    # Empleado puede ver tickets asignados a él
    if ticket.asignaciones.filter(usuario=user, is_active=True).exists():
        return True
    
    return False


def get_role_display_name(user):
    """
    Obtiene el nombre de display del rol del usuario.
    
    Args:
        user: Usuario a verificar
        
    Returns:
        str: Nombre del rol para mostrar en la interfaz
    """
    role = PermissionHelper.get_user_role(user)
    
    role_names = {
        'Admin': 'Administrador',
        'Supervisor': 'Supervisor',
        'Secretaria': 'Secretaria',
        'Empleado': 'Empleado',
        'Sin rol': 'Sin rol asignado',
        'Sin autenticar': 'No autenticado'
    }
    
    return role_names.get(role, role)
