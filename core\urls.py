"""
URL configuration for tickets project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import include, path
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path("admin/", admin.site.urls),
    # Autenticación
    path('', include('login_app.urls')),
    # Dashboard principal
    path('inicio/', include('Home.urls')),
    # Sistema base (demo y testing)
    path('base/', include('Base.urls')),
    # Gestión de usuarios
    path('usuarios/', include('user.urls')),
    # Sistema de tickets
    path('tickets/', include('tickets.urls')),
    # Gestión de ciudadanos
    path('ciudadanos/', include('ciudadano.urls')),
    # Sistema de notificaciones
    path('notificaciones/', include('notificaciones.urls')),
    # Gestión de asignaciones
    path('asignaciones/', include('asignaciones.urls')),
    # Sistema de reportes
    path('reportes/', include('reportes.urls')),
    # Consulta pública de tickets (sin autenticación)
    path('', include('public_tickets.urls')),
]

# Configurar handlers de error personalizados
from Base.error_handlers import (
    permission_denied_view,
    page_not_found_view,
    server_error_view,
    bad_request_view
)

handler400 = bad_request_view
handler403 = permission_denied_view
handler404 = page_not_found_view
handler500 = server_error_view

# Servir archivos multimedia en desarrollo
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
