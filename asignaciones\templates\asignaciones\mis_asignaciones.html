{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Mis Asignaciones{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header personalizado -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Mis Asignaciones</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ usuario.get_full_name|default:usuario.username }}
                                    <span class="ms-3">
                                        <i class="fas fa-tasks me-1"></i>{{ stats.total_asignaciones }} asignación{{ stats.total_asignaciones|pluralize:"es" }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'asignaciones:lista_asignaciones' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-2"></i>Ver Todas
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas personales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                    <h4 class="text-info">{{ stats.asignadas }}</h4>
                    <small class="text-muted">Asignadas</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                    <h4 class="text-warning">{{ stats.en_progreso }}</h4>
                    <small class="text-muted">En Progreso</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="text-success">{{ stats.finalizadas }}</h4>
                    <small class="text-muted">Finalizadas</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                    <h4 class="text-primary">{{ stats.total_asignaciones }}</h4>
                    <small class="text-muted">Total</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros simples -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="estado" class="form-label">Filtrar por Estado</label>
                            <select class="form-select" id="estado" name="estado">
                                <option value="">Todos los estados</option>
                                <option value="1" {% if estado_filtro == "1" %}selected{% endif %}>Asignado</option>
                                <option value="2" {% if estado_filtro == "2" %}selected{% endif %}>En Progreso</option>
                                <option value="3" {% if estado_filtro == "3" %}selected{% endif %}>Finalizado</option>
                                <option value="4" {% if estado_filtro == "4" %}selected{% endif %}>Cancelado</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Filtrar
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'asignaciones:mis_asignaciones' %}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-times me-2"></i>Limpiar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de mis asignaciones -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Mis Asignaciones Activas
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if asignaciones %}
                        {% for asignacion in asignaciones %}
                            <div class="border-bottom p-3 asignacion-personal">
                                <div class="d-flex align-items-start justify-content-between">
                                    <div class="d-flex align-items-start flex-grow-1">
                                        <div class="avatar-sm me-3 bg-{{ asignacion.get_estado_display_color }}">
                                            <i class="fas fa-ticket-alt"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <h6 class="mb-0 me-2">
                                                    <a href="{% url 'tickets:detalle_ticket' asignacion.ticket.id %}" class="text-decoration-none">
                                                        Ticket #{{ asignacion.ticket.id }} - {{ asignacion.ticket.titulo }}
                                                    </a>
                                                </h6>
                                                <span class="badge bg-{{ asignacion.get_estado_display_color }}">
                                                    {{ asignacion.get_estado_display }}
                                                </span>
                                                <span class="badge bg-{{ asignacion.ticket.get_prioridad_display_color }} ms-1">
                                                    {{ asignacion.ticket.get_prioridad_display|title }}
                                                </span>
                                            </div>
                                            
                                            <p class="text-muted mb-2">{{ asignacion.ticket.descripcion|truncatechars:150 }}</p>
                                            
                                            <div class="d-flex align-items-center text-sm mb-2">
                                                <span class="me-3">
                                                    <i class="fas fa-building me-1"></i>{{ asignacion.ticket.grupo.name }}
                                                </span>
                                                <span class="me-3">
                                                    <i class="fas fa-calendar me-1"></i>Asignado: {{ asignacion.fecha_asignacion|date:"d/m/Y H:i" }}
                                                </span>
                                                {% if asignacion.asignado_por %}
                                                    <span class="text-muted">
                                                        <i class="fas fa-user me-1"></i>Por: {{ asignacion.asignado_por.get_full_name|default:asignacion.asignado_por.username }}
                                                    </span>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- Información de tiempo -->
                                            <div class="d-flex align-items-center text-sm">
                                                {% if asignacion.fecha_inicio %}
                                                    <span class="me-3 text-info">
                                                        <i class="fas fa-play me-1"></i>Iniciado: {{ asignacion.fecha_inicio|date:"d/m H:i" }}
                                                    </span>
                                                {% endif %}
                                                {% if asignacion.fecha_finalizacion %}
                                                    <span class="me-3 text-success">
                                                        <i class="fas fa-check me-1"></i>Finalizado: {{ asignacion.fecha_finalizacion|date:"d/m H:i" }}
                                                    </span>
                                                {% endif %}
                                                <span class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>Asignado {{ asignacion.fecha_asignacion|timesince }} atrás
                                                </span>
                                            </div>
                                            
                                            {% if asignacion.nota %}
                                                <div class="mt-2 p-2 bg-light rounded">
                                                    <small class="text-info">
                                                        <i class="fas fa-sticky-note me-1"></i><strong>Nota:</strong> {{ asignacion.nota }}
                                                    </small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <!-- Acciones personales -->
                                    <div class="ms-3">
                                        <div class="btn-group-vertical" role="group">
                                            <a href="{% url 'tickets:detalle_ticket' asignacion.ticket.id %}"
                                               class="btn btn-sm btn-primary" title="Ver y gestionar ticket">
                                                <i class="fas fa-eye me-1"></i>Ver Ticket
                                            </a>
                                            <!-- Botones de estado removidos: Los cambios se hacen desde el detalle del ticket -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        
                        <!-- Paginación -->
                        {% if asignaciones.has_other_pages %}
                            <div class="card-footer bg-white border-top">
                                <nav aria-label="Paginación de asignaciones">
                                    <ul class="pagination justify-content-center mb-0">
                                        {% if asignaciones.has_previous %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ asignaciones.previous_page_number }}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        {% endif %}
                                        
                                        <li class="page-item active">
                                            <span class="page-link">
                                                {{ asignaciones.number }} de {{ asignaciones.paginator.num_pages }}
                                            </span>
                                        </li>
                                        
                                        {% if asignaciones.has_next %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ asignaciones.next_page_number }}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        {% endif %}
                                    </ul>
                                </nav>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No tienes asignaciones</h5>
                            <p class="text-muted">
                                {% if estado_filtro %}
                                    No tienes asignaciones con el estado seleccionado
                                {% else %}
                                    No tienes asignaciones activas en este momento
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.asignacion-personal:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.text-sm {
    font-size: 0.875rem;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
</style>

<script>
// Funciones de cambio de estado removidas
// Los cambios de estado ahora se realizan desde el detalle del ticket
// para mantener consistencia y evitar confusiones

// Función para filtros (si es necesaria)
function aplicarFiltros() {
    document.getElementById('filtros-form').submit();
}
</script>

{% csrf_token %}
{% endblock %}
