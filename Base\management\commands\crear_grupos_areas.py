"""
Base/management/commands/crear_grupos_areas.py
Comando para crear grupos básicos de áreas de trabajo.

Uso: python manage.py crear_grupos_areas
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType


class Command(BaseCommand):
    help = 'Crea grupos básicos de áreas de trabajo para el sistema'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Elimina y recrea todos los grupos de áreas',
        )

    def handle(self, *args, **options):
        """
        Crea los grupos básicos de áreas de trabajo.
        """
        self.stdout.write(
            self.style.SUCCESS('Creando grupos de áreas de trabajo...')
        )

        if options['reset']:
            self.reset_grupos()

        # Definir grupos de áreas básicas
        grupos_areas = [
            {
                'name': 'Administración',
                'description': 'Personal administrativo y de gestión'
            },
            {
                'name': 'Secretaría',
                'description': 'Personal de secretaría y atención al público'
            },
            {
                'name': 'Fontanería',
                'description': 'Técnicos en fontanería y plomería'
            },
            {
                'name': 'Electricidad',
                'description': 'Técnicos electricistas'
            },
            {
                'name': 'Mantenimiento',
                'description': 'Personal de mantenimiento general'
            },
            {
                'name': 'Limpieza',
                'description': 'Personal de limpieza y aseo'
            },
            {
                'name': 'Jardinería',
                'description': 'Personal de jardinería y áreas verdes'
            },
            {
                'name': 'Seguridad',
                'description': 'Personal de seguridad'
            },
            {
                'name': 'Obras Públicas',
                'description': 'Personal de obras públicas y construcción'
            },
            {
                'name': 'Servicios Generales',
                'description': 'Personal de servicios generales'
            },
        ]

        grupos_creados = 0
        grupos_existentes = 0

        for grupo_data in grupos_areas:
            grupo, created = Group.objects.get_or_create(
                name=grupo_data['name']
            )
            
            if created:
                grupos_creados += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Grupo "{grupo.name}" creado')
                )
            else:
                grupos_existentes += 1
                self.stdout.write(
                    self.style.WARNING(f'⚠ Grupo "{grupo.name}" ya existe')
                )

        # Configurar permisos básicos para cada tipo de área
        self.configurar_permisos_areas()

        # Resumen
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS(f'✓ Grupos creados: {grupos_creados}')
        )
        self.stdout.write(
            self.style.WARNING(f'⚠ Grupos existentes: {grupos_existentes}')
        )
        
        # Mostrar todos los grupos
        self.stdout.write('\nGrupos disponibles:')
        for grupo in Group.objects.all().order_by('name'):
            usuarios_count = grupo.user_set.filter(is_active=True).count()
            self.stdout.write(f'  • {grupo.name}: {usuarios_count} usuarios')

        self.stdout.write(
            self.style.SUCCESS('\n¡Grupos de áreas creados exitosamente!')
        )

    def reset_grupos(self):
        """
        Elimina grupos existentes (excepto Admin, Secretaria, Empleado).
        """
        self.stdout.write('Eliminando grupos de áreas existentes...')
        
        # Mantener grupos básicos del sistema
        grupos_sistema = ['Admin', 'Secretaria', 'Empleado']
        
        grupos_eliminados = Group.objects.exclude(name__in=grupos_sistema).delete()[0]
        
        self.stdout.write(
            self.style.WARNING(f'Eliminados {grupos_eliminados} grupos de áreas')
        )

    def configurar_permisos_areas(self):
        """
        Configura permisos básicos para las áreas.
        """
        self.stdout.write('\nConfigurando permisos básicos...')
        
        # Permisos básicos que todos los grupos de área deben tener
        permisos_basicos = [
            'view_ticket',
            'view_ciudadano',
            'view_notificacion',
            'view_notificacionusuario',
        ]
        
        # Permisos adicionales para áreas administrativas
        permisos_administrativos = [
            'add_ticket',
            'change_ticket',
            'add_ciudadano',
            'change_ciudadano',
            'add_asignacionticket',
            'view_asignacionticket',
        ]
        
        # Configurar permisos por área
        areas_config = {
            'Administración': permisos_basicos + permisos_administrativos,
            'Secretaría': permisos_basicos + permisos_administrativos,
            'Fontanería': permisos_basicos + ['change_asignacionticket'],
            'Electricidad': permisos_basicos + ['change_asignacionticket'],
            'Mantenimiento': permisos_basicos + ['change_asignacionticket'],
            'Limpieza': permisos_basicos + ['change_asignacionticket'],
            'Jardinería': permisos_basicos + ['change_asignacionticket'],
            'Seguridad': permisos_basicos + ['change_asignacionticket'],
            'Obras Públicas': permisos_basicos + ['change_asignacionticket'],
            'Servicios Generales': permisos_basicos + ['change_asignacionticket'],
        }
        
        for area_nombre, permisos_codenames in areas_config.items():
            try:
                grupo = Group.objects.get(name=area_nombre)
                permisos = Permission.objects.filter(codename__in=permisos_codenames)
                grupo.permissions.set(permisos)
                
                self.stdout.write(f'  ✓ {area_nombre}: {permisos.count()} permisos asignados')
                
            except Group.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ Grupo "{area_nombre}" no encontrado')
                )
