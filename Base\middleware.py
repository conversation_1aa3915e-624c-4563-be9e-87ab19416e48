"""
Base/middleware.py
Middleware personalizado para seguridad y protección del sistema.

Incluye:
- Protección del panel de administración
- Rate limiting personalizado
- Validaciones de seguridad adicionales
"""

from django.http import HttpResponseRedirect, HttpResponseForbidden
from django.urls import reverse
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.shortcuts import redirect
from django_ratelimit.decorators import ratelimit
try:
    from django_ratelimit.core import is_ratelimited
except ImportError:
    # Fallback para versiones más antiguas
    from django_ratelimit import is_ratelimited
import logging

logger = logging.getLogger(__name__)


class AdminProtectionMiddleware:
    """
    Middleware que protege el acceso al panel de administración.
    Solo permite acceso a usuarios staff autenticados.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        """
        Procesa cada request antes de llegar a las vistas.
        
        Args:
            request: HttpRequest object
            
        Returns:
            HttpResponse: Respuesta procesada o redirección
        """
        # Verificar si la URL es del admin
        if request.path.startswith('/admin/'):
            # Si no está autenticado o no es staff, redirigir al login
            if not request.user.is_authenticated or not request.user.is_staff:
                logger.warning(
                    f"Intento de acceso no autorizado al admin desde IP: {self.get_client_ip(request)}"
                )
                messages.error(request, 'No tienes permisos para acceder al panel de administración.')
                return HttpResponseRedirect(reverse('login'))
        
        response = self.get_response(request)
        return response
    
    def get_client_ip(self, request):
        """
        Obtiene la IP real del cliente considerando proxies.
        
        Args:
            request: HttpRequest object
            
        Returns:
            str: Dirección IP del cliente
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class RateLimitMiddleware:
    """
    Middleware para aplicar rate limiting a rutas específicas.
    Protege contra ataques de fuerza bruta y spam.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Rutas que requieren rate limiting más estricto
        self.protected_paths = [
            '/login/',
            '/admin/',
            '/api/',
        ]

    def __call__(self, request):
        """
        Aplica rate limiting según la ruta y tipo de request.
        
        Args:
            request: HttpRequest object
            
        Returns:
            HttpResponse: Respuesta procesada o error 429
        """
        # Aplicar rate limiting más estricto a rutas protegidas
        for path in self.protected_paths:
            if request.path.startswith(path):
                # Rate limit más estricto para login
                if path == '/login/' and request.method == 'POST':
                    if is_ratelimited(request, group='login', key='ip', rate='5/m', increment=True):
                        logger.warning(
                            f"Rate limit excedido para login desde IP: {self.get_client_ip(request)}"
                        )
                        return HttpResponseForbidden(
                            "Demasiados intentos de login. Intenta de nuevo en unos minutos."
                        )
                
                # Rate limit general para admin
                elif path == '/admin/':
                    if is_ratelimited(request, group='admin', key='ip', rate='30/m', increment=True):
                        logger.warning(
                            f"Rate limit excedido para admin desde IP: {self.get_client_ip(request)}"
                        )
                        return HttpResponseForbidden(
                            "Demasiadas solicitudes. Intenta de nuevo más tarde."
                        )
        
        response = self.get_response(request)
        return response
    
    def get_client_ip(self, request):
        """
        Obtiene la IP real del cliente considerando proxies.
        
        Args:
            request: HttpRequest object
            
        Returns:
            str: Dirección IP del cliente
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SecurityHeadersMiddleware:
    """
    Middleware que añade headers de seguridad adicionales.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Headers de seguridad adicionales
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        return response


class SecurityScannerMiddleware:
    """
    Middleware para detectar y bloquear intentos de ataques comunes.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # Patrones sospechosos comunes
        self.suspicious_patterns = [
            'union select',
            'drop table',
            'insert into',
            'delete from',
            '<script',
            'javascript:',
            'eval(',
            'alert(',
            '../../../',
            '..\\..\\',
            'cmd.exe',
            '/etc/passwd',
            'wp-admin',
            'phpmyadmin',
        ]

    def __call__(self, request):
        """
        Analiza la request en busca de patrones sospechosos.

        Args:
            request: HttpRequest object

        Returns:
            HttpResponse: Respuesta procesada o bloqueo
        """
        # Analizar URL
        if self.contains_suspicious_content(request.path.lower()):
            logger.warning(
                f"URL sospechosa detectada: {request.path} desde IP: {self.get_client_ip(request)}"
            )
            return HttpResponseForbidden("Acceso denegado")

        # Analizar parámetros GET
        for key, value in request.GET.items():
            if self.contains_suspicious_content(f"{key}={value}".lower()):
                logger.warning(
                    f"Parámetro GET sospechoso: {key}={value} desde IP: {self.get_client_ip(request)}"
                )
                return HttpResponseForbidden("Acceso denegado")

        # Analizar User-Agent
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if self.is_suspicious_user_agent(user_agent):
            logger.warning(
                f"User-Agent sospechoso: {user_agent} desde IP: {self.get_client_ip(request)}"
            )
            return HttpResponseForbidden("Acceso denegado")

        response = self.get_response(request)
        return response

    def contains_suspicious_content(self, content):
        """
        Verifica si el contenido contiene patrones sospechosos.

        Args:
            content: Contenido a analizar

        Returns:
            bool: True si contiene patrones sospechosos
        """
        return any(pattern in content for pattern in self.suspicious_patterns)

    def is_suspicious_user_agent(self, user_agent):
        """
        Verifica si el User-Agent es sospechoso.

        Args:
            user_agent: User-Agent a verificar

        Returns:
            bool: True si es sospechoso
        """
        suspicious_agents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'nessus',
            'openvas',
            'w3af',
            'havij',
            'pangolin',
        ]

        return any(agent in user_agent for agent in suspicious_agents)

    def get_client_ip(self, request):
        """
        Obtiene la IP real del cliente considerando proxies.

        Args:
            request: HttpRequest object

        Returns:
            str: Dirección IP del cliente
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ContentSecurityPolicyMiddleware:
    """
    Middleware que implementa Content Security Policy (CSP) para prevenir XSS.
    Configura headers CSP basados en la configuración de Django settings.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Aplicar CSP desde settings
        from django.conf import settings
        csp = getattr(settings, 'SECURE_CONTENT_SECURITY_POLICY', None)

        if csp:
            # Construir la política CSP
            policy_parts = []
            for directive, sources in csp.items():
                if isinstance(sources, list):
                    sources_str = ' '.join(sources)
                else:
                    sources_str = sources
                policy_parts.append(f"{directive} {sources_str}")

            policy = '; '.join(policy_parts)
            response['Content-Security-Policy'] = policy

            # También agregar CSP en modo report-only para debugging
            if getattr(settings, 'DEBUG', False):
                response['Content-Security-Policy-Report-Only'] = policy

        return response


class UnauthorizedAccessMiddleware:
    """
    Middleware que maneja intentos de acceso no autorizado con sistema de advertencias.

    Funcionalidad:
    - Registra intentos de acceso no autorizado
    - Muestra advertencias progresivas
    - Desloguea automáticamente después de 3 intentos en 30 minutos
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        """
        Procesa excepciones PermissionDenied para implementar sistema de advertencias.
        """
        from django.core.exceptions import PermissionDenied

        if isinstance(exception, PermissionDenied) and request.user.is_authenticated:
            # Registrar el intento y manejar advertencias
            response = self._handle_permission_denied(request, exception)
            if response:  # Solo si hay deslogueo automático
                return response
            # Si no hay deslogueo, permitir que la excepción continúe para mostrar 403
        return None

    def _handle_permission_denied(self, request, exception):
        """
        Maneja intentos de acceso no autorizado con sistema de advertencias progresivas.
        """
        from django.contrib.auth import logout
        from .models import UnauthorizedAccessAttempt

        # Registrar el intento
        attempt = UnauthorizedAccessAttempt.objects.create(
            user=request.user,
            ip_address=self._get_client_ip(request),
            url_attempted=request.build_absolute_uri(),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            session_key=request.session.session_key or ''
        )

        # Obtener número de intentos recientes
        recent_attempts = UnauthorizedAccessAttempt.get_recent_attempts_count(request.user)

        # Log del intento
        logger.warning(
            f"Intento de acceso no autorizado #{recent_attempts} - "
            f"Usuario: {request.user.username} - "
            f"IP: {self._get_client_ip(request)} - "
            f"URL: {request.build_absolute_uri()}"
        )

        # Determinar acción según número de intentos
        if recent_attempts >= 3:
            # Desloguear después de 3 intentos
            logout(request)
            messages.error(
                request,
                "⚠️ Has sido deslogueado automáticamente por múltiples intentos de acceso no autorizado. "
                "Por favor, contacta al administrador si necesitas acceso a estas funciones."
            )
            logger.critical(
                f"Usuario {request.user.username} deslogueado automáticamente por "
                f"{recent_attempts} intentos de acceso no autorizado desde IP {self._get_client_ip(request)}"
            )
            return HttpResponseRedirect(reverse('login'))

        elif recent_attempts == 2:
            # Segunda advertencia - más severa
            messages.error(
                request,
                f"🚨 ADVERTENCIA FINAL: Este es tu intento #{recent_attempts} de acceso no autorizado. "
                f"Un intento más resultará en deslogueo automático. "
                f"Solo accede a las funciones para las que tienes permisos."
            )

        elif recent_attempts == 1:
            # Primera advertencia - informativa
            messages.warning(
                request,
                f"⚠️ Advertencia: Has intentado acceder a una función sin permisos suficientes. "
                f"Intento #{recent_attempts} de 3. Por favor, solo accede a las funciones autorizadas para tu rol."
            )

        # Solo retornar respuesta si hay deslogueo, sino dejar que la excepción continúe
        return None

    def _get_client_ip(self, request):
        """
        Obtiene la IP real del cliente considerando proxies.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
