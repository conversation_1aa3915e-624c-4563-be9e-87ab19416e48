"""
Comando de Django para limpiar intentos de acceso no autorizado antiguos.

Uso:
    python manage.py cleanup_unauthorized_attempts
    python manage.py cleanup_unauthorized_attempts --days 7
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from Base.models import UnauthorizedAccessAttempt


class Command(BaseCommand):
    help = 'Limpia intentos de acceso no autorizado antiguos'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Número de días de antigüedad para eliminar (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Mostrar qué se eliminaría sin hacer cambios'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        
        # Calcular fecha límite
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Obtener intentos a eliminar
        attempts_to_delete = UnauthorizedAccessAttempt.objects.filter(
            timestamp__lt=cutoff_date
        )
        
        count = attempts_to_delete.count()
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Se eliminarían {count} intentos de acceso '
                    f'no autorizado anteriores a {cutoff_date.strftime("%Y-%m-%d %H:%M:%S")}'
                )
            )
            
            if count > 0:
                self.stdout.write('\nPrimeros 10 registros que se eliminarían:')
                for attempt in attempts_to_delete[:10]:
                    self.stdout.write(
                        f'  - {attempt.user.username} | {attempt.ip_address} | '
                        f'{attempt.timestamp.strftime("%Y-%m-%d %H:%M:%S")}'
                    )
        else:
            # Eliminar registros
            deleted_count, _ = attempts_to_delete.delete()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Se eliminaron {deleted_count} intentos de acceso no autorizado '
                    f'anteriores a {cutoff_date.strftime("%Y-%m-%d %H:%M:%S")}'
                )
            )
        
        # Mostrar estadísticas actuales
        total_attempts = UnauthorizedAccessAttempt.objects.count()
        recent_attempts = UnauthorizedAccessAttempt.objects.filter(
            timestamp__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        self.stdout.write(f'\nEstadísticas actuales:')
        self.stdout.write(f'  - Total de intentos: {total_attempts}')
        self.stdout.write(f'  - Intentos últimos 7 días: {recent_attempts}')
