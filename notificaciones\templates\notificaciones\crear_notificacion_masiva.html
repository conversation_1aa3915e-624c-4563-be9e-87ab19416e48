{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}

{% block title %}Crear Notificación Masiva{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-broadcast-tower text-danger"></i>
                        Crear Notificación Masiva
                    </h2>
                    <p class="text-muted mb-0">Envíe notificaciones a múltiples destinatarios simultáneamente</p>
                </div>
                <div>
                    <a href="{% url 'notificaciones:lista_notificaciones' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/inicio/">
                    <i class="fas fa-home"></i> Inicio
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'notificaciones:lista_notificaciones' %}">Notificaciones</a>
            </li>
            <li class="breadcrumb-item active">Notificación Masiva</li>
        </ol>
    </nav>

    <!-- Alerta de advertencia -->
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>¡Atención!</strong> Esta funcionalidad permite enviar notificaciones a un gran número de usuarios. 
        Use con responsabilidad y verifique el contenido antes de enviar.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Formulario -->
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Formulario de Notificación Masiva
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate id="masiva-form">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas del sistema -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar"></i> Información del Envío
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="p-3 border rounded bg-light">
                                <h4 class="text-primary mb-1" id="total-usuarios-activos">-</h4>
                                <small class="text-muted">Usuarios Activos en el Sistema</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="p-3 border rounded bg-light">
                                <h4 class="text-success mb-1">100%</h4>
                                <small class="text-muted">Cobertura del Envío</small>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Importante:</strong> Esta notificación se enviará automáticamente a todos los usuarios activos del sistema.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Información de uso responsable -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-thumbs-up"></i> Buenas Prácticas
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Use mensajes claros y concisos
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Seleccione el tipo de notificación apropiado
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Verifique la lista de destinatarios
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Incluya URLs de acción cuando sea relevante
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            Evite el envío excesivo de notificaciones
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-ban"></i> Restricciones
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-times text-danger"></i>
                            Máximo 200 usuarios por envío
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger"></i>
                            No se pueden editar una vez enviadas
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger"></i>
                            Solo usuarios activos reciben notificaciones
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger"></i>
                            Requiere permisos de administrador
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-times text-danger"></i>
                            No usar para spam o contenido inapropiado
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmación de envío -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i> Confirmar Envío Masivo
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Está a punto de enviar una notificación masiva a <strong id="confirm-count">0</strong> usuario(s).</p>
                    <div class="alert alert-info">
                        <strong>Mensaje:</strong> <span id="confirm-mensaje"></span>
                    </div>
                    <p class="text-muted">Esta acción no se puede deshacer. ¿Está seguro de continuar?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="confirm-send">
                        <i class="fas fa-paper-plane"></i> Enviar Notificación
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let totalUsuarios = 0;
    const maxUsuarios = 200;

    // Elementos del DOM
    const usuariosEspecificosEl = document.getElementById('usuarios-especificos');
    const usuariosGruposEl = document.getElementById('usuarios-grupos');
    const totalUsuariosEl = document.getElementById('total-usuarios');
    const limiteEstadoEl = document.getElementById('limite-estado');
    const progressBar = document.getElementById('progress-bar');
    const form = document.getElementById('masiva-form');
    const confirmModal = new bootstrap.Modal(document.getElementById('confirmModal'));

    // Función para actualizar estadísticas
    function updateStats() {
        let usuariosEspecificos = 0;
        let usuariosGrupos = 0;
        let todosLosUsuarios = false;

        // Contar usuarios específicos seleccionados
        const usuariosCheckboxes = document.querySelectorAll('input[name="usuarios"]:checked');
        usuariosEspecificos = usuariosCheckboxes.length;

        // Contar usuarios en grupos seleccionados
        const gruposCheckboxes = document.querySelectorAll('input[name="grupos"]:checked');
        gruposCheckboxes.forEach(checkbox => {
            const label = checkbox.nextElementSibling.textContent;
            const match = label.match(/\((\d+) miembros/);
            if (match) {
                usuariosGrupos += parseInt(match[1]);
            }
        });

        // Verificar si está seleccionado "todos los usuarios"
        const todosCheckbox = document.querySelector('input[name="todos_los_usuarios"]');
        if (todosCheckbox && todosCheckbox.checked) {
            todosLosUsuarios = true;
            // Aquí podrías hacer una llamada AJAX para obtener el número real
            totalUsuarios = 150; // Valor estimado
        } else {
            totalUsuarios = usuariosEspecificos + usuariosGrupos;
        }

        // Actualizar elementos del DOM
        usuariosEspecificosEl.textContent = usuariosEspecificos;
        usuariosGruposEl.textContent = usuariosGrupos;
        totalUsuariosEl.textContent = totalUsuarios;

        // Actualizar estado del límite
        const porcentaje = (totalUsuarios / maxUsuarios) * 100;
        progressBar.style.width = Math.min(porcentaje, 100) + '%';
        progressBar.textContent = Math.round(porcentaje) + '%';

        if (totalUsuarios > maxUsuarios) {
            limiteEstadoEl.textContent = 'EXCEDIDO';
            limiteEstadoEl.className = 'text-danger mb-1';
            progressBar.className = 'progress-bar bg-danger';
        } else if (totalUsuarios > maxUsuarios * 0.8) {
            limiteEstadoEl.textContent = 'ALTO';
            limiteEstadoEl.className = 'text-warning mb-1';
            progressBar.className = 'progress-bar bg-warning';
        } else {
            limiteEstadoEl.textContent = 'OK';
            limiteEstadoEl.className = 'text-success mb-1';
            progressBar.className = 'progress-bar bg-success';
        }
    }

    // Event listeners para checkboxes
    document.addEventListener('change', function(e) {
        if (e.target.name === 'usuarios' || e.target.name === 'grupos' || e.target.name === 'todos_los_usuarios') {
            updateStats();
        }
    });

    // Interceptar envío del formulario para mostrar confirmación
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (totalUsuarios === 0) {
            Swal.fire({
                title: 'Error',
                text: 'Debe seleccionar al menos un destinatario.',
                icon: 'error'
            });
            return;
        }

        if (totalUsuarios > maxUsuarios) {
            Swal.fire({
                title: 'Límite Excedido',
                text: `No puede enviar a más de ${maxUsuarios} usuarios. Actualmente: ${totalUsuarios}`,
                icon: 'error'
            });
            return;
        }

        // Mostrar modal de confirmación
        const mensaje = document.querySelector('textarea[name="mensaje"]').value;
        document.getElementById('confirm-count').textContent = totalUsuarios;
        document.getElementById('confirm-mensaje').textContent = mensaje.substring(0, 100) + (mensaje.length > 100 ? '...' : '');
        
        confirmModal.show();
    });

    // Confirmar envío
    document.getElementById('confirm-send').addEventListener('click', function() {
        confirmModal.hide();
        form.submit();
    });

    // Inicializar estadísticas
    updateStats();

    // Cargar estadísticas del sistema
    cargarEstadisticasSistema();

    // Función para confirmar envío masivo
    window.confirmarEnvioMasivo = function() {
        return Swal.fire({
            title: '¿Confirmar envío masivo?',
            html: `
                <div class="text-start">
                    <p><strong>Esta notificación se enviará a:</strong></p>
                    <ul>
                        <li>Todos los usuarios activos del sistema</li>
                        <li>Se procesará en segundo plano</li>
                        <li>No se puede cancelar una vez iniciado</li>
                    </ul>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Sí, enviar a todos',
            cancelButtonText: 'Cancelar',
            customClass: {
                popup: 'swal-wide'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Mostrar progreso
                Swal.fire({
                    title: 'Enviando notificación masiva...',
                    html: `
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Enviando...</span>
                            </div>
                            <p>Por favor espere mientras se procesa el envío masivo.</p>
                            <small class="text-muted">Este proceso puede tomar varios minutos.</small>
                        </div>
                    `,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false
                });
                return true;
            }
            return false;
        });
    };

    async function cargarEstadisticasSistema() {
        try {
            const response = await fetch('/notificaciones/ajax/estadisticas-sistema/');
            const data = await response.json();

            document.getElementById('total-usuarios-activos').textContent = data.usuarios_activos || 0;
        } catch (error) {
            console.error('Error cargando estadísticas:', error);
            document.getElementById('total-usuarios-activos').textContent = 'Error';
        }
    }

    // Validación del formulario
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const confirmacion = document.querySelector('input[name="confirmacion"]');

            if (!confirmacion || !confirmacion.checked) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Confirmación requerida',
                    text: 'Debe confirmar que desea enviar la notificación a todos los usuarios.'
                });
                return false;
            }
        });
    }
});

// Estilos adicionales para SweetAlert
const style = document.createElement('style');
style.textContent = `
    .swal-wide {
        width: 600px !important;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
