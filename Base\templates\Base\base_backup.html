{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistema de Tickets{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        /* Definición de la paleta de colores */
        :root {
            --primary-color: #1A237E;      /* Azul Oscuro */
            --secondary-color: #283593;    /* Azul Medio */
            --tertiary-color: #3F51B5;     /* Azul Claro */
            --gray-color: #9E9E9E;         /* Gris */
            --white-color: #FFFFFF;        /* Blanco */
            --accent-orange: #FF9800;      /* Naranja */
            --accent-green: #4CAF50;       /* Verde */
            --accent-yellow: #FFEB3B;      /* Amarillo */
        }

        /* Estilos globales y animación de entrada */
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--white-color);
            min-height: 100vh;
            margin: 0;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Sidebar */
        #sidebar-wrapper {
            background: var(--primary-color);
            width: 250px;
            transition: all 0.3s ease;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            overflow: hidden;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-content {
            width: 250px;
            min-height: 100vh;
        }

        #sidebar-wrapper.collapsed {
            width: 0;
        }

        /* Items del menú con efecto hover */
        .list-group-item {
            background: transparent !important;
            border: none;
            color: var(--white-color) !important;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .list-group-item:hover {
            background: rgba(255,255,255,0.1) !important;
            padding-left: 2rem;
            color: var(--white-color) !important;
        }

        /* Estilos para desplegables */
        .dropdown-menu-custom {
            background: rgba(255,255,255,0.05);
            border: none;
            margin: 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .dropdown-menu-custom.show {
            max-height: 300px;
        }

        .dropdown-item-custom {
            background: transparent !important;
            border: none;
            color: rgba(255,255,255,0.8) !important;
            padding: 0.75rem 2.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            font-size: 0.9rem;
        }

        .dropdown-item-custom:hover {
            background: rgba(255,255,255,0.1) !important;
            color: var(--white-color) !important;
            padding-left: 3rem;
        }

        .dropdown-toggle-custom {
            position: relative;
        }

        .dropdown-toggle-custom::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 1rem;
            transition: transform 0.3s ease;
        }

        .dropdown-toggle-custom.collapsed::after {
            transform: rotate(-90deg);
        }

        /* Contenido principal */
        .main-content {
            margin-left: 250px;
            transition: all 0.3s ease;
            min-height: 100vh;
            width: calc(100% - 250px);
        }

        .main-content.collapsed {
            margin-left: 0;
            width: 100%;
        }

        /* Barra superior */
        .navbar {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
            padding: 0.5rem 1rem;
            background-color: var(--white-color);
            position: sticky;
            top: 0;
            z-index: 1020;
        }

        /* Botón para mostrar/ocultar el sidebar */
        #sidebarToggle {
            transition: transform 0.3s ease;
            margin-left: 1rem;
            color: var(--primary-color);
        }

        #sidebarToggle.active {
            transform: rotate(180deg);
        }

        /* Encabezado del sidebar con animación de entrada */
        .sidebar-heading {
            background: var(--secondary-color);
            padding: 1rem;
            text-align: center;
        }

        .sidebar-heading h3 {
            margin: 0;
            color: var(--white-color);
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Ajustes responsivos */
        @media (max-width: 768px) {
            #sidebar-wrapper {
                width: 0;
            }
            
            #sidebar-wrapper.active {
                width: 250px;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .main-content.collapsed-mobile {
                margin-left: 250px;
            }
        }

        /* OPTIMIZACIONES DE VELOCIDAD - Animaciones más rápidas */
        body {
            animation: fadeIn 0.15s ease-in !important;
        }

        .list-group-item {
            transition: all 0.1s ease !important;
        }

        #sidebar-wrapper {
            transition: all 0.15s ease !important;
        }

        .main-content {
            transition: all 0.15s ease !important;
        }

        .btn {
            transition: all 0.1s ease !important;
        }

        .card {
            transition: all 0.15s ease !important;
        }

        .modal.fade .modal-dialog {
            transition: transform 0.2s ease-out !important;
        }

        .collapse {
            transition: height 0.2s ease !important;
        }

        .form-control, .form-select {
            transition: all 0.1s ease !important;
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar Mejorado -->
        {% include 'base/sidebar_mejorado.html' %}
                <div class="list-group list-group-flush">
                    <!-- Menú de navegación moderno -->

                    <!-- Inicio -->
                    <a href="/inicio/" class="list-group-item">
                        <i class="fas fa-home me-3"></i>
                        <span>Dashboard</span>
                    </a>

                    <!-- Tickets (Desplegable) -->
                    <a href="#ticketsSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-ticket-alt me-3"></i>
                        <span>Tickets</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="ticketsSubmenu">
                        <a href="{% url 'tickets:lista_tickets' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Ver Todos los Tickets
                        </a>
                        <a href="{% url 'tickets:crear_ticket' %}" class="dropdown-item-custom">
                            <i class="fas fa-plus me-2"></i> Crear Nuevo Ticket
                        </a>
                        <a href="{% url 'tickets:dashboard' %}" class="dropdown-item-custom">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard de Tickets
                        </a>
                    </div>

                    <!-- Asignaciones (Desplegable) -->
                    <a href="#asignacionesSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-tasks me-3"></i>
                        <span>Asignaciones</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="asignacionesSubmenu">
                        <a href="{% url 'asignaciones:mis_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-check me-2"></i> Mis Asignaciones
                        </a>
                        <a href="{% url 'asignaciones:lista_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-users-cog me-2"></i> Gestionar Asignaciones
                        </a>
                        <a href="{% url 'asignaciones:asignaciones_grupo' %}" class="dropdown-item-custom">
                            <i class="fas fa-layer-group me-2"></i> Asignaciones por Grupo
                        </a>
                        <a href="{% url 'asignaciones:historial_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-history me-2"></i> Historial de Asignaciones
                        </a>
                        <a href="{% url 'asignaciones:reportes_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-chart-pie me-2"></i> Reportes de Asignaciones
                        </a>
                        <a href="{% url 'asignaciones:reporte_productividad' %}" class="dropdown-item-custom">
                            <i class="fas fa-chart-line me-2"></i> Reporte de Productividad
                        </a>
                    </div>

                    <!-- Ciudadanos (Desplegable) -->
                    <a href="#ciudadanosSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-users me-3"></i>
                        <span>Ciudadanos</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="ciudadanosSubmenu">
                        <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Lista de Ciudadanos
                        </a>
                        <a href="{% url 'ciudadano:crear_ciudadano' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-plus me-2"></i> Registrar Ciudadano
                        </a>
                        <a href="{% url 'ciudadano:buscar_ciudadano' %}" class="dropdown-item-custom">
                            <i class="fas fa-search me-2"></i> Buscar Ciudadano
                        </a>
                    </div>

                    <!-- Notificaciones (Desplegable) -->
                    <a href="#notificacionesSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-bell me-3"></i>
                        <span>Notificaciones</span>
                        <span class="badge bg-danger ms-auto">3</span>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="notificacionesSubmenu">
                        <a href="{% url 'notificaciones:lista_notificaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Todas las Notificaciones
                        </a>
                        <a href="{% url 'notificaciones:no_leidas' %}" class="dropdown-item-custom">
                            <i class="fas fa-envelope me-2"></i> No Leídas
                        </a>
                        <a href="{% url 'notificaciones:crear_notificacion' %}" class="dropdown-item-custom">
                            <i class="fas fa-plus me-2"></i> Crear Notificación
                        </a>
                        <a href="{% url 'notificaciones:enviar_masiva' %}" class="dropdown-item-custom">
                            <i class="fas fa-broadcast-tower me-2"></i> Envío Masivo
                        </a>
                    </div>

                    <!-- Usuarios (Desplegable) - Solo para Admins -->
                    <a href="#usuariosSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-user-cog me-3"></i>
                        <span>Usuarios</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="usuariosSubmenu">
                        <a href="{% url 'user:lista_usuarios' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Lista de Usuarios
                        </a>
                        <a href="{% url 'user:crear_usuario_paso1' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-plus me-2"></i> Crear Usuario
                        </a>
                        <a href="{% url 'user:lista_usuarios_inactivos' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-slash me-2"></i> Usuarios Inactivos
                        </a>
                        <a href="{% url 'user:lista_cargos' %}" class="dropdown-item-custom">
                            <i class="fas fa-briefcase me-2"></i> Gestión de Cargos
                        </a>
                    </div>

                    <!-- Reportes y Estadísticas (Desplegable) -->
                    <a href="#reportesSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-chart-bar me-3"></i>
                        <span>Reportes</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="reportesSubmenu">
                        <a href="{% url 'tickets:reportes' %}" class="dropdown-item-custom">
                            <i class="fas fa-chart-line me-2"></i> Reportes de Tickets
                        </a>
                        <a href="{% url 'tickets:estadisticas' %}" class="dropdown-item-custom">
                            <i class="fas fa-chart-pie me-2"></i> Estadísticas Generales
                        </a>
                        <a href="{% url 'asignaciones:reportes_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-tasks me-2"></i> Reportes de Asignaciones
                        </a>
                        <a href="{% url 'asignaciones:reporte_productividad' %}" class="dropdown-item-custom">
                            <i class="fas fa-trophy me-2"></i> Productividad
                        </a>
                    </div>

                    <!-- Separador -->
                    <div class="sidebar-divider"></div>

                    <!-- Perfil de Usuario -->
                    <a href="#" class="list-group-item">
                        <i class="fas fa-user-circle me-3"></i>
                        <span>Mi Perfil</span>
                    </a>

                    <!-- Configuración -->
                    <a href="#" class="list-group-item">
                        <i class="fas fa-cog me-3"></i>
                        <span>Configuración</span>
                    </a>

                    <!-- Cerrar Sesión -->
                    <a href="{% url 'logout' %}" class="list-group-item text-danger">
                        <i class="fas fa-sign-out-alt me-3"></i>
                        <span>Cerrar Sesión</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Contenido principal -->
        <div class="main-content" id="page-content-wrapper">
            <!-- Barra superior -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid px-0">
                    <button class="btn btn-link" id="sidebarToggle">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <span class="navbar-brand m-0">
                        <i class="fas fa-user-circle me-2"></i>Bienvenido, <strong>{{ user.username }}</strong>
                    </span>
                </div>
            </nav>

            <!-- Contenido dinámico -->
            <div class="container-fluid p-4">
                {% block content %}
                {% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            // Toggle sidebar
            $('#sidebarToggle').on('click', function () {
                const sidebar = $('#sidebar-wrapper');
                const content = $('.main-content');

                sidebar.toggleClass('collapsed active');
                content.toggleClass('collapsed collapsed-mobile');
                $(this).toggleClass('active');

                if ($(window).width() > 768) {
                    sidebar.hasClass('collapsed') ?
                        sidebar.css('width', '0') :
                        sidebar.css('width', '250px');
                }
            });

            // Manejar desplegables del sidebar
            $('.dropdown-toggle-custom').on('click', function(e) {
                e.preventDefault();

                const target = $(this).attr('href');
                const isExpanded = $(this).attr('aria-expanded') === 'true';

                // Cerrar otros desplegables
                $('.dropdown-toggle-custom').not(this).attr('aria-expanded', 'false').addClass('collapsed');
                $('.dropdown-menu-custom').not(target).removeClass('show');

                // Toggle el desplegable actual
                if (isExpanded) {
                    $(this).attr('aria-expanded', 'false').addClass('collapsed');
                    $(target).removeClass('show');
                } else {
                    $(this).attr('aria-expanded', 'true').removeClass('collapsed');
                    $(target).addClass('show');
                }
            });

            // Cerrar desplegables al hacer clic fuera
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.dropdown-toggle-custom, .dropdown-menu-custom').length) {
                    $('.dropdown-toggle-custom').attr('aria-expanded', 'false').addClass('collapsed');
                    $('.dropdown-menu-custom').removeClass('show');
                }
            });

            // Mantener desplegable abierto si hay una página activa
            $('.dropdown-item-custom').each(function() {
                if ($(this).attr('href') === window.location.pathname) {
                    $(this).addClass('active').css({
                        'background': 'rgba(255,255,255,0.2) !important',
                        'color': 'var(--white-color) !important'
                    });

                    // Abrir el desplegable padre
                    const parentCollapse = $(this).closest('.dropdown-menu-custom');
                    const parentToggle = $(`[href="#${parentCollapse.attr('id')}"]`);

                    parentToggle.attr('aria-expanded', 'true').removeClass('collapsed');
                    parentCollapse.addClass('show');
                }
            });
        });
    </script>
</body>
</html>
