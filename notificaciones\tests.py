"""
notificaciones/tests.py
Tests completos para el sistema de notificaciones.

Incluye:
- Tests de modelos
- Tests de vistas
- Tests de formularios
- Tests de funcionalidades AJAX
- Tests de permisos
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.http import JsonResponse
import json

from .models import Notificacion, NotificacionUsuario, NotificacionGrupo
from .forms import NotificacionUsuarioForm, NotificacionGrupoForm, NotificacionMasivaForm
from tickets.models import Ticket
from user.models import CargoUsuario

User = get_user_model()


class NotificacionModelTest(TestCase):
    """
    Tests para el modelo Notificacion.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        self.cargo = CargoUsuario.objects.create(
            nombre="Administrador",
            descripcion="Cargo de administrador"
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            dpi='1234567890123',
            cargo=self.cargo
        )

        self.grupo = Group.objects.create(name='Admin')
        self.user.groups.add(self.grupo)

    def test_crear_notificacion_basica(self):
        """Test para crear una notificación básica."""
        notificacion = Notificacion.objects.create(
            mensaje="Test de notificación",
            tipo="info",
            creado_por=self.user
        )

        self.assertEqual(notificacion.mensaje, "Test de notificación")
        self.assertEqual(notificacion.tipo, "info")
        self.assertEqual(notificacion.creado_por, self.user)
        self.assertTrue(notificacion.is_active)
        self.assertIsNotNone(notificacion.fecha_creacion)

    def test_notificacion_con_titulo_y_url(self):
        """Test para notificación con título y URL de acción."""
        notificacion = Notificacion.objects.create(
            titulo="Título de prueba",
            mensaje="Mensaje de prueba",
            tipo="success",
            url_accion="https://example.com/action",
            creado_por=self.user
        )

        self.assertEqual(notificacion.titulo, "Título de prueba")
        self.assertEqual(notificacion.url_accion, "https://example.com/action")

    def test_validacion_mensaje_minimo(self):
        """Test para validación de longitud mínima del mensaje."""
        with self.assertRaises(ValidationError):
            notificacion = Notificacion(
                mensaje="abc",  # Muy corto
                tipo="info",
                creado_por=self.user
            )
            notificacion.full_clean()

    def test_get_tipo_display_color(self):
        """Test para el método get_tipo_display_color."""
        notificacion = Notificacion.objects.create(
            mensaje="Test",
            tipo="error",
            creado_por=self.user
        )

        self.assertEqual(notificacion.get_tipo_display_color(), "danger")

    def test_get_icono(self):
        """Test para el método get_icono."""
        notificacion = Notificacion.objects.create(
            mensaje="Test",
            tipo="warning",
            creado_por=self.user
        )

        self.assertEqual(notificacion.get_icono(), "fas fa-exclamation-triangle")

    def test_enviar_a_usuario(self):
        """Test para el método enviar_a_usuario."""
        notificacion = Notificacion.objects.create(
            mensaje="Test para usuario",
            tipo="info",
            creado_por=self.user
        )

        notif_usuario = notificacion.enviar_a_usuario(self.user)

        self.assertIsInstance(notif_usuario, NotificacionUsuario)
        self.assertEqual(notif_usuario.notificacion, notificacion)
        self.assertEqual(notif_usuario.usuario, self.user)
        self.assertFalse(notif_usuario.leida)

    def test_enviar_a_grupo(self):
        """Test para el método enviar_a_grupo."""
        notificacion = Notificacion.objects.create(
            mensaje="Test para grupo",
            tipo="info",
            creado_por=self.user
        )

        notif_grupo = notificacion.enviar_a_grupo(self.grupo)

        self.assertIsInstance(notif_grupo, NotificacionGrupo)
        self.assertEqual(notif_grupo.notificacion, notificacion)
        self.assertEqual(notif_grupo.grupo, self.grupo)


class NotificacionUsuarioModelTest(TestCase):
    """
    Tests para el modelo NotificacionUsuario.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        self.cargo = CargoUsuario.objects.create(
            nombre="Empleado",
            descripcion="Cargo de empleado"
        )

        self.user = User.objects.create_user(
            username='empleado',
            email='<EMAIL>',
            password='testpass123',
            dpi='1234567890124',
            cargo=self.cargo
        )

        self.notificacion = Notificacion.objects.create(
            mensaje="Notificación de prueba",
            tipo="info",
            creado_por=self.user
        )

    def test_crear_notificacion_usuario(self):
        """Test para crear NotificacionUsuario."""
        notif_usuario = NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.user
        )

        self.assertEqual(notif_usuario.notificacion, self.notificacion)
        self.assertEqual(notif_usuario.usuario, self.user)
        self.assertFalse(notif_usuario.leida)
        self.assertIsNone(notif_usuario.fecha_lectura)
        self.assertIsNotNone(notif_usuario.fecha_envio)

    def test_marcar_como_leida(self):
        """Test para el método marcar_como_leida."""
        notif_usuario = NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.user
        )

        # Verificar estado inicial
        self.assertFalse(notif_usuario.leida)
        self.assertIsNone(notif_usuario.fecha_lectura)

        # Marcar como leída
        notif_usuario.marcar_como_leida()

        # Verificar cambios
        self.assertTrue(notif_usuario.leida)
        self.assertIsNotNone(notif_usuario.fecha_lectura)

    def test_get_tiempo_sin_leer(self):
        """Test para el método get_tiempo_sin_leer."""
        notif_usuario = NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.user
        )

        # Sin leer - debe retornar timedelta
        tiempo = notif_usuario.get_tiempo_sin_leer()
        self.assertIsNotNone(tiempo)

        # Marcar como leída
        notif_usuario.marcar_como_leida()

        # Leída - debe retornar None
        tiempo = notif_usuario.get_tiempo_sin_leer()
        self.assertIsNone(tiempo)

    def test_unique_together_constraint(self):
        """Test para la restricción unique_together."""
        # Crear primera notificación
        NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.user
        )

        # Intentar crear duplicado - debe fallar
        with self.assertRaises(Exception):
            NotificacionUsuario.objects.create(
                notificacion=self.notificacion,
                usuario=self.user
            )


class NotificacionGrupoModelTest(TestCase):
    """
    Tests para el modelo NotificacionGrupo.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        self.cargo = CargoUsuario.objects.create(
            nombre="Supervisor",
            descripcion="Cargo de supervisor"
        )

        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123',
            dpi='1234567890125',
            cargo=self.cargo
        )

        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123',
            dpi='1234567890126',
            cargo=self.cargo
        )

        self.grupo = Group.objects.create(name='Supervisores')
        self.user1.groups.add(self.grupo)
        self.user2.groups.add(self.grupo)

        self.notificacion = Notificacion.objects.create(
            mensaje="Notificación para grupo",
            tipo="info",
            creado_por=self.user1
        )

    def test_crear_notificacion_grupo(self):
        """Test para crear NotificacionGrupo."""
        notif_grupo = NotificacionGrupo.objects.create(
            notificacion=self.notificacion,
            grupo=self.grupo
        )

        self.assertEqual(notif_grupo.notificacion, self.notificacion)
        self.assertEqual(notif_grupo.grupo, self.grupo)
        self.assertIsNotNone(notif_grupo.fecha_envio)

    def test_crear_notificaciones_individuales(self):
        """Test para el método crear_notificaciones_individuales."""
        notif_grupo = NotificacionGrupo.objects.create(
            notificacion=self.notificacion,
            grupo=self.grupo
        )

        # Crear notificaciones individuales
        count = notif_grupo.crear_notificaciones_individuales()

        # Verificar que se crearon para todos los miembros activos
        self.assertEqual(count, 2)  # user1 y user2

        # Verificar que existen las notificaciones individuales
        notif_user1 = NotificacionUsuario.objects.get(
            notificacion=self.notificacion,
            usuario=self.user1
        )
        notif_user2 = NotificacionUsuario.objects.get(
            notificacion=self.notificacion,
            usuario=self.user2
        )

        self.assertIsNotNone(notif_user1)
        self.assertIsNotNone(notif_user2)

    def test_no_duplicar_notificaciones_individuales(self):
        """Test para evitar duplicar notificaciones individuales."""
        notif_grupo = NotificacionGrupo.objects.create(
            notificacion=self.notificacion,
            grupo=self.grupo
        )

        # Primera creación
        count1 = notif_grupo.crear_notificaciones_individuales()
        self.assertEqual(count1, 2)

        # Segunda creación - no debe crear duplicados
        count2 = notif_grupo.crear_notificaciones_individuales()
        self.assertEqual(count2, 0)

        # Verificar que solo hay 2 notificaciones individuales
        total = NotificacionUsuario.objects.filter(
            notificacion=self.notificacion
        ).count()
        self.assertEqual(total, 2)


class NotificacionViewsTest(TestCase):
    """
    Tests para las vistas de notificaciones.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        self.client = Client()

        # Crear cargo y usuarios
        self.cargo_admin = CargoUsuario.objects.create(
            nombre="Administrador",
            descripcion="Cargo de administrador"
        )

        self.cargo_empleado = CargoUsuario.objects.create(
            nombre="Empleado",
            descripcion="Cargo de empleado"
        )

        # Usuario administrador
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            dpi='1234567890127',
            cargo=self.cargo_admin
        )

        # Usuario empleado
        self.empleado_user = User.objects.create_user(
            username='empleado',
            email='<EMAIL>',
            password='empleadopass123',
            dpi='1234567890128',
            cargo=self.cargo_empleado
        )

        # Crear grupos
        self.admin_group = Group.objects.create(name='Admin')
        self.empleado_group = Group.objects.create(name='Empleado')

        self.admin_user.groups.add(self.admin_group)
        self.empleado_user.groups.add(self.empleado_group)

        # Crear notificación de prueba
        self.notificacion = Notificacion.objects.create(
            mensaje="Notificación de prueba",
            tipo="info",
            creado_por=self.admin_user
        )

    def test_lista_notificaciones_requiere_login(self):
        """Test que la lista de notificaciones requiere login."""
        response = self.client.get(reverse('notificaciones:lista_notificaciones'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_lista_notificaciones_admin(self):
        """Test que el admin puede ver la lista de notificaciones."""
        self.client.login(username='admin', password='adminpass123')
        response = self.client.get(reverse('notificaciones:lista_notificaciones'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Gestión de Notificaciones')

    def test_mis_notificaciones_view(self):
        """Test para la vista de mis notificaciones."""
        # Crear notificación para el empleado
        NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.empleado_user
        )

        self.client.login(username='empleado', password='empleadopass123')
        response = self.client.get(reverse('notificaciones:mis_notificaciones'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Mis Notificaciones')
        self.assertContains(response, 'Notificación de prueba')

    def test_crear_notificacion_usuario_get(self):
        """Test GET para crear notificación a usuario."""
        self.client.login(username='admin', password='adminpass123')
        response = self.client.get(reverse('notificaciones:crear_notificacion_usuario'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Crear Notificación para Usuarios')

    def test_crear_notificacion_usuario_post(self):
        """Test POST para crear notificación a usuario."""
        self.client.login(username='admin', password='adminpass123')

        data = {
            'titulo': 'Test Notificación',
            'mensaje': 'Este es un mensaje de prueba para el usuario',
            'tipo': 'info',
            'usuarios': [self.empleado_user.id]
        }

        response = self.client.post(
            reverse('notificaciones:crear_notificacion_usuario'),
            data
        )

        # Verificar redirección exitosa
        self.assertEqual(response.status_code, 302)

        # Verificar que se creó la notificación
        notificacion = Notificacion.objects.filter(
            titulo='Test Notificación'
        ).first()
        self.assertIsNotNone(notificacion)

        # Verificar que se creó la notificación individual
        notif_usuario = NotificacionUsuario.objects.filter(
            notificacion=notificacion,
            usuario=self.empleado_user
        ).first()
        self.assertIsNotNone(notif_usuario)

    def test_marcar_leida_ajax(self):
        """Test para marcar notificación como leída via AJAX."""
        # Crear notificación para el empleado
        notif_usuario = NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.empleado_user
        )

        self.client.login(username='empleado', password='empleadopass123')

        response = self.client.post(
            reverse('notificaciones:marcar_leida_ajax'),
            {'notificacion_id': notif_usuario.id},
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )

        self.assertEqual(response.status_code, 200)

        # Verificar respuesta JSON
        data = json.loads(response.content)
        self.assertTrue(data['success'])

        # Verificar que se marcó como leída
        notif_usuario.refresh_from_db()
        self.assertTrue(notif_usuario.leida)

    def test_contar_no_leidas_ajax(self):
        """Test para contar notificaciones no leídas via AJAX."""
        # Crear notificaciones para el empleado
        NotificacionUsuario.objects.create(
            notificacion=self.notificacion,
            usuario=self.empleado_user
        )

        notificacion2 = Notificacion.objects.create(
            mensaje="Segunda notificación",
            tipo="warning",
            creado_por=self.admin_user
        )

        NotificacionUsuario.objects.create(
            notificacion=notificacion2,
            usuario=self.empleado_user
        )

        self.client.login(username='empleado', password='empleadopass123')

        response = self.client.get(reverse('notificaciones:contar_no_leidas'))
        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertEqual(data['count'], 2)
        self.assertTrue(data['has_notifications'])


class NotificacionFormsTest(TestCase):
    """
    Tests para los formularios de notificaciones.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        self.cargo = CargoUsuario.objects.create(
            nombre="Administrador",
            descripcion="Cargo de administrador"
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            dpi='1234567890129',
            cargo=self.cargo
        )

        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123',
            dpi='1234567890130',
            cargo=self.cargo
        )

        self.grupo = Group.objects.create(name='TestGroup')
        self.user.groups.add(self.grupo)
        self.user2.groups.add(self.grupo)

    def test_notificacion_usuario_form_valid(self):
        """Test para formulario válido de notificación a usuario."""
        form_data = {
            'titulo': 'Test Título',
            'mensaje': 'Este es un mensaje de prueba válido',
            'tipo': 'info',
            'usuarios': [self.user.id, self.user2.id]
        }

        form = NotificacionUsuarioForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())

    def test_notificacion_usuario_form_mensaje_corto(self):
        """Test para mensaje muy corto en formulario de usuario."""
        form_data = {
            'mensaje': 'abc',  # Muy corto
            'tipo': 'info',
            'usuarios': [self.user.id]
        }

        form = NotificacionUsuarioForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('mensaje', form.errors)

    def test_notificacion_usuario_form_sin_usuarios(self):
        """Test para formulario sin usuarios seleccionados."""
        form_data = {
            'mensaje': 'Mensaje válido de prueba',
            'tipo': 'info',
            'usuarios': []
        }

        form = NotificacionUsuarioForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('usuarios', form.errors)

    def test_notificacion_grupo_form_valid(self):
        """Test para formulario válido de notificación a grupo."""
        form_data = {
            'titulo': 'Test Grupo',
            'mensaje': 'Mensaje para grupo de prueba',
            'tipo': 'warning',
            'grupos': [self.grupo.id],
            'crear_individuales': True
        }

        form = NotificacionGrupoForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())

    def test_notificacion_grupo_form_sin_grupos(self):
        """Test para formulario sin grupos seleccionados."""
        form_data = {
            'mensaje': 'Mensaje válido de prueba',
            'tipo': 'info',
            'grupos': [],
            'crear_individuales': True
        }

        form = NotificacionGrupoForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('grupos', form.errors)

    def test_notificacion_masiva_form_valid(self):
        """Test para formulario válido de notificación masiva."""
        form_data = {
            'mensaje': 'Mensaje masivo de prueba',
            'tipo': 'success',
            'usuarios': [self.user.id],
            'grupos': [self.grupo.id],
            'todos_los_usuarios': False
        }

        form = NotificacionMasivaForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())

    def test_notificacion_masiva_form_sin_destinatarios(self):
        """Test para formulario masivo sin destinatarios."""
        form_data = {
            'mensaje': 'Mensaje masivo de prueba',
            'tipo': 'success',
            'usuarios': [],
            'grupos': [],
            'todos_los_usuarios': False
        }

        form = NotificacionMasivaForm(data=form_data, user=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_notificacion_masiva_form_todos_usuarios(self):
        """Test para formulario masivo con todos los usuarios."""
        form_data = {
            'mensaje': 'Mensaje para todos los usuarios',
            'tipo': 'info',
            'usuarios': [],
            'grupos': [],
            'todos_los_usuarios': True
        }

        form = NotificacionMasivaForm(data=form_data, user=self.user)
        self.assertTrue(form.is_valid())


class NotificacionPermissionsTest(TestCase):
    """
    Tests para permisos de notificaciones.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        # Crear cargos
        self.cargo_admin = CargoUsuario.objects.create(
            nombre="Administrador",
            descripcion="Cargo de administrador"
        )

        self.cargo_empleado = CargoUsuario.objects.create(
            nombre="Empleado",
            descripcion="Cargo de empleado"
        )

        # Crear usuarios
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            dpi='1234567890131',
            cargo=self.cargo_admin
        )

        self.empleado_user = User.objects.create_user(
            username='empleado',
            email='<EMAIL>',
            password='empleadopass123',
            dpi='1234567890132',
            cargo=self.cargo_empleado
        )

        # Crear grupos
        self.admin_group = Group.objects.create(name='Admin')
        self.empleado_group = Group.objects.create(name='Empleado')

        self.admin_user.groups.add(self.admin_group)
        self.empleado_user.groups.add(self.empleado_group)

        self.client = Client()

    def test_empleado_no_puede_crear_notificaciones(self):
        """Test que empleado no puede acceder a crear notificaciones."""
        self.client.login(username='empleado', password='empleadopass123')

        response = self.client.get(reverse('notificaciones:crear_notificacion_usuario'))
        # Debe redirigir por falta de permisos
        self.assertEqual(response.status_code, 302)

    def test_empleado_no_puede_crear_masivas(self):
        """Test que empleado no puede crear notificaciones masivas."""
        self.client.login(username='empleado', password='empleadopass123')

        response = self.client.get(reverse('notificaciones:crear_notificacion_masiva'))
        # Debe redirigir por falta de permisos
        self.assertEqual(response.status_code, 302)

    def test_admin_puede_crear_notificaciones(self):
        """Test que admin puede crear notificaciones."""
        self.client.login(username='admin', password='adminpass123')

        response = self.client.get(reverse('notificaciones:crear_notificacion_usuario'))
        self.assertEqual(response.status_code, 200)

    def test_admin_puede_crear_masivas(self):
        """Test que admin puede crear notificaciones masivas."""
        self.client.login(username='admin', password='adminpass123')

        response = self.client.get(reverse('notificaciones:crear_notificacion_masiva'))
        self.assertEqual(response.status_code, 200)

    def test_empleado_puede_ver_sus_notificaciones(self):
        """Test que empleado puede ver sus propias notificaciones."""
        self.client.login(username='empleado', password='empleadopass123')

        response = self.client.get(reverse('notificaciones:mis_notificaciones'))
        self.assertEqual(response.status_code, 200)


class NotificacionIntegrationTest(TestCase):
    """
    Tests de integración para el sistema completo de notificaciones.
    """

    def setUp(self):
        """Configuración inicial para los tests."""
        self.cargo = CargoUsuario.objects.create(
            nombre="Administrador",
            descripcion="Cargo de administrador"
        )

        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            dpi='1234567890133',
            cargo=self.cargo
        )

        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='userpass123',
            dpi='1234567890134',
            cargo=self.cargo
        )

        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='userpass123',
            dpi='1234567890135',
            cargo=self.cargo
        )

        self.grupo = Group.objects.create(name='TestGroup')
        self.admin_user.groups.add(self.grupo)
        self.user1.groups.add(self.grupo)
        self.user2.groups.add(self.grupo)

        self.client = Client()

    def test_flujo_completo_notificacion_grupo(self):
        """Test del flujo completo: crear notificación para grupo y expandir a usuarios."""
        self.client.login(username='admin', password='adminpass123')

        # 1. Crear notificación para grupo
        form_data = {
            'titulo': 'Notificación de Grupo',
            'mensaje': 'Esta es una notificación para todo el grupo de prueba',
            'tipo': 'info',
            'grupos': [self.grupo.id],
            'crear_individuales': True
        }

        response = self.client.post(
            reverse('notificaciones:crear_notificacion_grupo'),
            form_data
        )

        # Verificar redirección exitosa
        self.assertEqual(response.status_code, 302)

        # 2. Verificar que se creó la notificación
        notificacion = Notificacion.objects.filter(
            titulo='Notificación de Grupo'
        ).first()
        self.assertIsNotNone(notificacion)

        # 3. Verificar que se creó la notificación de grupo
        notif_grupo = NotificacionGrupo.objects.filter(
            notificacion=notificacion,
            grupo=self.grupo
        ).first()
        self.assertIsNotNone(notif_grupo)

        # 4. Verificar que se crearon notificaciones individuales
        notificaciones_individuales = NotificacionUsuario.objects.filter(
            notificacion=notificacion
        )
        self.assertEqual(notificaciones_individuales.count(), 3)  # admin, user1, user2

        # 5. Verificar que los usuarios pueden ver sus notificaciones
        self.client.login(username='user1', password='userpass123')
        response = self.client.get(reverse('notificaciones:mis_notificaciones'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Notificación de Grupo')

    def test_marcar_leida_y_contar(self):
        """Test para marcar como leída y verificar contadores."""
        # Crear notificación
        notificacion = Notificacion.objects.create(
            mensaje="Test de lectura",
            tipo="info",
            creado_por=self.admin_user
        )

        notif_usuario = NotificacionUsuario.objects.create(
            notificacion=notificacion,
            usuario=self.user1
        )

        self.client.login(username='user1', password='userpass123')

        # Verificar contador inicial
        response = self.client.get(reverse('notificaciones:contar_no_leidas'))
        data = json.loads(response.content)
        self.assertEqual(data['count'], 1)

        # Marcar como leída
        response = self.client.post(
            reverse('notificaciones:marcar_leida_ajax'),
            {'notificacion_id': notif_usuario.id}
        )
        self.assertEqual(response.status_code, 200)

        # Verificar contador después de marcar como leída
        response = self.client.get(reverse('notificaciones:contar_no_leidas'))
        data = json.loads(response.content)
        self.assertEqual(data['count'], 0)
