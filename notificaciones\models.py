"""
notificaciones/models.py
Modelos para el sistema de notificaciones del sistema.

Incluye:
- Notificacion: Notificaciones generales del sistema
- NotificacionUsuario: Notificaciones dirigidas a usuarios específicos
- NotificacionGrupo: Notificaciones dirigidas a grupos
"""

from django.db import models
from django.conf import settings
from django.contrib.auth.models import Group
from django.utils import timezone
from django.core.validators import MinLengthValidator


class Notificacion(models.Model):
    """
    Modelo base para las notificaciones del sistema.

    Almacena el contenido de las notificaciones que pueden ser
    enviadas a usuarios específicos o grupos.
    """

    TIPO_CHOICES = (
        ('info', 'Información'),
        ('warning', 'Advertencia'),
        ('success', 'Éxito'),
        ('error', 'Error'),
        ('ticket', 'Ticket'),
    )

    mensaje = models.CharField(
        max_length=500,
        validators=[MinLengthValidator(5)],
        help_text='Mensaje de la notificación (máximo 500 caracteres)'
    )
    tipo = models.CharField(
        max_length=20,
        choices=TIPO_CHOICES,
        default='info',
        help_text='Tipo de notificación',
        db_index=True
    )
    ticket = models.ForeignKey(
        'tickets.Ticket',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='notificaciones',
        help_text='Ticket relacionado (opcional)'
    )
    fecha_creacion = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de creación de la notificación',
        db_index=True
    )
    creado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='notificaciones_creadas',
        help_text='Usuario que creó la notificación'
    )

    # Campos adicionales
    titulo = models.CharField(
        max_length=200,
        blank=True,
        help_text='Título opcional de la notificación'
    )
    url_accion = models.URLField(
        blank=True,
        help_text='URL de acción opcional (para redireccionar)'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Indica si la notificación está activa'
    )

    class Meta:
        db_table = 'notificacion'
        verbose_name = 'Notificación'
        verbose_name_plural = 'Notificaciones'
        ordering = ['-fecha_creacion']
        indexes = [
            models.Index(fields=['tipo', 'fecha_creacion'], name='idx_notif_tipo_fecha'),
            models.Index(fields=['ticket'], name='idx_notif_ticket'),
            models.Index(fields=['is_active'], name='idx_notif_activa'),
        ]

    def __str__(self):
        return f"{self.get_tipo_display()}: {self.mensaje[:50]}..."

    def get_tipo_display_color(self):
        """
        Retorna el color CSS asociado al tipo de notificación.

        Returns:
            str: Clase CSS para el color del tipo
        """
        colors = {
            'info': 'info',
            'warning': 'warning',
            'success': 'success',
            'error': 'danger',
            'ticket': 'primary',
        }
        return colors.get(self.tipo, 'secondary')

    def get_icono(self):
        """
        Retorna el icono FontAwesome asociado al tipo de notificación.

        Returns:
            str: Clase CSS del icono
        """
        iconos = {
            'info': 'fas fa-info-circle',
            'warning': 'fas fa-exclamation-triangle',
            'success': 'fas fa-check-circle',
            'error': 'fas fa-times-circle',
            'ticket': 'fas fa-ticket-alt',
        }
        return iconos.get(self.tipo, 'fas fa-bell')

    def enviar_a_usuario(self, usuario, leida=False):
        """
        Envía la notificación a un usuario específico.

        Args:
            usuario: Usuario destinatario
            leida: Si la notificación debe marcarse como leída

        Returns:
            NotificacionUsuario: Instancia creada
        """
        return NotificacionUsuario.objects.create(
            notificacion=self,
            usuario=usuario,
            leida=leida
        )

    def enviar_a_grupo(self, grupo):
        """
        Envía la notificación a un grupo específico.

        Args:
            grupo: Grupo destinatario

        Returns:
            NotificacionGrupo: Instancia creada
        """
        return NotificacionGrupo.objects.create(
            notificacion=self,
            grupo=grupo
        )


class NotificacionUsuario(models.Model):
    """
    Modelo para notificaciones dirigidas a usuarios específicos.

    Permite el seguimiento individual de qué notificaciones ha
    recibido cada usuario y si las ha leído.
    """

    notificacion = models.ForeignKey(
        Notificacion,
        on_delete=models.CASCADE,
        related_name='usuarios_notificados',
        help_text='Notificación enviada'
    )
    usuario = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notificaciones_recibidas',
        help_text='Usuario que recibe la notificación'
    )
    fecha_envio = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de envío de la notificación'
    )
    fecha_lectura = models.DateTimeField(
        blank=True,
        null=True,
        help_text='Fecha y hora de lectura de la notificación'
    )
    leida = models.BooleanField(
        default=False,
        help_text='Indica si la notificación ha sido leída',
        db_index=True
    )

    class Meta:
        db_table = 'notificacion_usuario'
        verbose_name = 'Notificación de Usuario'
        verbose_name_plural = 'Notificaciones de Usuarios'
        ordering = ['-fecha_envio']
        unique_together = ['notificacion', 'usuario']
        indexes = [
            models.Index(fields=['usuario', 'leida'], name='idx_notif_usuario_leida'),
            models.Index(fields=['fecha_envio'], name='idx_notif_usuario_fecha'),
        ]

    def __str__(self):
        estado = "Leída" if self.leida else "No leída"
        return f"{self.notificacion.mensaje[:30]}... → {self.usuario.username} ({estado})"

    def marcar_como_leida(self):
        """
        Marca la notificación como leída y registra la fecha de lectura.
        """
        if not self.leida:
            self.leida = True
            self.fecha_lectura = timezone.now()
            self.save()

    def get_tiempo_sin_leer(self):
        """
        Calcula el tiempo que ha pasado sin leer la notificación.

        Returns:
            timedelta: Tiempo transcurrido o None si ya fue leída
        """
        if self.leida:
            return None
        return timezone.now() - self.fecha_envio


class NotificacionGrupo(models.Model):
    """
    Modelo para notificaciones dirigidas a grupos específicos.

    Permite enviar notificaciones a todos los miembros de un grupo
    de forma eficiente.
    """

    notificacion = models.ForeignKey(
        Notificacion,
        on_delete=models.CASCADE,
        related_name='grupos_notificados',
        help_text='Notificación enviada'
    )
    grupo = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='notificaciones_recibidas',
        help_text='Grupo que recibe la notificación'
    )
    fecha_envio = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de envío de la notificación'
    )

    class Meta:
        db_table = 'notificacion_grupo'
        verbose_name = 'Notificación de Grupo'
        verbose_name_plural = 'Notificaciones de Grupos'
        ordering = ['-fecha_envio']
        unique_together = ['notificacion', 'grupo']
        indexes = [
            models.Index(fields=['grupo', 'fecha_envio'], name='idx_notif_grupo_fecha'),
        ]

    def __str__(self):
        return f"{self.notificacion.mensaje[:30]}... → Grupo: {self.grupo.name}"

    def crear_notificaciones_individuales(self):
        """
        Crea notificaciones individuales para todos los miembros del grupo.

        Returns:
            int: Número de notificaciones individuales creadas
        """
        usuarios = self.grupo.user_set.filter(is_active=True)
        notificaciones_creadas = 0

        for usuario in usuarios:
            # Evitar duplicados
            if not NotificacionUsuario.objects.filter(
                notificacion=self.notificacion,
                usuario=usuario
            ).exists():
                NotificacionUsuario.objects.create(
                    notificacion=self.notificacion,
                    usuario=usuario
                )
                notificaciones_creadas += 1

        return notificaciones_creadas
