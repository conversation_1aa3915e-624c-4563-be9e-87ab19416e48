"""
reportes/models.py
Modelos para el sistema de reportes.

Este módulo contiene los modelos necesarios para generar reportes
de tickets por empleado, área, ciudadano y reportes generales.
"""

from django.db import models
from django.contrib.auth.models import Group
from django.conf import settings
from django.utils import timezone
from ciudadano.models import Ciudadano
from tickets.models import Ticket
from asignaciones.models import AsignacionTicket


class ReporteGenerado(models.Model):
    """
    Modelo para llevar registro de los reportes generados.
    Permite auditoría y control de acceso a reportes.
    """
    
    TIPO_REPORTE_CHOICES = (
        ('empleado', 'Por Empleado'),
        ('area', 'Por Área'),
        ('ciudadano', 'Por Ciudadano'),
        ('general', 'General'),
    )
    
    FORMATO_CHOICES = (
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
    )
    
    # Información básica del reporte
    tipo_reporte = models.CharField(
        max_length=20,
        choices=TIPO_REPORTE_CHOICES,
        help_text='Tipo de reporte generado'
    )
    formato = models.CharField(
        max_length=10,
        choices=FORMATO_CHOICES,
        help_text='Formato del reporte'
    )
    
    # Filtros aplicados
    fecha_inicio = models.DateField(
        null=True,
        blank=True,
        help_text='Fecha de inicio del rango (opcional)'
    )
    fecha_fin = models.DateField(
        null=True,
        blank=True,
        help_text='Fecha de fin del rango (opcional)'
    )
    
    # Entidades incluidas (JSON para flexibilidad)
    entidades_incluidas = models.JSONField(
        default=dict,
        help_text='IDs de empleados, áreas o ciudadanos incluidos'
    )
    
    # Metadatos
    generado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reportes_generados',
        help_text='Usuario que generó el reporte'
    )
    fecha_generacion = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de generación'
    )
    
    # Información del archivo
    nombre_archivo = models.CharField(
        max_length=255,
        help_text='Nombre del archivo generado'
    )
    tamaño_archivo = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text='Tamaño del archivo en bytes'
    )
    
    class Meta:
        db_table = 'reporte_generado'
        verbose_name = 'Reporte Generado'
        verbose_name_plural = 'Reportes Generados'
        ordering = ['-fecha_generacion']
        indexes = [
            models.Index(fields=['tipo_reporte', 'formato'], name='idx_reporte_tipo_formato'),
            models.Index(fields=['generado_por', 'fecha_generacion'], name='idx_reporte_usuario_fecha'),
            models.Index(fields=['fecha_generacion'], name='idx_reporte_fecha'),
        ]
    
    def __str__(self):
        return f"{self.get_tipo_reporte_display()} - {self.get_formato_display()} - {self.fecha_generacion.strftime('%d/%m/%Y %H:%M')}"
    
    def get_rango_fechas_display(self):
        """Retorna una representación legible del rango de fechas."""
        if self.fecha_inicio and self.fecha_fin:
            return f"Del {self.fecha_inicio.strftime('%d/%m/%Y')} al {self.fecha_fin.strftime('%d/%m/%Y')}"
        elif self.fecha_inicio:
            return f"Desde {self.fecha_inicio.strftime('%d/%m/%Y')}"
        elif self.fecha_fin:
            return f"Hasta {self.fecha_fin.strftime('%d/%m/%Y')}"
        else:
            return "Todo el tiempo"
