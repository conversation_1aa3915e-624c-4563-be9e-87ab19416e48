/* Sidebar Moderno y Mejorado */

/* Contenedor principal del sidebar */
#sidebar-wrapper {
    background: linear-gradient(135deg, #1A237E 0%, #283593 50%, #3F51B5 100%);
    width: 280px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 4px 0 20px rgba(0,0,0,0.15);
    overflow: hidden;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

#sidebar-wrapper.collapsed {
    width: 0;
}

/* Contenido del sidebar con scroll personalizado */
.sidebar-content {
    width: 280px;
    min-height: 100vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255,255,255,0.3) transparent;
}

.sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* Header del sidebar */
.sidebar-heading {
    background: rgba(0,0,0,0.2);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    backdrop-filter: blur(5px);
}

.sidebar-heading h5 {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Items del menú principal */
.list-group-item {
    background: transparent !important;
    border: none !important;
    color: rgba(255,255,255,0.9) !important;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    border-radius: 0;
    position: relative;
    overflow: hidden;
}

.list-group-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(45deg, #FF9800, #FFC107);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.list-group-item:hover {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.list-group-item:hover::before {
    transform: scaleY(1);
}

.list-group-item.active {
    background: rgba(255,255,255,0.15) !important;
    color: white !important;
}

.list-group-item.active::before {
    transform: scaleY(1);
}

/* Iconos de colores en el menú */
.list-group-item i.text-primary { color: #64B5F6 !important; }
.list-group-item i.text-info { color: #4FC3F7 !important; }
.list-group-item i.text-warning { color: #FFB74D !important; }
.list-group-item i.text-success { color: #81C784 !important; }
.list-group-item i.text-danger { color: #E57373 !important; }
.list-group-item i.text-secondary { color: #B0BEC5 !important; }
.list-group-item i.text-light { color: #F5F5F5 !important; }

/* Animación de chevron */
.transition-icon {
    transition: transform 0.3s ease;
}

.dropdown-toggle-custom[aria-expanded="true"] .transition-icon {
    transform: rotate(180deg);
}

/* Menús desplegables */
.dropdown-menu-custom {
    background: rgba(0,0,0,0.3);
    border: none;
    border-radius: 0;
    margin: 0;
    padding: 0;
    backdrop-filter: blur(5px);
}

.dropdown-item-custom {
    color: rgba(255,255,255,0.8) !important;
    padding: 0.75rem 2.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    border: none;
    background: transparent;
    position: relative;
}

.dropdown-item-custom::before {
    content: '';
    position: absolute;
    left: 2rem;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: rgba(255,255,255,0.4);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.dropdown-item-custom:hover {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
    padding-left: 3rem;
}

.dropdown-item-custom:hover::before {
    background: #FF9800;
    transform: translateY(-50%) scale(1.5);
}

/* Separador */
.sidebar-divider {
    border-top: 1px solid rgba(255,255,255,0.2);
    margin: 1rem 1.5rem;
    position: relative;
}

.sidebar-divider::after {
    content: '';
    position: absolute;
    left: 50%;
    top: -2px;
    transform: translateX(-50%);
    width: 30px;
    height: 4px;
    background: linear-gradient(45deg, #FF9800, #FFC107);
    border-radius: 2px;
}

/* Badge de notificaciones */
.badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
    #sidebar-wrapper {
        width: 0;
        transform: translateX(-100%);
    }
    
    #sidebar-wrapper.active {
        width: 280px;
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* Animaciones adicionales */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.list-group-item {
    animation: slideInLeft 0.3s ease forwards;
}

.list-group-item:nth-child(1) { animation-delay: 0.1s; }
.list-group-item:nth-child(2) { animation-delay: 0.2s; }
.list-group-item:nth-child(3) { animation-delay: 0.3s; }
.list-group-item:nth-child(4) { animation-delay: 0.4s; }
.list-group-item:nth-child(5) { animation-delay: 0.5s; }
.list-group-item:nth-child(6) { animation-delay: 0.6s; }
.list-group-item:nth-child(7) { animation-delay: 0.7s; }

/* Efectos de glassmorphism */
.sidebar-heading,
.dropdown-menu-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Hover effects mejorados */
.list-group-item:hover {
    box-shadow:
        0 4px 15px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.1);
}

/* Footer del sidebar */
.sidebar-footer {
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
    backdrop-filter: blur(5px);
}

.user-info {
    transition: all 0.3s ease;
}

.user-info:hover {
    transform: translateY(-2px);
}

.user-info i {
    transition: all 0.3s ease;
}

.user-info:hover i {
    color: rgba(255,255,255,0.9) !important;
    transform: scale(1.1);
}

/* Ajustes para el contenido del sidebar */
.sidebar-content {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.list-group {
    flex: 1;
    overflow-y: auto;
}

/* Divider en dropdowns */
.dropdown-divider {
    border-top: 1px solid rgba(255,255,255,0.1);
    margin: 0.5rem 0;
}

/* Cerrar sesión con estilo especial */
.list-group-item.text-danger:hover {
    background: rgba(244, 67, 54, 0.2) !important;
    color: #ffcdd2 !important;
}

.list-group-item.text-danger:hover::before {
    background: #f44336;
}
