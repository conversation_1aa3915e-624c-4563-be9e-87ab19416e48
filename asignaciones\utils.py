"""
Utilidades para el sistema de asignaciones.

Incluye funciones para crear notificaciones automáticas
cuando se realizan acciones en las asignaciones.
"""

from django.contrib.auth.models import Group
from django.urls import reverse
from notificaciones.models import Notificacion


def crear_notificacion_asignacion_grupo(ticket, usuario_asignador):
    """
    Crea notificación automática cuando se asigna un ticket a un grupo.
    Solo visible para supervisores del grupo.
    
    Args:
        ticket: Instancia del ticket asignado
        usuario_asignador: Usuario que realizó la asignación
    """
    try:
        # Crear la notificación
        notificacion = Notificacion.objects.create(
            titulo=f"Nuevo ticket asignado al área {ticket.grupo.name}",
            mensaje=f"El ticket #{ticket.id} '{ticket.titulo}' ha sido asignado al área {ticket.grupo.name} por {usuario_asignador.get_full_name() or usuario_asignador.username}",
            tipo='ticket',
            ticket=ticket,
            creado_por=usuario_asignador,
            url_accion=reverse('tickets:detalle_ticket', args=[ticket.id])
        )
        
        # Enviar solo a supervisores del grupo
        supervisores = ticket.grupo.user_set.filter(
            is_active=True,
            groups__name__in=['Supervisor', 'Admin']
        ).distinct()
        
        for supervisor in supervisores:
            notificacion.enviar_a_usuario(supervisor)
            
        return notificacion
        
    except Exception as e:
        print(f"Error creando notificación de asignación a grupo: {e}")
        return None


def crear_notificacion_asignacion_usuario(ticket, usuario_asignado, usuario_asignador, nota=None):
    """
    Crea notificación automática cuando se asigna un ticket a un usuario específico.
    Solo visible para el usuario asignado.
    
    Args:
        ticket: Instancia del ticket asignado
        usuario_asignado: Usuario al que se asignó el ticket
        usuario_asignador: Usuario que realizó la asignación
        nota: Nota opcional de la asignación
    """
    try:
        # Mensaje base
        mensaje = f"Se te ha asignado el ticket #{ticket.id} '{ticket.titulo}' por {usuario_asignador.get_full_name() or usuario_asignador.username}"
        
        # Agregar nota si existe
        if nota:
            mensaje += f". Nota: {nota}"
        
        # Crear la notificación
        notificacion = Notificacion.objects.create(
            titulo=f"Ticket #{ticket.id} asignado a ti",
            mensaje=mensaje,
            tipo='ticket',
            ticket=ticket,
            creado_por=usuario_asignador,
            url_accion=reverse('tickets:detalle_ticket', args=[ticket.id])
        )
        
        # Enviar solo al usuario asignado
        notificacion.enviar_a_usuario(usuario_asignado)
        
        return notificacion
        
    except Exception as e:
        print(f"Error creando notificación de asignación a usuario: {e}")
        return None


def crear_notificacion_cambio_estado(ticket, usuario, nuevo_estado, estado_anterior=None):
    """
    Crea notificación cuando cambia el estado de un ticket.
    
    Args:
        ticket: Instancia del ticket
        usuario: Usuario que cambió el estado
        nuevo_estado: Nuevo estado del ticket
        estado_anterior: Estado anterior (opcional)
    """
    try:
        estados_display = {
            1: 'Abierto',
            2: 'En Progreso', 
            3: 'Cerrado',
            4: 'Pendiente'
        }
        
        mensaje = f"El ticket #{ticket.id} '{ticket.titulo}' cambió a estado '{estados_display.get(nuevo_estado, nuevo_estado)}'"
        
        if estado_anterior:
            mensaje += f" desde '{estados_display.get(estado_anterior, estado_anterior)}'"
            
        mensaje += f" por {usuario.get_full_name() or usuario.username}"
        
        # Crear la notificación
        notificacion = Notificacion.objects.create(
            titulo=f"Cambio de estado - Ticket #{ticket.id}",
            mensaje=mensaje,
            tipo='info',
            ticket=ticket,
            creado_por=usuario,
            url_accion=reverse('tickets:detalle_ticket', args=[ticket.id])
        )
        
        # Enviar a supervisores del área
        supervisores = ticket.grupo.user_set.filter(
            is_active=True,
            groups__name__in=['Supervisor', 'Admin']
        ).distinct()
        
        for supervisor in supervisores:
            notificacion.enviar_a_usuario(supervisor)
            
        return notificacion
        
    except Exception as e:
        print(f"Error creando notificación de cambio de estado: {e}")
        return None


def crear_notificacion_trabajo_iniciado(asignacion):
    """
    Crea notificación cuando un usuario inicia trabajo en una asignación.
    
    Args:
        asignacion: Instancia de AsignacionTicket
    """
    try:
        mensaje = f"{asignacion.usuario.get_full_name() or asignacion.usuario.username} ha iniciado el trabajo en el ticket #{asignacion.ticket.id} '{asignacion.ticket.titulo}'"
        
        # Crear la notificación
        notificacion = Notificacion.objects.create(
            titulo=f"Trabajo iniciado - Ticket #{asignacion.ticket.id}",
            mensaje=mensaje,
            tipo='info',
            ticket=asignacion.ticket,
            creado_por=asignacion.usuario,
            url_accion=reverse('tickets:detalle_ticket', args=[asignacion.ticket.id])
        )
        
        # Enviar a supervisores del área
        supervisores = asignacion.ticket.grupo.user_set.filter(
            is_active=True,
            groups__name__in=['Supervisor', 'Admin']
        ).distinct()
        
        for supervisor in supervisores:
            if supervisor != asignacion.usuario:  # No enviar al mismo usuario
                notificacion.enviar_a_usuario(supervisor)
                
        return notificacion
        
    except Exception as e:
        print(f"Error creando notificación de trabajo iniciado: {e}")
        return None


def crear_notificacion_trabajo_finalizado(asignacion, nota=None):
    """
    Crea notificación cuando un usuario finaliza trabajo en una asignación.
    
    Args:
        asignacion: Instancia de AsignacionTicket
        nota: Nota de finalización (opcional)
    """
    try:
        mensaje = f"{asignacion.usuario.get_full_name() or asignacion.usuario.username} ha finalizado el trabajo en el ticket #{asignacion.ticket.id} '{asignacion.ticket.titulo}'"
        
        if nota:
            mensaje += f". Nota: {nota}"
        
        # Crear la notificación
        notificacion = Notificacion.objects.create(
            titulo=f"Trabajo finalizado - Ticket #{asignacion.ticket.id}",
            mensaje=mensaje,
            tipo='success',
            ticket=asignacion.ticket,
            creado_por=asignacion.usuario,
            url_accion=reverse('tickets:detalle_ticket', args=[asignacion.ticket.id])
        )
        
        # Enviar a supervisores del área
        supervisores = asignacion.ticket.grupo.user_set.filter(
            is_active=True,
            groups__name__in=['Supervisor', 'Admin']
        ).distinct()
        
        for supervisor in supervisores:
            if supervisor != asignacion.usuario:  # No enviar al mismo usuario
                notificacion.enviar_a_usuario(supervisor)
                
        return notificacion
        
    except Exception as e:
        print(f"Error creando notificación de trabajo finalizado: {e}")
        return None


def crear_notificacion_personalizada(titulo, mensaje, tipo, usuarios=None, grupos=None, ticket=None, creado_por=None, url_accion=None):
    """
    Crea una notificación personalizada.
    
    Args:
        titulo: Título de la notificación
        mensaje: Mensaje de la notificación
        tipo: Tipo de notificación ('info', 'warning', 'success', 'error', 'ticket')
        usuarios: Lista de usuarios destinatarios (opcional)
        grupos: Lista de grupos destinatarios (opcional)
        ticket: Ticket relacionado (opcional)
        creado_por: Usuario que crea la notificación (opcional)
        url_accion: URL de acción (opcional)
    """
    try:
        # Crear la notificación
        notificacion = Notificacion.objects.create(
            titulo=titulo,
            mensaje=mensaje,
            tipo=tipo,
            ticket=ticket,
            creado_por=creado_por,
            url_accion=url_accion or ''
        )
        
        # Enviar a usuarios específicos
        if usuarios:
            for usuario in usuarios:
                notificacion.enviar_a_usuario(usuario)
        
        # Enviar a grupos
        if grupos:
            for grupo in grupos:
                notif_grupo = notificacion.enviar_a_grupo(grupo)
                notif_grupo.crear_notificaciones_individuales()
                
        return notificacion
        
    except Exception as e:
        print(f"Error creando notificación personalizada: {e}")
        return None


def obtener_notificaciones_usuario(usuario, limite=10):
    """
    Obtiene las notificaciones más recientes de un usuario.
    
    Args:
        usuario: Usuario del que obtener notificaciones
        limite: Número máximo de notificaciones a obtener
        
    Returns:
        QuerySet: Notificaciones del usuario ordenadas por fecha
    """
    from notificaciones.models import NotificacionUsuario
    
    return NotificacionUsuario.objects.filter(
        usuario=usuario,
        notificacion__is_active=True
    ).select_related(
        'notificacion', 
        'notificacion__ticket',
        'notificacion__creado_por'
    ).order_by('-fecha_envio')[:limite]


def obtener_notificaciones_grupo_usuario(usuario, limite=10):
    """
    Obtiene las notificaciones de grupo más recientes para un usuario.
    
    Args:
        usuario: Usuario del que obtener notificaciones
        limite: Número máximo de notificaciones a obtener
        
    Returns:
        QuerySet: Notificaciones de grupo del usuario ordenadas por fecha
    """
    from notificaciones.models import NotificacionGrupo
    
    grupos_usuario = usuario.groups.all()
    
    return NotificacionGrupo.objects.filter(
        grupo__in=grupos_usuario,
        notificacion__is_active=True
    ).select_related(
        'notificacion',
        'notificacion__ticket', 
        'notificacion__creado_por',
        'grupo'
    ).order_by('-fecha_envio')[:limite]
