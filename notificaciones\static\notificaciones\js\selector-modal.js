/**
 * selector-modal.js
 * JavaScript para modales de selección de usuarios y grupos
 * con funcionalidad de búsqueda y selección múltiple
 */

class SelectorModal {
    constructor(modalId, inputId, type = 'usuarios') {
        this.modalId = modalId;
        this.inputId = inputId;
        this.type = type;
        this.selectedItems = new Set();
        this.allItems = [];
        this.filteredItems = [];
        
        this.init();
    }

    init() {
        this.modal = document.getElementById(this.modalId);
        this.input = document.getElementById(this.inputId);
        this.searchInput = this.modal.querySelector('.search-input');
        this.itemsList = this.modal.querySelector('.items-list');
        this.selectedCount = this.modal.querySelector('.selected-count');
        this.confirmBtn = this.modal.querySelector('.confirm-selection');
        this.clearBtn = this.modal.querySelector('.clear-selection');

        this.setupEventListeners();
        this.loadItems();
    }

    setupEventListeners() {
        // Búsqueda en tiempo real
        this.searchInput.addEventListener('input', (e) => {
            this.filterItems(e.target.value);
        });

        // Confirmar selección
        this.confirmBtn.addEventListener('click', () => {
            this.confirmSelection();
        });

        // Limpiar selección
        this.clearBtn.addEventListener('click', () => {
            this.clearSelection();
        });

        // Seleccionar todos los filtrados
        const selectAllBtn = this.modal.querySelector('.select-all-filtered');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                this.selectAllFiltered();
            });
        }
    }

    async loadItems() {
        try {
            const endpoint = this.type === 'usuarios' ? 
                '/notificaciones/ajax/obtener-usuarios/' : 
                '/notificaciones/ajax/obtener-grupos/';
            
            const response = await fetch(endpoint);
            const data = await response.json();
            
            this.allItems = data.items || [];
            this.filteredItems = [...this.allItems];
            this.renderItems();
        } catch (error) {
            console.error('Error cargando items:', error);
            this.showError('Error al cargar los datos');
        }
    }

    filterItems(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        
        if (!term) {
            this.filteredItems = [...this.allItems];
        } else {
            this.filteredItems = this.allItems.filter(item => {
                const name = (item.name || item.full_name || '').toLowerCase();
                const username = (item.username || '').toLowerCase();
                const email = (item.email || '').toLowerCase();
                const cargo = (item.cargo || '').toLowerCase();
                
                return name.includes(term) || 
                       username.includes(term) || 
                       email.includes(term) || 
                       cargo.includes(term);
            });
        }
        
        this.renderItems();
    }

    renderItems() {
        if (this.filteredItems.length === 0) {
            this.itemsList.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <p>No se encontraron ${this.type}</p>
                </div>
            `;
            return;
        }

        const html = this.filteredItems.map(item => {
            const isSelected = this.selectedItems.has(item.id);
            const checkClass = isSelected ? 'fas fa-check-square text-primary' : 'far fa-square text-muted';
            
            if (this.type === 'usuarios') {
                return `
                    <div class="item-row ${isSelected ? 'selected' : ''}" data-id="${item.id}">
                        <div class="d-flex align-items-center p-3 border-bottom cursor-pointer">
                            <i class="${checkClass} me-3 fs-5"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold">${item.full_name || item.username}</div>
                                <small class="text-muted">
                                    ${item.username} • ${item.email || 'Sin email'}
                                    ${item.cargo ? ` • ${item.cargo}` : ''}
                                </small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">${item.groups_count || 0} grupos</small>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="item-row ${isSelected ? 'selected' : ''}" data-id="${item.id}">
                        <div class="d-flex align-items-center p-3 border-bottom cursor-pointer">
                            <i class="${checkClass} me-3 fs-5"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold">${item.name}</div>
                                <small class="text-muted">${item.members_count} miembros activos</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-${item.members_count > 0 ? 'success' : 'secondary'}">
                                    ${item.members_count}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }
        }).join('');

        this.itemsList.innerHTML = html;

        // Agregar event listeners a los items
        this.itemsList.querySelectorAll('.item-row').forEach(row => {
            row.addEventListener('click', () => {
                const id = parseInt(row.dataset.id);
                this.toggleItem(id);
            });
        });

        this.updateSelectedCount();
    }

    toggleItem(id) {
        if (this.selectedItems.has(id)) {
            this.selectedItems.delete(id);
        } else {
            this.selectedItems.add(id);
        }
        
        this.renderItems();
    }

    selectAllFiltered() {
        this.filteredItems.forEach(item => {
            this.selectedItems.add(item.id);
        });
        this.renderItems();
    }

    clearSelection() {
        this.selectedItems.clear();
        this.renderItems();
    }

    updateSelectedCount() {
        const count = this.selectedItems.size;
        this.selectedCount.textContent = count;
        
        this.confirmBtn.disabled = count === 0;
        this.clearBtn.disabled = count === 0;
    }

    confirmSelection() {
        if (this.selectedItems.size === 0) {
            this.showWarning('Debe seleccionar al menos un elemento');
            return;
        }

        // Actualizar el input hidden del formulario
        const selectedIds = Array.from(this.selectedItems);
        this.input.value = selectedIds.join(',');

        // También actualizar cualquier input con el mismo name que pueda haber sido creado por crispy forms
        const allInputs = document.querySelectorAll(`input[name="${this.inputId.replace('-', '_')}"]`);
        allInputs.forEach(input => {
            input.value = selectedIds.join(',');
        });

        // Actualizar la vista previa
        this.updatePreview();

        // Cerrar modal
        const modalInstance = bootstrap.Modal.getInstance(this.modal);
        modalInstance.hide();

        this.showSuccess(`${this.selectedItems.size} ${this.type} seleccionado(s)`);

        // Debug: mostrar en consola
        console.log(`${this.type} seleccionados:`, selectedIds);
        console.log('Input value:', this.input.value);
    }

    updatePreview() {
        const previewContainer = document.getElementById(`${this.type}-preview`);
        if (!previewContainer) return;

        const selectedIds = Array.from(this.selectedItems);
        const selectedItemsData = this.allItems.filter(item => selectedIds.includes(item.id));

        if (selectedItemsData.length === 0) {
            previewContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No hay ${this.type} seleccionado(s)
                </div>
            `;
            return;
        }

        const html = selectedItemsData.map(item => {
            if (this.type === 'usuarios') {
                return `
                    <div class="badge bg-primary me-2 mb-2 p-2">
                        <i class="fas fa-user me-1"></i>
                        ${item.full_name || item.username}
                        <button type="button" class="btn-close btn-close-white ms-2" 
                                onclick="removeSelectedItem(${item.id}, '${this.type}')"></button>
                    </div>
                `;
            } else {
                return `
                    <div class="badge bg-success me-2 mb-2 p-2">
                        <i class="fas fa-users me-1"></i>
                        ${item.name} (${item.members_count})
                        <button type="button" class="btn-close btn-close-white ms-2" 
                                onclick="removeSelectedItem(${item.id}, '${this.type}')"></button>
                    </div>
                `;
            }
        }).join('');

        previewContainer.innerHTML = `
            <div class="alert alert-success">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <strong><i class="fas fa-check-circle"></i> ${selectedItemsData.length} ${this.type} seleccionado(s):</strong>
                    <button type="button" class="btn btn-sm btn-outline-danger" 
                            onclick="clearAllSelected('${this.type}')">
                        <i class="fas fa-trash"></i> Limpiar todo
                    </button>
                </div>
                <div>${html}</div>
            </div>
        `;
    }

    showSuccess(message) {
        Swal.fire({
            icon: 'success',
            title: 'Éxito',
            text: message,
            timer: 2000,
            showConfirmButton: false
        });
    }

    showWarning(message) {
        Swal.fire({
            icon: 'warning',
            title: 'Atención',
            text: message
        });
    }

    showError(message) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message
        });
    }
}

// Funciones globales para manejar la eliminación de items seleccionados
window.removeSelectedItem = function(id, type) {
    const selectorInstance = window[`${type}Selector`];
    if (selectorInstance) {
        selectorInstance.selectedItems.delete(id);
        selectorInstance.updatePreview();
        selectorInstance.input.value = Array.from(selectorInstance.selectedItems).join(',');
    }
};

window.clearAllSelected = function(type) {
    const selectorInstance = window[`${type}Selector`];
    if (selectorInstance) {
        selectorInstance.selectedItems.clear();
        selectorInstance.updatePreview();
        selectorInstance.input.value = '';
    }
};

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando selectores modales...');

    // Inicializar selector de usuarios si existe
    const usuariosModal = document.getElementById('usuariosModal');
    const usuariosInput = document.getElementById('usuarios-selected');

    if (usuariosModal && usuariosInput) {
        console.log('Inicializando selector de usuarios');
        window.usuariosSelector = new SelectorModal('usuariosModal', 'usuarios-selected', 'usuarios');
    } else {
        console.log('Modal o input de usuarios no encontrado:', {
            modal: !!usuariosModal,
            input: !!usuariosInput
        });
    }

    // Inicializar selector de grupos si existe
    const gruposModal = document.getElementById('gruposModal');
    const gruposInput = document.getElementById('grupos-selected');

    if (gruposModal && gruposInput) {
        console.log('Inicializando selector de grupos');
        window.gruposSelector = new SelectorModal('gruposModal', 'grupos-selected', 'grupos');
    } else {
        console.log('Modal o input de grupos no encontrado:', {
            modal: !!gruposModal,
            input: !!gruposInput
        });
    }
});
