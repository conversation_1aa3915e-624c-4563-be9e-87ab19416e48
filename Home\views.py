"""
Home/views.py
Vistas para el dashboard principal del sistema.

Usa el sistema centralizado de permisos para proporcionar información
relevante según el rol del usuario.
"""

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Count, Case, When, IntegerField
from django.utils import timezone
from datetime import timedelta

from tickets.models import Ticket, HistorialTicket
from asignaciones.models import AsignacionTicket
from notificaciones.models import NotificacionUsuario, NotificacionGrupo
from ciudadano.models import Ciudadano
from user.models import User
from asignaciones.utils import obtener_notificaciones_usuario, obtener_notificaciones_grupo_usuario

# Usar el sistema centralizado de permisos
from permissions.core import PermissionHelper
from permissions.utils import filter_tickets_for_user, get_user_dashboard_context


@login_required
def home_view(request):
    """
    Vista principal del dashboard que muestra información relevante según el rol del usuario.

    Redirige a funciones específicas según el tipo de usuario para mantener
    responsabilidad única y optimizar las consultas.

    Args:
        request: HttpRequest object con información del usuario autenticado

    Returns:
        HttpResponse: Render del template home.html con contexto específico del rol

    Security:
        - Requiere autenticación (@login_required)
        - Filtra información según permisos del usuario
    """
    user = request.user

    # Obtener contexto base del dashboard usando el sistema centralizado
    context = get_user_dashboard_context(user)

    # Determinar el rol del usuario y obtener contexto específico
    if PermissionHelper.is_admin(user):
        context.update(_get_dashboard_administrador(user))
    elif PermissionHelper.is_secretaria(user):
        context.update(_get_dashboard_secretaria(user))
    elif PermissionHelper.is_supervisor(user):
        context.update(_get_dashboard_supervisor(user))
    else:
        context.update(_get_dashboard_empleado(user))

    # Agregar información común (el sistema centralizado ya agrega permisos)
    context.update({
        'user': user,  # Para compatibilidad con template
        'usuario': user,
        'fecha_actual': timezone.now(),
        'rol_usuario': PermissionHelper.get_user_role(user),
        # Mantener compatibilidad con templates existentes
        'permisos': {
            'es_administrador': PermissionHelper.is_admin(user),
            'es_supervisor': PermissionHelper.is_supervisor(user),
            'es_secretaria': PermissionHelper.is_secretaria(user),
            'puede_gestionar_asignaciones': PermissionHelper.is_admin(user) or PermissionHelper.is_supervisor(user),
            'puede_ver_reportes': PermissionHelper.is_admin(user) or PermissionHelper.is_supervisor(user),
            'puede_gestionar_ciudadanos': PermissionHelper.is_admin(user) or PermissionHelper.is_secretaria(user)
        }
    })

    # Agregar notificaciones a todos los contextos
    context.update(_get_notificaciones_usuario(user))

    return render(request, 'home.html', context)


def _get_notificaciones_usuario(user):
    """
    Obtiene las notificaciones combinadas (individuales y de grupo) para un usuario.

    Args:
        user: Usuario del que obtener notificaciones

    Returns:
        dict: Contexto con notificaciones combinadas
    """
    # Obtener notificaciones individuales
    notificaciones_individuales = obtener_notificaciones_usuario(user, limite=15)

    # Obtener notificaciones de grupo
    notificaciones_grupo = obtener_notificaciones_grupo_usuario(user, limite=15)

    # Combinar y ordenar por fecha
    todas_notificaciones = []

    # Agregar notificaciones individuales
    for notif_usuario in notificaciones_individuales:
        todas_notificaciones.append({
            'tipo': 'individual',
            'notificacion': notif_usuario.notificacion,
            'fecha': notif_usuario.fecha_envio,
            'leida': notif_usuario.leida,
            'instancia': notif_usuario
        })

    # Agregar notificaciones de grupo
    for notif_grupo in notificaciones_grupo:
        todas_notificaciones.append({
            'tipo': 'grupo',
            'notificacion': notif_grupo.notificacion,
            'fecha': notif_grupo.fecha_envio,
            'grupo': notif_grupo.grupo,
            'instancia': notif_grupo
        })

    # Ordenar por fecha (más recientes primero)
    todas_notificaciones.sort(key=lambda x: x['fecha'], reverse=True)

    # Limitar a las 10 más recientes
    notificaciones_recientes = todas_notificaciones[:10]

    return {
        'notificaciones_recientes': notificaciones_recientes,
        'total_notificaciones': len(todas_notificaciones)
    }


# ============================================================================
# FUNCIONES AUXILIARES PARA DETERMINACIÓN DE ROLES
# ============================================================================

# ============================================================================
# NOTA: Las funciones de determinación de roles han sido movidas al sistema
# centralizado en permissions/core.py para evitar duplicación y conflictos.
# Usar PermissionHelper.is_admin(), PermissionHelper.is_supervisor(), etc.
# ============================================================================


# ============================================================================
# FUNCIONES DE DASHBOARD POR ROL
# ============================================================================

def _get_dashboard_empleado(user):
    """
    Obtiene el contexto del dashboard para empleados.

    Incluye:
    - Tickets asignados al empleado
    - Notificaciones no leídas
    - Estadísticas personales

    Args:
        user: Instancia del usuario empleado

    Returns:
        dict: Contexto con información relevante para el empleado
    """
    # Obtener asignaciones activas del empleado
    asignaciones_activas = AsignacionTicket.objects.filter(
        usuario=user,
        is_active=True
    ).select_related('ticket', 'ticket__grupo').order_by('-fecha_asignacion')[:5]

    # Contar tickets por estado
    tickets_stats = AsignacionTicket.objects.filter(
        usuario=user,
        is_active=True
    ).aggregate(
        total=Count('id'),
        asignados=Count(Case(When(estado=1, then=1), output_field=IntegerField())),
        en_progreso=Count(Case(When(estado=2, then=1), output_field=IntegerField())),
        finalizados=Count(Case(When(estado=3, then=1), output_field=IntegerField()))
    )

    # Obtener notificaciones no leídas
    notificaciones_no_leidas = NotificacionUsuario.objects.filter(
        usuario=user,
        leida=False
    ).select_related('notificacion').order_by('-fecha_envio')[:5]

    # Calcular tiempo promedio de resolución (últimos 30 días)
    fecha_limite = timezone.now() - timedelta(days=30)
    asignaciones_finalizadas = AsignacionTicket.objects.filter(
        usuario=user,
        estado=3,  # Finalizado
        fecha_finalizacion__gte=fecha_limite
    )

    tiempo_promedio = None
    if asignaciones_finalizadas.exists():
        tiempos = [a.get_tiempo_trabajado() for a in asignaciones_finalizadas if a.get_tiempo_trabajado()]
        if tiempos:
            tiempo_promedio = sum(tiempos, timedelta()) / len(tiempos)

    return {
        'asignaciones_activas': asignaciones_activas,
        'tickets_stats': tickets_stats,
        'notificaciones_no_leidas': notificaciones_no_leidas,
        'total_notificaciones_no_leidas': notificaciones_no_leidas.count(),
        'tiempo_promedio_resolucion': tiempo_promedio,
        'dashboard_type': 'empleado'
    }


def _get_dashboard_supervisor(user):
    """
    Obtiene el contexto del dashboard para supervisores.

    Incluye:
    - Tickets de su área por estado
    - Equipo de trabajo (usuarios de su área)
    - Tickets sin asignar
    - Rendimiento del área

    Args:
        user: Instancia del usuario supervisor

    Returns:
        dict: Contexto con información relevante para el supervisor
    """
    # Obtener el grupo/área del supervisor
    area_supervisor = user.groups.first()

    if not area_supervisor:
        return {'dashboard_type': 'supervisor', 'sin_area': True}

    # Tickets del área por estado
    tickets_area = Ticket.objects.filter(
        grupo=area_supervisor,
        is_active=True
    ).aggregate(
        total=Count('id'),
        abiertos=Count(Case(When(estado=1, then=1), output_field=IntegerField())),
        en_progreso=Count(Case(When(estado=2, then=1), output_field=IntegerField())),
        cerrados=Count(Case(When(estado=3, then=1), output_field=IntegerField())),
        pendientes=Count(Case(When(estado=4, then=1), output_field=IntegerField()))
    )

    # Tickets recientes del área
    tickets_recientes = Ticket.objects.filter(
        grupo=area_supervisor,
        is_active=True
    ).select_related('creado_por').order_by('-fecha_creacion')[:5]

    # Equipo de trabajo (usuarios del área)
    equipo_trabajo = User.objects.filter(
        groups=area_supervisor,
        is_active=True
    ).exclude(id=user.id).select_related('cargo')[:10]

    # Tickets sin asignar en el área
    tickets_sin_asignar = Ticket.objects.filter(
        grupo=area_supervisor,
        is_active=True,
        asignaciones__isnull=True
    ).count()

    # Asignaciones activas del equipo
    asignaciones_equipo = AsignacionTicket.objects.filter(
        ticket__grupo=area_supervisor,
        is_active=True
    ).select_related('usuario', 'ticket').order_by('-fecha_asignacion')[:5]

    # Notificaciones del supervisor
    notificaciones_no_leidas = NotificacionUsuario.objects.filter(
        usuario=user,
        leida=False
    ).select_related('notificacion').order_by('-fecha_envio')[:5]

    return {
        'area_supervisor': area_supervisor,
        'tickets_area': tickets_area,
        'tickets_recientes': tickets_recientes,
        'equipo_trabajo': equipo_trabajo,
        'tickets_sin_asignar': tickets_sin_asignar,
        'asignaciones_equipo': asignaciones_equipo,
        'notificaciones_no_leidas': notificaciones_no_leidas,
        'total_notificaciones_no_leidas': notificaciones_no_leidas.count(),
        'dashboard_type': 'supervisor'
    }


def _get_dashboard_secretaria(user):
    """
    Obtiene el contexto del dashboard para secretarias.

    Incluye:
    - Resumen global de tickets
    - Ciudadanos registrados recientemente
    - Tickets por área
    - Accesos rápidos para gestión

    Args:
        user: Instancia del usuario secretaria

    Returns:
        dict: Contexto con información relevante para la secretaria
    """
    # Resumen global de tickets
    tickets_globales = Ticket.objects.filter(is_active=True).aggregate(
        total=Count('id'),
        abiertos=Count(Case(When(estado=1, then=1), output_field=IntegerField())),
        en_progreso=Count(Case(When(estado=2, then=1), output_field=IntegerField())),
        cerrados=Count(Case(When(estado=3, then=1), output_field=IntegerField())),
        pendientes=Count(Case(When(estado=4, then=1), output_field=IntegerField())),
        criticos=Count(Case(When(prioridad='critica', then=1), output_field=IntegerField())),
        altos=Count(Case(When(prioridad='alta', then=1), output_field=IntegerField()))
    )

    # Tickets recientes del sistema
    tickets_recientes = Ticket.objects.filter(
        is_active=True
    ).select_related('creado_por', 'grupo').order_by('-fecha_creacion')[:8]

    # Ciudadanos registrados recientemente
    ciudadanos_recientes = Ciudadano.objects.filter(
        is_active=True
    ).order_by('-fecha_registro')[:5]

    # Tickets por área (top 5 áreas con más tickets)
    from django.contrib.auth.models import Group
    tickets_por_area = Group.objects.annotate(
        total_tickets=Count('tickets_asignados', filter=Q(tickets_asignados__is_active=True)),
        tickets_abiertos=Count('tickets_asignados', filter=Q(
            tickets_asignados__is_active=True,
            tickets_asignados__estado=1
        )),
        tickets_criticos=Count('tickets_asignados', filter=Q(
            tickets_asignados__is_active=True,
            tickets_asignados__prioridad='critica'
        ))
    ).filter(total_tickets__gt=0).order_by('-total_tickets')[:5]

    # Notificaciones importantes
    notificaciones_no_leidas = NotificacionUsuario.objects.filter(
        usuario=user,
        leida=False
    ).select_related('notificacion').order_by('-fecha_envio')[:5]

    # Estadísticas de ciudadanos
    ciudadanos_stats = Ciudadano.objects.filter(is_active=True).aggregate(
        total=Count('id'),
        con_tickets=Count('id', filter=Q(tickets__isnull=False)),
        registrados_hoy=Count('id', filter=Q(
            fecha_registro__date=timezone.now().date()
        ))
    )

    return {
        'tickets_globales': tickets_globales,
        'tickets_recientes': tickets_recientes,
        'ciudadanos_recientes': ciudadanos_recientes,
        'tickets_por_area': tickets_por_area,
        'notificaciones_no_leidas': notificaciones_no_leidas,
        'total_notificaciones_no_leidas': notificaciones_no_leidas.count(),
        'ciudadanos_stats': ciudadanos_stats,
        'dashboard_type': 'secretaria'
    }


def _get_dashboard_administrador(user):
    """
    Obtiene el contexto del dashboard para administradores.

    Incluye:
    - Métricas generales del sistema
    - Usuarios activos/inactivos
    - Tickets críticos
    - Estadísticas por área
    - Rendimiento general

    Args:
        user: Instancia del usuario administrador

    Returns:
        dict: Contexto con información completa del sistema para el administrador
    """
    # Métricas generales del sistema
    sistema_stats = {
        'tickets': Ticket.objects.filter(is_active=True).aggregate(
            total=Count('id'),
            abiertos=Count(Case(When(estado=1, then=1), output_field=IntegerField())),
            en_progreso=Count(Case(When(estado=2, then=1), output_field=IntegerField())),
            cerrados=Count(Case(When(estado=3, then=1), output_field=IntegerField())),
            pendientes=Count(Case(When(estado=4, then=1), output_field=IntegerField())),
            criticos=Count(Case(When(prioridad='critica', then=1), output_field=IntegerField()))
        ),
        'usuarios': User.objects.aggregate(
            total=Count('id'),
            activos=Count(Case(When(is_active=True, then=1), output_field=IntegerField())),
            inactivos=Count(Case(When(is_active=False, then=1), output_field=IntegerField())),
            supervisores=Count(Case(When(is_supervisor=True, then=1), output_field=IntegerField()))
        ),
        'ciudadanos': Ciudadano.objects.filter(is_active=True).aggregate(
            total=Count('id'),
            con_tickets=Count('id', filter=Q(tickets__isnull=False))
        )
    }

    # Tickets críticos que requieren atención
    tickets_criticos = Ticket.objects.filter(
        is_active=True,
        prioridad='critica'
    ).select_related('creado_por', 'grupo').order_by('-fecha_creacion')[:5]

    # Rendimiento por área
    from django.contrib.auth.models import Group
    rendimiento_areas = Group.objects.annotate(
        total_tickets=Count('tickets_asignados', filter=Q(tickets_asignados__is_active=True)),
        tickets_cerrados=Count('tickets_asignados', filter=Q(
            tickets_asignados__is_active=True,
            tickets_asignados__estado=3
        )),
        tickets_pendientes=Count('tickets_asignados', filter=Q(
            tickets_asignados__is_active=True,
            tickets_asignados__estado__in=[1, 2, 4]
        )),
        usuarios_activos=Count('user', filter=Q(user__is_active=True))
    ).filter(total_tickets__gt=0).order_by('-total_tickets')

    # Actividad reciente del sistema
    actividad_reciente = HistorialTicket.objects.select_related(
        'ticket', 'usuario'
    ).order_by('-fecha')[:10]

    # Usuarios más activos (por tickets creados en últimos 30 días)
    fecha_limite = timezone.now() - timedelta(days=30)
    usuarios_activos = User.objects.filter(
        is_active=True,
        tickets_creados__fecha_creacion__gte=fecha_limite
    ).annotate(
        tickets_creados_mes=Count('tickets_creados')
    ).order_by('-tickets_creados_mes')[:5]

    # Notificaciones del administrador
    notificaciones_no_leidas = NotificacionUsuario.objects.filter(
        usuario=user,
        leida=False
    ).select_related('notificacion').order_by('-fecha_envio')[:5]

    return {
        'sistema_stats': sistema_stats,
        'tickets_criticos': tickets_criticos,
        'rendimiento_areas': rendimiento_areas,
        'actividad_reciente': actividad_reciente,
        'usuarios_activos': usuarios_activos,
        'notificaciones_no_leidas': notificaciones_no_leidas,
        'total_notificaciones_no_leidas': notificaciones_no_leidas.count(),
        'dashboard_type': 'administrador'
    }