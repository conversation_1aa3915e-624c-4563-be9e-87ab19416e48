{% extends 'public_tickets/base_public.html' %}

{% block title %}Ticket #{{ ticket.id }} - {{ ticket.titulo }}{% endblock %}

{% block content %}
<div class="ticket-card">
    <!-- Header -->
    <div class="header-section">
        <div class="municipality-logo">
            <i class="fas fa-building"></i>
        </div>
        <h2>Municipalidad de Estanzuela</h2>
        <p class="mb-0">"Un gobierno de puertas Abiertas"</p>
    </div>
    
    <!-- Contenido principal -->
    <div class="content-section">
        <!-- Información del ticket -->
        <div class="text-center mb-4">
            <h3 class="mb-3">
                <i class="fas fa-ticket-alt me-2"></i>
                Ticket #{{ ticket.id }}
            </h3>
            <div class="mb-3">
                <span class="badge status-badge bg-{{ estado_color }}">
                    {{ estado_display }}
                </span>
                <span class="badge priority-badge bg-{{ prioridad_color }} ms-2">
                    {{ prioridad_display|title }}
                </span>
            </div>
        </div>
        
        <!-- Título del ticket -->
        <div class="info-item">
            <div class="info-label">
                <i class="fas fa-heading me-2"></i>Título
            </div>
            <div class="info-value">
                <h5>{{ ticket.titulo }}</h5>
            </div>
        </div>
        
        <!-- Descripción -->
        <div class="info-item">
            <div class="info-label">
                <i class="fas fa-align-left me-2"></i>Descripción
            </div>
            <div class="info-value">
                {{ ticket.descripcion|linebreaks }}
            </div>
        </div>
        
        <!-- Área responsable -->
        <div class="info-item">
            <div class="info-label">
                <i class="fas fa-building me-2"></i>Área Responsable
            </div>
            <div class="info-value">
                {{ ticket.grupo.name }}
            </div>
        </div>
        
        <!-- Fecha de solicitud -->
        <div class="info-item">
            <div class="info-label">
                <i class="fas fa-calendar me-2"></i>Fecha de Solicitud
            </div>
            <div class="info-value">
                {{ ticket.fecha_creacion|date:"d/m/Y H:i" }}
            </div>
        </div>
        
        <!-- Ciudadano solicitante -->
        {% if ciudadano %}
        <div class="info-item">
            <div class="info-label">
                <i class="fas fa-user me-2"></i>Solicitado por
            </div>
            <div class="info-value">
                <strong>{{ ciudadano.nombre_completo }}</strong>
            </div>
        </div>
        {% endif %}
        
        <!-- Imágenes -->
        {% if imagenes %}
        <div class="image-gallery">
            <div class="info-label mb-3">
                <i class="fas fa-images me-2"></i>Imágenes del Ticket
            </div>
            <div class="row g-3">
                {% for imagen in imagenes %}
                <div class="col-md-6">
                    <div class="image-item">
                        <img src="{{ imagen.imagen.url }}" 
                             alt="{{ imagen.descripcion|default:'Imagen del ticket' }}"
                             data-bs-toggle="modal" 
                             data-bs-target="#modalImagen{{ imagen.id }}">
                        {% if imagen.descripcion %}
                        <div class="p-2 bg-light">
                            <small class="text-muted">{{ imagen.descripcion }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Modal para ver imagen completa -->
                <div class="modal fade" id="modalImagen{{ imagen.id }}" tabindex="-1">
                    <div class="modal-dialog modal-lg modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    {{ imagen.descripcion|default:'Imagen del ticket' }}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="{{ imagen.imagen.url }}" 
                                     class="img-fluid" 
                                     alt="{{ imagen.descripcion|default:'Imagen del ticket' }}">
                            </div>
                            <div class="modal-footer">
                                <a href="{{ imagen.imagen.url }}" 
                                   target="_blank" 
                                   class="btn btn-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>Ver en tamaño completo
                                </a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    Cerrar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Información adicional -->
        <div class="mt-4 p-3 bg-light rounded">
            <h6 class="mb-2">
                <i class="fas fa-info-circle me-2"></i>Información Importante
            </h6>
            <ul class="mb-0 small text-muted">
                <li>Este ticket fue creado el {{ ticket.fecha_creacion|date:"d/m/Y" }} a las {{ ticket.fecha_creacion|date:"H:i" }}</li>
                <li>El estado actual es: <strong>{{ estado_display }}</strong></li>
                <li>Para consultas adicionales, puede acercarse a las oficinas municipales</li>
                <li>Conserve este enlace para futuras consultas</li>
            </ul>
        </div>
    </div>
    
    <!-- Footer -->
    <div class="footer-section">
        <h6>Municipalidad de Estanzuela</h6>
        <p>"Un gobierno de puertas Abiertas"</p>
    </div>
</div>
{% endblock %}
