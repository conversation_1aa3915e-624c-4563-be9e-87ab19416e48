{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Gestionar Teléfonos - {{ usuario.get_full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con información del usuario -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">{{ usuario.get_full_name }}</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ usuario.username }} • 
                                    <i class="fas fa-briefcase me-1"></i>{{ usuario.cargo.nombre }}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarTelefono">
                                <i class="fas fa-plus me-2"></i>Agregar Teléfono
                            </button>
                            <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-left me-2"></i>Volver
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de teléfonos -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>Teléfonos Registrados
                        <span class="badge bg-primary ms-2" id="contador-telefonos">{{ telefonos.count }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="lista-telefonos">
                        {% if telefonos %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th><i class="fas fa-phone me-1"></i>Número</th>
                                            <th><i class="fas fa-tag me-1"></i>Tipo</th>
                                            <th width="120"><i class="fas fa-cogs me-1"></i>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tbody-telefonos">
                                        {% for telefono in telefonos %}
                                        <tr id="telefono-{{ telefono.id }}">
                                            <td>
                                                <strong class="text-primary">{{ telefono.numero }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ telefono.get_tipo_display }}</span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="eliminarTelefono({{ telefono.id }}, '{{ telefono.numero }}')"
                                                        title="Eliminar teléfono">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5" id="estado-vacio">
                                <i class="fas fa-phone-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No hay teléfonos registrados</h5>
                                <p class="text-muted">Agrega el primer teléfono para este usuario</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarTelefono">
                                    <i class="fas fa-plus me-2"></i>Agregar Teléfono
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para agregar teléfono -->
<div class="modal fade" id="modalAgregarTelefono" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Agregar Teléfono
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="form-agregar-telefono">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="numero" class="form-label">Número de Teléfono <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="numero" name="numero" 
                               placeholder="12345678" maxlength="8" pattern="[0-9]{8}" required>
                        <div class="form-text">Ingresa 8 dígitos numéricos</div>
                    </div>
                    <div class="mb-3">
                        <label for="tipo" class="form-label">Tipo de Teléfono <span class="text-danger">*</span></label>
                        <select class="form-select" id="tipo" name="tipo" required>
                            <option value="">Selecciona un tipo</option>
                            {% for value, label in tipos_telefono %}
                                <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Guardar Teléfono
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-outline-danger:hover {
    transform: scale(1.05);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-agregar-telefono');
    const modal = new bootstrap.Modal(document.getElementById('modalAgregarTelefono'));
    
    // Manejar envío del formulario
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Mostrar loading
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Guardando...';
        
        fetch('', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Cerrar modal
                modal.hide();
                
                // Mostrar mensaje de éxito
                Swal.fire({
                    icon: 'success',
                    title: '¡Éxito!',
                    text: data.message,
                    timer: 2000,
                    showConfirmButton: false
                });
                
                // Agregar teléfono a la tabla
                agregarTelefonoATabla(data.telefono);
                
                // Limpiar formulario
                form.reset();
                
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor'
            });
        })
        .finally(() => {
            // Restaurar botón
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
    
    // Validación en tiempo real del número
    document.getElementById('numero').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Solo números
        if (value.length > 8) value = value.slice(0, 8);
        e.target.value = value;
    });
});

function agregarTelefonoATabla(telefono) {
    const tbody = document.getElementById('tbody-telefonos');
    const estadoVacio = document.getElementById('estado-vacio');
    const contador = document.getElementById('contador-telefonos');
    
    // Si no hay tabla, crearla
    if (estadoVacio) {
        document.getElementById('lista-telefonos').innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-phone me-1"></i>Número</th>
                            <th><i class="fas fa-tag me-1"></i>Tipo</th>
                            <th width="120"><i class="fas fa-cogs me-1"></i>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="tbody-telefonos">
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // Agregar fila
    const newRow = document.createElement('tr');
    newRow.id = `telefono-${telefono.id}`;
    newRow.innerHTML = `
        <td><strong class="text-primary">${telefono.numero}</strong></td>
        <td><span class="badge bg-info">${telefono.tipo}</span></td>
        <td>
            <button class="btn btn-sm btn-outline-danger" 
                    onclick="eliminarTelefono(${telefono.id}, '${telefono.numero}')"
                    title="Eliminar teléfono">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    document.getElementById('tbody-telefonos').appendChild(newRow);
    
    // Actualizar contador
    const currentCount = parseInt(contador.textContent) + 1;
    contador.textContent = currentCount;
}

function eliminarTelefono(id, numero) {
    Swal.fire({
        title: '¿Eliminar teléfono?',
        text: `Se eliminará el número ${numero}`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`{% url 'user:desactivar_telefono' usuario.id 0 %}`.replace('0', id), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remover fila
                    document.getElementById(`telefono-${id}`).remove();
                    
                    // Actualizar contador
                    const contador = document.getElementById('contador-telefonos');
                    const currentCount = parseInt(contador.textContent) - 1;
                    contador.textContent = currentCount;
                    
                    // Si no quedan teléfonos, mostrar estado vacío
                    if (currentCount === 0) {
                        document.getElementById('lista-telefonos').innerHTML = `
                            <div class="text-center py-5" id="estado-vacio">
                                <i class="fas fa-phone-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No hay teléfonos registrados</h5>
                                <p class="text-muted">Agrega el primer teléfono para este usuario</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarTelefono">
                                    <i class="fas fa-plus me-2"></i>Agregar Teléfono
                                </button>
                            </div>
                        `;
                    }
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'Eliminado',
                        text: 'Teléfono eliminado correctamente',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Error al eliminar teléfono'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}
</script>
{% endblock %}
