# Actualización de Permisos para Secretarias

## 📋 Resumen del Cambio

Se ha corregido un error en los permisos del sistema donde las **secretarias no podían crear notificaciones** a usuarios y grupos. Ahora las secretarias tienen los permisos correctos para:

- ✅ Crear notificaciones para usuarios específicos
- ✅ Crear notificaciones para grupos
- ✅ Ver y gestionar notificaciones

## 🔧 Cambios Realizados

### 1. Actualización del Sistema de Permisos
- **Archivo modificado**: `permissions/core.py`
- **Método actualizado**: `can_create_notifications()`
- **Cambio**: Agregado `PermissionHelper.is_secretaria(user)` a la condición

### 2. Actualización de Permisos de Grupo
- **Archivo modificado**: `Base/management/commands/setup_groups.py`
- **Cambio**: Agregados permisos de notificaciones al grupo Secretaria

### 3. Comando de Actualización Automática
- **Archivo creado**: `Base/management/commands/update_permissions.py`
- **Propósito**: Actualizar permisos automáticamente en cualquier entorno

## 🚀 Instrucciones para Producción

### Paso 1: Hacer Deploy del Código
```bash
# Subir los cambios a GitHub
git add .
git commit -m "Fix: Agregar permisos de notificaciones para secretarias"
git push origin main
```

### Paso 2: Ejecutar en Producción
```bash
# En el servidor de producción, después del deploy:
python manage.py update_permissions
```

### Paso 3: Verificar Funcionamiento
1. Iniciar sesión como secretaria
2. Verificar que aparezcan las opciones de notificaciones en:
   - **Sidebar**: Sección "Notificaciones" → "Notificar Usuarios" y "Notificar Grupos"
   - **Accesos Rápidos**: Dropdown "Crear Notificación"

## 🔍 Verificación Técnica

### Permisos Django Agregados para Secretarias:
- `add_notificacion`
- `view_notificacion`
- `change_notificacion`
- `add_notificacionusuario`
- `view_notificacionusuario`
- `add_notificaciongrupo`
- `view_notificaciongrupo`

### Método de Verificación:
```python
# En Django shell:
from django.contrib.auth import get_user_model
from permissions.core import PermissionHelper

User = get_user_model()
secretaria = User.objects.filter(groups__name='Secretaria').first()
print(PermissionHelper.can_create_notifications(secretaria))  # Debe ser True
```

## ⚠️ Notas Importantes

1. **El comando `update_permissions` es seguro** - Solo actualiza permisos, no elimina datos
2. **Se puede ejecutar múltiples veces** sin problemas
3. **Funciona en cualquier entorno** (desarrollo, staging, producción)
4. **No requiere migraciones de base de datos** - Solo actualiza permisos existentes

## 🎯 Resultado Esperado

Después de ejecutar el comando en producción:
- ✅ Las secretarias podrán crear notificaciones
- ✅ El sidebar mostrará las opciones correctas
- ✅ Los accesos rápidos funcionarán correctamente
- ✅ No se afectarán otros permisos existentes

## 📞 Soporte

Si hay algún problema durante la implementación en producción, verificar:
1. Que el comando se ejecutó sin errores
2. Que las secretarias están asignadas al grupo "Secretaria"
3. Que el servidor se reinició después de los cambios
