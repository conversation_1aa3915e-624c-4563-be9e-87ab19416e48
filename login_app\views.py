"""
login_app/views.py
Vistas para el sistema de autenticación con seguridad mejorada.

Incluye:
- Login con rate limiting y captcha
- Logout seguro
- Validaciones de seguridad adicionales
"""

from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.cache import never_cache
from django_ratelimit.decorators import ratelimit
try:
    from django_ratelimit.core import is_ratelimited
except ImportError:
    # Fallback para versiones más antiguas
    from django_ratelimit import is_ratelimited
from Base.captcha import SimpleMathCaptcha
import logging

logger = logging.getLogger(__name__)


@never_cache
@csrf_protect
@ratelimit(key='ip', rate='5/m', method='POST', block=False)
def login_view(request):
    """
    Vista de login con seguridad mejorada.

    Incluye:
    - Rate limiting por IP
    - Captcha matemático después de varios intentos fallidos
    - Logging de intentos de acceso
    - Validaciones de seguridad

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Página de login o redirección
    """
    # Verificar rate limiting
    was_limited = getattr(request, 'limited', False)
    if was_limited:
        messages.error(
            request,
            'Demasiados intentos de login. Espera unos minutos antes de intentar nuevamente.'
        )
        logger.warning(f"Rate limit excedido para login desde IP: {get_client_ip(request)}")

    # Verificar si necesita captcha (después de 3 intentos fallidos)
    failed_attempts = request.session.get('failed_login_attempts', 0)
    needs_captcha = failed_attempts >= 3

    if request.method == 'POST':
        username = request.POST.get('username', '').strip()
        password = request.POST.get('password', '')

        # Validaciones básicas
        if not username or not password:
            messages.error(request, 'Por favor, completa todos los campos.')
            return render(request, 'login_app/login.html', {'needs_captcha': needs_captcha})

        # Verificar captcha si es necesario
        if needs_captcha:
            captcha_answer = request.POST.get('captcha', '')
            captcha_token = request.POST.get('captcha_token', '')

            if not captcha_answer or not captcha_token:
                messages.error(request, 'Por favor, resuelve el captcha.')
                return render(request, 'login_app/login.html', {'needs_captcha': needs_captcha})

            if not SimpleMathCaptcha.verify_captcha(captcha_token, captcha_answer):
                messages.error(request, 'Captcha incorrecto. Intenta nuevamente.')
                return render(request, 'login_app/login.html', {'needs_captcha': needs_captcha})

        # Intentar autenticación
        user = authenticate(request, username=username, password=password)

        if user is not None:
            if user.is_active:
                login(request, user)

                # Limpiar intentos fallidos
                request.session.pop('failed_login_attempts', None)

                # Log exitoso
                logger.info(f"Login exitoso para usuario: {username} desde IP: {get_client_ip(request)}")

                # Redireccionar según el rol
                next_url = request.GET.get('next', 'home')
                return redirect(next_url)
            else:
                messages.error(request, 'Tu cuenta está desactivada. Contacta al administrador.')
                logger.warning(f"Intento de login con cuenta desactivada: {username}")
        else:
            # Incrementar intentos fallidos
            request.session['failed_login_attempts'] = failed_attempts + 1

            messages.error(request, 'Usuario o contraseña incorrectos.')
            logger.warning(f"Login fallido para usuario: {username} desde IP: {get_client_ip(request)}")

    # Generar captcha si es necesario (siempre generar uno nuevo)
    captcha_data = None
    if needs_captcha:
        question, answer, token = SimpleMathCaptcha.generate_captcha()
        captcha_data = {
            'question': question,
            'token': token
        }
        # Debug: verificar que se genera correctamente
        logger.info(f"Captcha generado: {question} = {answer}, token: {token}")

    context = {
        'needs_captcha': needs_captcha,
        'captcha_data': captcha_data,
        'was_limited': was_limited,
        'failed_attempts': failed_attempts
    }

    return render(request, 'login_app/login.html', context)


@never_cache
def logout_view(request):
    """
    Vista de logout seguro.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Redirección al login
    """
    if request.user.is_authenticated:
        username = request.user.username
        logout(request)
        logger.info(f"Logout exitoso para usuario: {username}")
        messages.success(request, 'Has cerrado sesión correctamente.')

    return redirect('login')


def get_client_ip(request):
    """
    Obtiene la IP real del cliente considerando proxies.

    Args:
        request: HttpRequest object

    Returns:
        str: Dirección IP del cliente
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip