<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página No Encontrada - Sistema de Tickets</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .error-icon {
            font-size: 6rem;
            color: var(--info-color);
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .error-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .error-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn-outline-secondary {
            border: 2px solid #6c757d;
            color: #6c757d;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-2px);
        }

        .suggestions {
            background: #f8f9fa;
            border-left: 4px solid var(--info-color);
            padding: 1rem;
            margin-top: 2rem;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }

        .suggestions h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .suggestions ul {
            margin: 0;
            padding-left: 1.2rem;
            color: #666;
        }

        .suggestions li {
            margin-bottom: 0.3rem;
        }

        .search-box {
            margin-top: 1.5rem;
            position: relative;
        }

        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 50px;
            padding: 12px 50px 12px 20px;
            width: 100%;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(26, 35, 126, 0.25);
            outline: none;
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: white;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-50%) scale(1.05);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        <h1 class="error-title">Página No Encontrada</h1>
        
        <p class="error-description">
            Lo sentimos, la página que estás buscando no existe o ha sido movida.
            Verifica la URL o utiliza los enlaces de navegación.
        </p>

        <div class="search-box">
            <input type="text" class="search-input" placeholder="¿Qué estás buscando?" id="searchInput">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <div class="d-flex gap-3 justify-content-center flex-wrap mt-3">
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver Atrás
            </a>
            <a href="/inicio/" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Ir al Dashboard
            </a>
        </div>

        <div class="suggestions">
            <h6><i class="fas fa-lightbulb me-2"></i>Páginas Populares</h6>
            <ul>
                <li><a href="/inicio/" class="text-decoration-none">Dashboard Principal</a></li>
                <li><a href="/tickets/" class="text-decoration-none">Lista de Tickets</a></li>
                <li><a href="/tickets/crear/" class="text-decoration-none">Crear Nuevo Ticket</a></li>
                <li><a href="/ciudadanos/" class="text-decoration-none">Gestión de Ciudadanos</a></li>
                <li><a href="/notificaciones/" class="text-decoration-none">Notificaciones</a></li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (searchTerm) {
                // Redirigir a una página de búsqueda o al dashboard con parámetros
                window.location.href = `/inicio/?search=${encodeURIComponent(searchTerm)}`;
            }
        }

        // Permitir búsqueda con Enter
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    </script>
</body>
</html>
