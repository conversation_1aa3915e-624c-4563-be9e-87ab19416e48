{"Base": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)", "admin.cpython-312.pyc": "(binary)", "apps.cpython-312.pyc": "(binary)", "models.cpython-312.pyc": "(binary)"}, "admin.py": "from django.contrib import admin\r\n\r\n# Register your models here.\r\n", "apps.py": "from django.apps import AppConfig\r\n\r\n\r\nclass BaseConfig(AppConfig):\r\n    default_auto_field = \"django.db.models.BigAutoField\"\r\n    name = \"Base\"\r\n", "migrations": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)"}}, "models.py": "from django.db import models\r\n\r\n# Create your models here.\r\n", "static": {"img": {"logo.png": "(binary)"}}, "Templates": {"Base": {"base.html": "{% load static %}\r\n<!DOCTYPE html>\r\n<html lang=\"es\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>{% block title %}Mi Proyecto{% endblock %}</title>\r\n    <!-- Bootstrap CSS -->\r\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\r\n    <!-- Font Awesome (para íconos) -->\r\n    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css\" rel=\"stylesheet\">\r\n</head>\r\n<body class=\"bg-light\">\r\n    <!-- Contenedor principal -->\r\n    <div class=\"d-flex\" id=\"wrapper\">\r\n        <!-- Sidebar -->\r\n        <div class=\"bg-dark text-white vh-100\" id=\"sidebar-wrapper\" style=\"width: 250px;\">\r\n            <div class=\"sidebar-heading p-3\">\r\n                <h3 class=\"mb-0\">Gestor de tickets</h3>\r\n                \r\n            </div>\r\n            <div class=\"list-group list-group-flush\">\r\n                <a href=\"#\" class=\"list-group-item list-group-item-action bg-dark text-white\">\r\n                    <i class=\"fas fa-home me-2\"></i> Inicio\r\n                </a>\r\n                <a href=\"#\" class=\"list-group-item list-group-item-action bg-dark text-white\">\r\n                    <i class=\"fas fa-user me-2\"></i> Perfil\r\n                </a>\r\n                <a href=\"#\" class=\"list-group-item list-group-item-action bg-dark text-white\">\r\n                    <i class=\"fas fa-cog me-2\"></i> Configuración\r\n                </a>\r\n                <a href=\"{% url 'logout' %}\" class=\"list-group-item list-group-item-action bg-dark text-white\">\r\n                    <i class=\"fas fa-sign-out-alt me-2\"></i> Cerrar Sesión\r\n                </a>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Contenido principal -->\r\n        <div id=\"page-content-wrapper\" class=\"flex-grow-1\">\r\n            <!-- Barra superior -->\r\n            <nav class=\"navbar navbar-expand-lg navbar-light bg-white border-bottom\">\r\n                <div class=\"container-fluid\">\r\n                    <button class=\"btn btn-secondary\" id=\"sidebarToggle\">\r\n                        <i class=\"fas fa-bars\"></i>\r\n                    </button>\r\n                    <span class=\"navbar-brand ms-3\">\r\n                        Bienvenido, <strong>{{ user.username }}</strong>\r\n                    </span>\r\n                </div>\r\n            </nav>\r\n\r\n            <!-- Bloque de contenido -->\r\n            <div class=\"container-fluid p-4\">\r\n                {% block content %}\r\n                <!-- El contenido específico de cada página irá aquí -->\r\n                {% endblock %}\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Bootstrap JS y dependencias -->\r\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js\"></script>\r\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js\"></script>\r\n    <!-- jQuery (para interactividad) -->\r\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\r\n    <!-- Script para el sidebar -->\r\n    <script>\r\n        $(document).ready(function () {\r\n            $('#sidebarToggle').on('click', function () {\r\n                $('#sidebar-wrapper').toggleClass('d-none'); // Oculta o muestra el sidebar\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n"}}, "tests.py": "from django.test import TestCase\r\n\r\n# Create your tests here.\r\n", "views.py": "from django.shortcuts import render\r\n\r\n# Create your views here.\r\n"}, "Home": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)", "admin.cpython-312.pyc": "(binary)", "apps.cpython-312.pyc": "(binary)", "models.cpython-312.pyc": "(binary)", "urls.cpython-312.pyc": "(binary)", "views.cpython-312.pyc": "(binary)"}, "admin.py": "from django.contrib import admin\r\n\r\n# Register your models here.\r\n", "apps.py": "from django.apps import AppConfig\r\n\r\n\r\nclass HomeConfig(AppConfig):\r\n    default_auto_field = \"django.db.models.BigAutoField\"\r\n    name = \"Home\"\r\n", "migrations": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)"}}, "models.py": "from django.db import models\r\n\r\n# Create your models here.\r\n", "templates": {"home.html": "{% extends 'base/base.html' %}\r\n\r\n{% block title %}Inicio{% endblock %}\r\n\r\n{% block content %}\r\n<div class=\"row\">\r\n    <div class=\"col-12\">\r\n        <h1>Bienvenido a Mi Proyecto</h1>\r\n        <p>Este es un ejemplo de contenido que hereda del template base.</p>\r\n    </div>\r\n</div>\r\n{% endblock %}"}, "tests.py": "from django.test import TestCase\r\n\r\n# Create your tests here.\r\n", "urls.py": "from django.urls import path\r\nfrom . import views\r\n\r\nurlpatterns = [\r\n    path('', views.home_view, name='home'),\r\n]", "views.py": "from django.shortcuts import render\r\n\r\n\r\n\r\ndef home_view(request):\r\n    return render(request, 'home.html')"}, "login_app": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)", "admin.cpython-312.pyc": "(binary)", "apps.cpython-312.pyc": "(binary)", "models.cpython-312.pyc": "(binary)", "urls.cpython-312.pyc": "(binary)", "views.cpython-312.pyc": "(binary)"}, "admin.py": "from django.contrib import admin\r\n\r\n# Register your models here.\r\n", "apps.py": "from django.apps import AppConfig\r\n\r\n\r\nclass LoginAppConfig(AppConfig):\r\n    default_auto_field = \"django.db.models.BigAutoField\"\r\n    name = \"login_app\"\r\n", "migrations": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)"}}, "models.py": "from django.db import models\r\n\r\n# Create your models here.\r\n", "templates": {"login_app": {"login.html": "{% load static %}\r\n<!DOCTYPE html>\r\n<html lang=\"es\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Login</title>\r\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\r\n</head>\r\n<body>\r\n    <div class=\"container d-flex justify-content-center align-items-center vh-100\">\r\n        <div class=\"card\" style=\"width: 25rem;\">\r\n            <div class=\"card-body text-center\">\r\n                <img src=\"{% static 'img\\logo.png' %}\" alt=\"Logo\" class=\"mb-5\" style=\"width: 20em;\">\r\n                <h5 class=\"card-title\">Bienvenido</h5>\r\n                <p class=\"card-text\">Por favor, inicia sesión para continuar.</p>\r\n                <form method=\"post\">\r\n                    {% csrf_token %}\r\n                    <div class=\"mb-3\">\r\n                        <input type=\"text\" class=\"form-control\" name=\"username\" placeholder=\"Usuario\" required>\r\n                    </div>\r\n                    <div class=\"mb-3\">\r\n                        <input type=\"password\" class=\"form-control\" name=\"password\" placeholder=\"Contraseña\" required>\r\n                    </div>\r\n                    <button type=\"submit\" class=\"btn btn-primary w-100\">Iniciar Sesión</button>\r\n                </form>\r\n                {% if messages %}\r\n                    {% for message in messages %}\r\n                        <div class=\"alert alert-danger mt-3\">\r\n                            {{ message }}\r\n                        </div>\r\n                    {% endfor %}\r\n                {% endif %}\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>"}}, "tests.py": "from django.test import TestCase\r\n\r\n# Create your tests here.\r\n", "urls.py": "from django.urls import path\r\nfrom . import views\r\n\r\nurlpatterns = [\r\n    path('', views.login_view, name='login'),\r\n    path('logout/', views.logout_view, name='logout'),\r\n]", "views.py": "from django.shortcuts import render, redirect\r\nfrom django.contrib.auth import authenticate, login\r\nfrom django.contrib import messages\r\nfrom django.contrib.auth import logout\r\ndef login_view(request):\r\n    if request.method == 'POST':\r\n        username = request.POST['username']\r\n        password = request.POST['password']\r\n        user = authenticate(request, username=username, password=password)\r\n        if user is not None:\r\n            login(request, user)\r\n            return redirect('home')  # Redirige a la página de inicio después del login\r\n        else:\r\n            messages.error(request, 'Usuario o contraseña incorrectos.')\r\n    return render(request, 'login_app/login.html')\r\n\r\n\r\ndef logout_view(request):\r\n    logout(request)  # Cierra la sesión del usuario\r\n    return redirect('login')  # Redirige al usuario a la página de login"}, "tickets": {"__init__.py": "", "__pycache__": {"__init__.cpython-312.pyc": "(binary)", "settings.cpython-312.pyc": "(binary)", "urls.cpython-312.pyc": "(binary)", "wsgi.cpython-312.pyc": "(binary)"}, "asgi.py": "\"\"\"\r\nASGI config for tickets project.\r\n\r\nIt exposes the ASGI callable as a module-level variable named ``application``.\r\n\r\nFor more information on this file, see\r\nhttps://docs.djangoproject.com/en/5.0/howto/deployment/asgi/\r\n\"\"\"\r\n\r\nimport os\r\n\r\nfrom django.core.asgi import get_asgi_application\r\n\r\nos.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"tickets.settings\")\r\n\r\napplication = get_asgi_application()\r\n", "settings.py": "\"\"\"\r\nDjango settings for tickets project.\r\n\r\nGenerated by 'django-admin startproject' using Django 5.0.6.\r\n\r\nFor more information on this file, see\r\nhttps://docs.djangoproject.com/en/5.0/topics/settings/\r\n\r\nFor the full list of settings and their values, see\r\nhttps://docs.djangoproject.com/en/5.0/ref/settings/\r\n\"\"\"\r\n\r\nimport os\r\nfrom pathlib import Path\r\n\r\n# Build paths inside the project like this: BASE_DIR / 'subdir'.\r\nBASE_DIR = Path(__file__).resolve().parent.parent\r\n\r\n\r\n# Quick-start development settings - unsuitable for production\r\n# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/\r\n\r\n# SECURITY WARNING: keep the secret key used in production secret!\r\nSECRET_KEY = \"django-insecure-(7_$luw50_rao@2e%m(1_j(zu^)*panzee&iism4-qq+z4i@#q\"\r\n\r\n# SECURITY WARNING: don't run with debug turned on in production!\r\nDEBUG = True\r\n\r\nALLOWED_HOSTS = []\r\n\r\n\r\n# Application definition\r\n\r\nINSTALLED_APPS = [\r\n    \"django.contrib.admin\",\r\n    \"django.contrib.auth\",\r\n    \"django.contrib.contenttypes\",\r\n    \"django.contrib.sessions\",\r\n    \"django.contrib.messages\",\r\n    \"django.contrib.staticfiles\",\r\n    \"login_app\",\r\n    \"Base\",\r\n    \"Home\",\r\n]\r\n\r\nMIDDLEWARE = [\r\n    \"django.middleware.security.SecurityMiddleware\",\r\n    \"django.contrib.sessions.middleware.SessionMiddleware\",\r\n    \"django.middleware.common.CommonMiddleware\",\r\n    \"django.middleware.csrf.CsrfViewMiddleware\",\r\n    \"django.contrib.auth.middleware.AuthenticationMiddleware\",\r\n    \"django.contrib.messages.middleware.MessageMiddleware\",\r\n    \"django.middleware.clickjacking.XFrameOptionsMiddleware\",\r\n]\r\n\r\nROOT_URLCONF = \"tickets.urls\"\r\n\r\nTEMPLATES = [\r\n    {\r\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\r\n        \"DIRS\": [],\r\n        \"APP_DIRS\": True,\r\n        \"OPTIONS\": {\r\n            \"context_processors\": [\r\n                \"django.template.context_processors.debug\",\r\n                \"django.template.context_processors.request\",\r\n                \"django.contrib.auth.context_processors.auth\",\r\n                \"django.contrib.messages.context_processors.messages\",\r\n            ],\r\n        },\r\n    },\r\n]\r\n\r\nWSGI_APPLICATION = \"tickets.wsgi.application\"\r\n\r\n\r\n# Database\r\n# https://docs.djangoproject.com/en/5.0/ref/settings/#databases\r\n\r\nDATABASES = {\r\n    'default': {\r\n        'ENGINE': 'django.db.backends.mysql',\r\n        'NAME': 'controlticket',\r\n        'USER': 'root',\r\n        'PASSWORD': '',\r\n        'HOST': 'localhost',\r\n        'POST': '3306',\r\n        'OPTIONS': {\r\n            'sql_mode': 'traditional',\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n# Password validation\r\n# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators\r\n\r\nAUTH_PASSWORD_VALIDATORS = [\r\n    {\r\n        \"NAME\": \"django.contrib.auth.password_validation.UserAttributeSimilarityValidator\",\r\n    },\r\n    {\r\n        \"NAME\": \"django.contrib.auth.password_validation.MinimumLengthValidator\",\r\n    },\r\n    {\r\n        \"NAME\": \"django.contrib.auth.password_validation.CommonPasswordValidator\",\r\n    },\r\n    {\r\n        \"NAME\": \"django.contrib.auth.password_validation.NumericPasswordValidator\",\r\n    },\r\n]\r\n\r\n\r\n# Internationalization\r\n# https://docs.djangoproject.com/en/5.0/topics/i18n/\r\n\r\nLANGUAGE_CODE = 'es-gt'\r\n\r\nTIME_ZONE = 'America/Guatemala'\r\n\r\nUSE_I18N = True\r\n\r\nUSE_TZ = True\r\n\r\n\r\n# Static files (CSS, JavaScript, Images)\r\n# https://docs.djangoproject.com/en/5.0/howto/static-files/\r\n\r\nSTATIC_URL = \"static/\"\r\n# Directorios donde Django buscará archivos estáticos adicionales\r\nSTATICFILES_DIRS = [\r\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"\r\n]\r\n\r\n# Ruta donde se recopilarán los archivos estáticos durante el despliegue (no necesario en desarrollo)\r\nSTATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')\r\n# Default primary key field type\r\n# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field\r\n\r\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"\r\nLOGIN_URL = '/'\r\n#AUTH_USER_MODEL = 'login_app.User'", "urls.py": "\"\"\"\r\nURL configuration for tickets project.\r\n\r\nThe `urlpatterns` list routes URLs to views. For more information please see:\r\n    https://docs.djangoproject.com/en/5.0/topics/http/urls/\r\nExamples:\r\nFunction views\r\n    1. Add an import:  from my_app import views\r\n    2. Add a URL to urlpatterns:  path('', views.home, name='home')\r\nClass-based views\r\n    1. Add an import:  from other_app.views import Home\r\n    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')\r\nIncluding another URLconf\r\n    1. Import the include() function: from django.urls import include, path\r\n    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))\r\n\"\"\"\r\n\r\nfrom django.contrib import admin\r\nfrom django.urls import include, path\r\n\r\nurlpatterns = [\r\n    path(\"admin/\", admin.site.urls),\r\n    path('', include('login_app.urls')),\r\n    path('inicio/', include('Home.urls')),\r\n    \r\n]\r\n", "wsgi.py": "\"\"\"\r\nWSGI config for tickets project.\r\n\r\nIt exposes the WSGI callable as a module-level variable named ``application``.\r\n\r\nFor more information on this file, see\r\nhttps://docs.djangoproject.com/en/5.0/howto/deployment/wsgi/\r\n\"\"\"\r\n\r\nimport os\r\n\r\nfrom django.core.wsgi import get_wsgi_application\r\n\r\nos.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"tickets.settings\")\r\n\r\napplication = get_wsgi_application()\r\n"}}