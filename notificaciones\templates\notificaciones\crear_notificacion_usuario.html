{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}
{% load static %}
{% block title %}Crear Notificación para Usuarios{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-user-plus text-primary"></i>
                        Crear Notificación para Usuarios
                    </h2>
                    <p class="text-muted mb-0">Envíe notificaciones a usuarios específicos del sistema</p>
                </div>
                <div>
                    <a href="{% url 'notificaciones:lista_notificaciones' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Volver
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/inicio/">
                    <i class="fas fa-home"></i> Inicio
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'notificaciones:lista_notificaciones' %}">Notificaciones</a>
            </li>
            <li class="breadcrumb-item active">Crear para Usuarios</li>
        </ol>
    </nav>

    <!-- Formulario -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Formulario de Notificación
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <!-- Input hidden para almacenar IDs seleccionados -->
                        <input type="hidden" id="usuarios-selected" name="usuarios_selected" value="">
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Información
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Las notificaciones se envían inmediatamente
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Los usuarios recibirán una notificación en tiempo real
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Puede incluir una URL de acción opcional
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            Máximo 50 usuarios por envío
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Consideraciones
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning"></i>
                            Solo usuarios activos recibirán las notificaciones
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning"></i>
                            El mensaje debe tener entre 5 y 500 caracteres
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning"></i>
                            Verifique la lista de destinatarios antes de enviar
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-exclamation text-warning"></i>
                            Las notificaciones no se pueden editar una vez enviadas
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Tipos de notificación -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-palette"></i> Tipos de Notificación
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 text-center mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-info-circle fa-2x text-info mb-2"></i>
                                <h6 class="text-info">Información</h6>
                                <small class="text-muted">Mensajes informativos generales</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                <h6 class="text-warning">Advertencia</h6>
                                <small class="text-muted">Alertas y advertencias</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h6 class="text-success">Éxito</h6>
                                <small class="text-muted">Confirmaciones exitosas</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                <h6 class="text-danger">Error</h6>
                                <small class="text-muted">Errores y problemas</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center mb-3">
                            <div class="p-3 border rounded">
                                <i class="fas fa-ticket-alt fa-2x text-primary mb-2"></i>
                                <h6 class="text-primary">Ticket</h6>
                                <small class="text-muted">Relacionadas con tickets</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación del formulario
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Contador de caracteres para el mensaje
    const mensajeField = document.querySelector('textarea[name="mensaje"]');
    if (mensajeField) {
        const maxLength = 500;
        const counter = document.createElement('small');
        counter.className = 'form-text text-muted';
        counter.id = 'mensaje-counter';
        mensajeField.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - mensajeField.value.length;
            counter.textContent = `${mensajeField.value.length}/${maxLength} caracteres`;
            
            if (remaining < 50) {
                counter.className = 'form-text text-warning';
            } else if (remaining < 0) {
                counter.className = 'form-text text-danger';
            } else {
                counter.className = 'form-text text-muted';
            }
        }

        mensajeField.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Mostrar/ocultar usuarios según selección
    const usuariosCheckboxes = document.querySelectorAll('input[name="usuarios"]');
    const selectedCount = document.createElement('div');
    selectedCount.className = 'alert alert-info mt-2';
    selectedCount.style.display = 'none';
    
    if (usuariosCheckboxes.length > 0) {
        usuariosCheckboxes[0].closest('.form-group').appendChild(selectedCount);
        
        usuariosCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selected = document.querySelectorAll('input[name="usuarios"]:checked').length;
                
                if (selected > 0) {
                    selectedCount.style.display = 'block';
                    selectedCount.innerHTML = `<i class="fas fa-users"></i> ${selected} usuario(s) seleccionado(s)`;
                    
                    if (selected > 50) {
                        selectedCount.className = 'alert alert-danger mt-2';
                        selectedCount.innerHTML += ' <strong>(Máximo 50 usuarios)</strong>';
                    } else {
                        selectedCount.className = 'alert alert-info mt-2';
                    }
                } else {
                    selectedCount.style.display = 'none';
                }
            });
        });
    }
});
</script>

<!-- Modal para seleccionar usuarios -->
<div class="modal fade" id="usuariosModal" tabindex="-1" aria-labelledby="usuariosModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="usuariosModalLabel">
                    <i class="fas fa-users"></i> Seleccionar Usuarios
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Buscador -->
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control search-input" placeholder="Buscar por nombre, usuario, email o cargo...">
                    </div>
                </div>

                <!-- Controles -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary select-all-filtered">
                            <i class="fas fa-check-double"></i> Seleccionar todos los filtrados
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary clear-selection">
                            <i class="fas fa-times"></i> Limpiar selección
                        </button>
                    </div>
                    <div>
                        <span class="badge bg-primary">
                            <span class="selected-count">0</span> seleccionado(s)
                        </span>
                    </div>
                </div>

                <!-- Lista de usuarios -->
                <div class="items-list border rounded" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary confirm-selection" disabled>
                    <i class="fas fa-check"></i> Confirmar Selección
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'notificaciones/js/selector-modal.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación simple del formulario
    const form = document.querySelector('form[method="post"]');

    if (form) {
        form.addEventListener('submit', function(e) {
            const usuariosInput = document.getElementById('usuarios-selected');
            const usuariosValue = usuariosInput ? usuariosInput.value.trim() : '';

            console.log('Validando usuarios seleccionados:', usuariosValue);

            // Solo validar que haya usuarios seleccionados
            if (!usuariosValue) {
                e.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'Usuarios requeridos',
                    text: 'Debe seleccionar al menos un usuario antes de enviar la notificación.',
                    confirmButtonText: 'Entendido'
                });
                return false;
            }

            // Si hay usuarios, permitir el envío normal
            console.log('Usuarios válidos, enviando formulario...');
        });
    }
});
</script>
{% endblock %}
