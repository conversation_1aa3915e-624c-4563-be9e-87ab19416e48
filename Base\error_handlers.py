"""
Base/error_handlers.py
Manejadores personalizados de errores para el sistema.

Incluye logging de intentos de acceso no autorizados y 
respuestas personalizadas para diferentes tipos de error.
"""

import logging
from django.shortcuts import render
from django.http import HttpResponseForbidden, HttpResponseNotFound, HttpResponseServerError
from django.contrib.auth import logout
from django.utils import timezone

# Configurar logger específico para errores de seguridad
security_logger = logging.getLogger('Base.security')
error_logger = logging.getLogger('Base.errors')


def permission_denied_view(request, exception=None):
    """
    Vista personalizada para errores 403 - Acceso Denegado.
    
    Registra el intento de acceso no autorizado y puede desloguear
    al usuario si es necesario.
    
    Args:
        request: HttpRequest object
        exception: Excepción que causó el error (opcional)
        
    Returns:
        HttpResponse: Respuesta con template 403.html
    """
    # Obtener información del usuario y la request
    user_info = "Anónimo"
    if request.user.is_authenticated:
        user_info = f"{request.user.username} (ID: {request.user.id})"
    
    # Obtener IP del cliente
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    
    # Registrar el intento de acceso no autorizado
    security_logger.warning(
        f"Acceso denegado (403) - Usuario: {user_info}, "
        f"IP: {ip}, URL: {request.get_full_path()}, "
        f"Method: {request.method}, "
        f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}, "
        f"Timestamp: {timezone.now()}"
    )
    
    # Contexto adicional para el template
    context = {
        'request_path': request.get_full_path(),
        'timestamp': timezone.now(),
        'user_authenticated': request.user.is_authenticated,
    }
    
    # Si el usuario no está autenticado, redirigir al login después del error
    if not request.user.is_authenticated:
        context['redirect_to_login'] = True
    
    # Renderizar template personalizado
    response = render(request, '403.html', context)
    response.status_code = 403
    return response


def page_not_found_view(request, exception=None):
    """
    Vista personalizada para errores 404 - Página No Encontrada.
    
    Args:
        request: HttpRequest object
        exception: Excepción que causó el error (opcional)
        
    Returns:
        HttpResponse: Respuesta con template 404.html
    """
    # Log del error 404
    error_logger.info(
        f"Página no encontrada (404) - URL: {request.get_full_path()}, "
        f"Method: {request.method}, "
        f"Referer: {request.META.get('HTTP_REFERER', 'Direct access')}, "
        f"User: {request.user.username if request.user.is_authenticated else 'Anónimo'}"
    )
    
    context = {
        'request_path': request.get_full_path(),
        'timestamp': timezone.now(),
    }
    
    response = render(request, '404.html', context)
    response.status_code = 404
    return response


def server_error_view(request):
    """
    Vista personalizada para errores 500 - Error Interno del Servidor.
    
    Args:
        request: HttpRequest object
        
    Returns:
        HttpResponse: Respuesta con template 500.html
    """
    # Log del error 500
    error_logger.error(
        f"Error interno del servidor (500) - URL: {request.get_full_path()}, "
        f"Method: {request.method}, "
        f"User: {request.user.username if request.user.is_authenticated else 'Anónimo'}, "
        f"IP: {request.META.get('REMOTE_ADDR', 'Unknown')}"
    )
    
    context = {
        'request_path': request.get_full_path(),
        'timestamp': timezone.now(),
    }
    
    response = render(request, '500.html', context)
    response.status_code = 500
    return response


def bad_request_view(request, exception=None):
    """
    Vista personalizada para errores 400 - Bad Request.
    
    Args:
        request: HttpRequest object
        exception: Excepción que causó el error (opcional)
        
    Returns:
        HttpResponse: Respuesta con mensaje de error
    """
    error_logger.warning(
        f"Bad Request (400) - URL: {request.get_full_path()}, "
        f"Method: {request.method}, "
        f"User: {request.user.username if request.user.is_authenticated else 'Anónimo'}"
    )
    
    # Para 400, usar un template simple o redirigir
    context = {
        'error_message': 'Solicitud incorrecta. Verifica los datos enviados.',
        'request_path': request.get_full_path(),
    }
    
    response = render(request, '400.html', context)
    response.status_code = 400
    return response


class SecurityLoggerMixin:
    """
    Mixin para agregar logging de seguridad a vistas que lo requieran.
    """
    
    def log_security_event(self, request, event_type, details=None):
        """
        Registra un evento de seguridad.
        
        Args:
            request: HttpRequest object
            event_type: Tipo de evento (str)
            details: Detalles adicionales (dict, opcional)
        """
        user_info = "Anónimo"
        if request.user.is_authenticated:
            user_info = f"{request.user.username} (ID: {request.user.id})"
        
        ip = self.get_client_ip(request)
        
        log_message = (
            f"Evento de seguridad: {event_type} - "
            f"Usuario: {user_info}, IP: {ip}, "
            f"URL: {request.get_full_path()}"
        )
        
        if details:
            log_message += f", Detalles: {details}"
        
        security_logger.warning(log_message)
    
    def get_client_ip(self, request):
        """
        Obtiene la IP real del cliente.
        
        Args:
            request: HttpRequest object
            
        Returns:
            str: Dirección IP del cliente
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


def log_permission_denied(request, reason="Acceso denegado"):
    """
    Función helper para registrar intentos de acceso denegado.
    
    Args:
        request: HttpRequest object
        reason: Razón del acceso denegado (str)
    """
    user_info = "Anónimo"
    if request.user.is_authenticated:
        user_info = f"{request.user.username} (ID: {request.user.id})"
    
    # Obtener IP del cliente
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    
    security_logger.warning(
        f"ACCESO DENEGADO - {reason} - "
        f"Usuario: {user_info}, IP: {ip}, "
        f"URL: {request.get_full_path()}, "
        f"Method: {request.method}, "
        f"Timestamp: {timezone.now()}"
    )
