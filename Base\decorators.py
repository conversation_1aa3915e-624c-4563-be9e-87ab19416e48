"""
Base/decorators.py
Decoradores personalizados para control de permisos y acceso en el sistema.

Incluye decoradores para:
- Verificación de roles (Admin, Secretaria, Empleado)
- Control de acceso a tickets
- Validación de permisos específicos
- Rate limiting personalizado
"""

from functools import wraps
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from django_ratelimit.decorators import ratelimit
try:
    from django_ratelimit.core import is_ratelimited
except ImportError:
    # Fallback para versiones más antiguas
    from django_ratelimit import is_ratelimited

# Importar el nuevo sistema de permisos
from permissions.core import PermissionHelper
from Base.error_handlers import log_permission_denied

User = get_user_model()


def admin_required(view_func):
    """
    Decorador que requiere que el usuario sea Admin.

    Usa el nuevo sistema centralizado de permisos y registra
    intentos de acceso no autorizados.

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.is_admin(request.user):
            log_permission_denied(request, "Acceso denegado - Se requiere rol Admin")
            raise PermissionDenied("Se requiere rol de Administrador para acceder a esta página.")
        return view_func(request, *args, **kwargs)
    return wrapper


def secretaria_or_admin_required(view_func):
    """
    Decorador que requiere que el usuario sea Secretaria o Admin.

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not (PermissionHelper.is_admin(request.user) or PermissionHelper.is_secretaria(request.user)):
            log_permission_denied(request, "Acceso denegado - Se requiere rol Admin o Secretaria")
            raise PermissionDenied("Se requiere rol de Administrador o Secretaria para acceder a esta página.")
        return view_func(request, *args, **kwargs)
    return wrapper


def supervisor_or_admin_required(view_func):
    """
    Decorador que requiere que el usuario sea Supervisor o Admin.

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not (PermissionHelper.is_admin(request.user) or PermissionHelper.is_supervisor(request.user)):
            log_permission_denied(request, "Acceso denegado - Se requiere rol Admin o Supervisor")
            raise PermissionDenied("Se requiere rol de Administrador o Supervisor para acceder a esta página.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_create_tickets_required(view_func):
    """
    Decorador que requiere permisos para crear tickets.

    Pueden crear tickets: Admin, Secretaria

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.can_create_tickets(request.user):
            log_permission_denied(request, "Acceso denegado - Sin permisos para crear tickets")
            raise PermissionDenied("No tienes permisos para crear tickets.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_manage_users_required(view_func):
    """
    Decorador que requiere permisos para gestionar usuarios.

    Pueden gestionar usuarios: Solo Admin

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.can_manage_users(request.user):
            log_permission_denied(request, "Acceso denegado - Sin permisos para gestionar usuarios")
            raise PermissionDenied("No tienes permisos para gestionar usuarios.")
        return view_func(request, *args, **kwargs)
    return wrapper


def can_assign_tickets_required(view_func):
    """
    Decorador que requiere permisos para asignar tickets.

    Pueden asignar tickets: Admin, Secretaria, Supervisor

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        if not PermissionHelper.can_assign_tickets(request.user):
            log_permission_denied(request, "Acceso denegado - Sin permisos para asignar tickets")
            raise PermissionDenied("No tienes permisos para asignar tickets.")
        return view_func(request, *args, **kwargs)
    return wrapper


def ticket_access_required(view_func):
    """
    Decorador que verifica el acceso a un ticket específico.
    Requiere que la vista reciba ticket_id como parámetro.

    Usa el nuevo sistema de permisos y registra intentos de acceso no autorizados.

    Args:
        view_func: Función de vista a decorar

    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        from tickets.models import Ticket

        ticket_id = kwargs.get('ticket_id')
        if not ticket_id:
            raise ValueError("La vista debe recibir ticket_id como parámetro")

        ticket = get_object_or_404(Ticket, id=ticket_id, is_active=True)

        # Admin puede ver cualquier ticket
        if PermissionHelper.is_admin(request.user):
            return view_func(request, *args, **kwargs)

        # Secretaria puede ver tickets que creó
        if PermissionHelper.is_secretaria(request.user) and ticket.creado_por == request.user:
            return view_func(request, *args, **kwargs)

        # Supervisor puede ver tickets de su área
        if PermissionHelper.is_supervisor(request.user):
            user_areas = PermissionHelper.get_user_areas(request.user)
            if user_areas.filter(id=ticket.grupo.id).exists():
                return view_func(request, *args, **kwargs)

        # Empleado puede ver tickets asignados a él
        if ticket.asignaciones.filter(usuario=request.user, is_active=True).exists():
            return view_func(request, *args, **kwargs)

        # Si llegamos aquí, no tiene permisos
        log_permission_denied(
            request,
            f"Acceso denegado a ticket #{ticket.id} - Usuario sin permisos"
        )
        raise PermissionDenied(f"No tienes permisos para acceder al ticket #{ticket.id}.")

    return wrapper


def ticket_edit_required(view_func):
    """
    Decorador que verifica los permisos de edición de un ticket específico.
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    @login_required
    def wrapper(request, *args, **kwargs):
        from tickets.models import Ticket
        
        ticket_id = kwargs.get('ticket_id')
        if not ticket_id:
            raise ValueError("La vista debe recibir ticket_id como parámetro")
        
        ticket = get_object_or_404(Ticket, id=ticket_id, is_active=True)
        
        if not ticket.puede_ser_editado_por(request.user):
            messages.error(request, 'No tienes permisos para editar este ticket.')
            return redirect('tickets:detalle_ticket', ticket_id=ticket.id)
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def ajax_login_required(view_func):
    """
    Decorador para vistas AJAX que requieren autenticación.
    Retorna JSON en lugar de redireccionar.
    
    Args:
        view_func: Función de vista a decorar
        
    Returns:
        function: Vista decorada
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({
                'error': 'Autenticación requerida',
                'redirect': '/login/'
            }, status=401)
        return view_func(request, *args, **kwargs)
    return wrapper


def ajax_permission_required(permission):
    """
    Decorador para vistas AJAX que requieren un permiso específico.
    
    Args:
        permission: Permiso requerido (ej: 'tickets.add_ticket')
        
    Returns:
        function: Decorador
    """
    def decorator(view_func):
        @wraps(view_func)
        @ajax_login_required
        def wrapper(request, *args, **kwargs):
            if not request.user.has_perm(permission):
                return JsonResponse({
                    'error': 'Permisos insuficientes'
                }, status=403)
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def rate_limit_by_user(rate='10/m'):
    """
    Decorador de rate limiting por usuario.
    
    Args:
        rate: Límite de velocidad (ej: '10/m' = 10 por minuto)
        
    Returns:
        function: Decorador
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Aplicar rate limiting por usuario autenticado
            key = f'user:{request.user.id}' if request.user.is_authenticated else 'ip'
            
            if is_ratelimited(request, group='user_action', key=key, rate=rate, increment=True):
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': 'Demasiadas solicitudes. Intenta más tarde.'
                    }, status=429)
                else:
                    messages.error(request, 'Demasiadas solicitudes. Intenta más tarde.')
                    return redirect('home')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def group_required(*group_names):
    """
    Decorador que requiere que el usuario pertenezca a uno de los grupos especificados.
    
    Args:
        *group_names: Nombres de los grupos permitidos
        
    Returns:
        function: Decorador
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def wrapper(request, *args, **kwargs):
            user_groups = request.user.groups.values_list('name', flat=True)
            
            if not any(group in user_groups for group in group_names):
                messages.error(
                    request, 
                    f'Necesitas pertenecer a uno de estos grupos: {", ".join(group_names)}'
                )
                return redirect('home')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def template_by_role(admin_template=None, secretaria_template=None, empleado_template=None):
    """
    Decorador que selecciona el template según el rol del usuario.
    
    Args:
        admin_template: Template para Admin
        secretaria_template: Template para Secretaria
        empleado_template: Template para Empleado
        
    Returns:
        function: Decorador
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Ejecutar la vista original
            response = view_func(request, *args, **kwargs)
            
            # Si la respuesta no es un render, devolverla tal como está
            if not hasattr(response, 'template_name'):
                return response
            
            # Determinar template según rol
            user_groups = request.user.groups.values_list('name', flat=True)
            
            if 'Admin' in user_groups and admin_template:
                response.template_name = admin_template
            elif 'Secretaria' in user_groups and secretaria_template:
                response.template_name = secretaria_template
            elif empleado_template:
                response.template_name = empleado_template
            
            return response
        return wrapper
    return decorator
