# Sistema de Advertencias Progresivas de Seguridad

## Descripción General

El sistema implementa un mecanismo de advertencias progresivas para usuarios que intentan acceder a recursos no autorizados, cumpliendo con el requerimiento de no desloguear automáticamente, sino mostrar advertencias y solo desloguear después de múltiples intentos.

## Funcionamiento

### 1. Detección de Intentos No Autorizados

El sistema detecta automáticamente cuando un usuario intenta acceder a una página o recurso para el cual no tiene permisos mediante:

- **Middleware personalizado**: `UnauthorizedAccessMiddleware` intercepta excepciones `PermissionDenied`
- **Registro automático**: Cada intento se guarda en la base de datos con detalles completos
- **Análisis temporal**: Se consideran solo intentos dentro de una ventana de 30 minutos

### 2. Sistema de Advertencias Progresivas

#### Primer Intento (1/3)
- **Mensaje**: ⚠️ Advertencia: No tienes permisos para acceder a esta sección. Si continúas intentando acceder a recursos no autorizados, tu sesión será cerrada automáticamente por seguridad.
- **Tipo**: Warning (amarillo)
- **Acción**: Solo mostrar advertencia

#### Segundo Intento (2/3)
- **Mensaje**: 🚨 ADVERTENCIA FINAL: Este es tu segundo intento de acceso no autorizado. Un intento más y tu sesión será cerrada automáticamente por medidas de seguridad.
- **Tipo**: Error (rojo)
- **Acción**: Advertencia final más prominente

#### Tercer Intento (3/3)
- **Mensaje**: ⚠️ Has sido deslogueado automáticamente por múltiples intentos de acceso no autorizado. Por favor, contacta al administrador si necesitas acceso a recursos adicionales.
- **Tipo**: Error (rojo)
- **Acción**: **Deslogueo automático**

### 3. Ventana Temporal

- **Duración**: 30 minutos
- **Reset automático**: Los intentos se "olvidan" después de 30 minutos
- **Conteo independiente**: Cada usuario tiene su propio contador

## Componentes Técnicos

### 1. Modelo de Base de Datos

```python
class UnauthorizedAccessAttempt(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    ip_address = models.GenericIPAddressField()
    url_attempted = models.URLField(max_length=500)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    session_key = models.CharField(max_length=40, blank=True)
```

### 2. Middleware de Seguridad

```python
class UnauthorizedAccessMiddleware:
    def process_exception(self, request, exception):
        if isinstance(exception, PermissionDenied) and request.user.is_authenticated:
            return self._handle_permission_denied(request, exception)
        return None
```

### 3. Métodos de Análisis

- `get_recent_attempts_count()`: Cuenta intentos en ventana temporal
- `should_logout_user()`: Determina si debe desloguear al usuario
- Logging completo de todos los intentos para auditoría

## Interfaz de Administración

### 1. Panel de Estadísticas (`/base/security-stats/`)

**Solo para Administradores**

- **Estadísticas generales**: Total de intentos, últimas 24h, 7 días, 30 días
- **Usuarios en riesgo**: Lista de usuarios con múltiples intentos recientes
- **Top usuarios**: Usuarios con más intentos en los últimos 7 días
- **Top IPs**: Direcciones IP con más intentos
- **URLs más intentadas**: Recursos más atacados
- **Intentos recientes**: Log de las últimas 24 horas
- **Auto-refresh**: Actualización automática cada 30 segundos

### 2. Admin de Django

**Acceso**: `/admin/Base/unauthorizedaccessattempt/`

- **Vista completa**: Todos los intentos registrados
- **Filtros avanzados**: Por usuario, IP, fecha
- **Búsqueda**: Por usuario, IP, URL
- **Solo lectura**: No se pueden modificar registros
- **Optimizado**: Consultas eficientes con select_related

### 3. Sidebar de Administración

**Solo para Administradores**

- **Sección Seguridad** en el sidebar
- **Estadísticas de Seguridad**: Acceso directo al panel
- **Administrar Intentos**: Enlace al admin de Django

## Comandos de Mantenimiento

### 1. Limpiar Registros Antiguos

```bash
# Limpiar registros de más de 30 días
python manage.py cleanup_unauthorized_attempts --days 30

# Vista previa sin eliminar
python manage.py cleanup_unauthorized_attempts --days 30 --dry-run
```

### 2. Probar el Sistema

```bash
# Simular intentos para un usuario
python manage.py test_unauthorized_system --username empleado1 --attempts 3
```

## Características de Seguridad

### 1. Prevención de Bypass

- **Middleware obligatorio**: No se puede desactivar fácilmente
- **Excepciones Django nativas**: Usa `PermissionDenied` estándar
- **Logging completo**: Todos los intentos se registran
- **IP tracking**: Seguimiento de direcciones IP

### 2. Información Registrada

- **Usuario**: Quien intentó el acceso
- **IP**: Dirección IP del intento
- **URL**: Recurso que se intentó acceder
- **User-Agent**: Navegador/cliente utilizado
- **Timestamp**: Fecha y hora exacta
- **Session Key**: Identificador de sesión

### 3. Análisis y Monitoreo

- **Patrones de ataque**: Identificación de usuarios problemáticos
- **Análisis temporal**: Detección de ataques coordinados
- **Alertas visuales**: Usuarios en riesgo destacados
- **Estadísticas en tiempo real**: Monitoreo continuo

## Configuración

### 1. Settings.py

```python
MIDDLEWARE = [
    # ... otros middlewares ...
    'Base.middleware.UnauthorizedAccessMiddleware',  # Después de AuthenticationMiddleware
]
```

### 2. Parámetros Configurables

- **MAX_ATTEMPTS**: 3 intentos (configurable en el middleware)
- **TIME_WINDOW**: 30 minutos (configurable en el modelo)
- **AUTO_REFRESH**: 30 segundos (configurable en el template)

## Beneficios del Sistema

### 1. Seguridad Mejorada

- **Prevención de ataques**: Disuade intentos maliciosos
- **Detección temprana**: Identifica comportamientos sospechosos
- **Respuesta automática**: Protección sin intervención manual

### 2. Experiencia de Usuario

- **Advertencias claras**: El usuario sabe qué está pasando
- **Oportunidades de corrección**: No se desloguea inmediatamente
- **Mensajes informativos**: Explicación del sistema de seguridad

### 3. Administración

- **Monitoreo completo**: Visibilidad total de intentos
- **Análisis de patrones**: Identificación de problemas
- **Mantenimiento automatizado**: Limpieza de registros antiguos

## Casos de Uso

### 1. Usuario Accidental

Un empleado hace clic en un enlace incorrecto:
- **1er intento**: Ve advertencia, entiende el error
- **Resultado**: Navega correctamente, no hay más intentos

### 2. Usuario Persistente

Un usuario intenta acceder repetidamente a recursos restringidos:
- **1er intento**: Advertencia inicial
- **2do intento**: Advertencia final
- **3er intento**: Deslogueo automático
- **Resultado**: Protección del sistema, usuario debe contactar admin

### 3. Ataque Malicioso

Intento de acceso no autorizado sistemático:
- **Detección**: Todos los intentos se registran
- **Análisis**: Patrones visibles en estadísticas
- **Respuesta**: Deslogueo automático + evidencia para investigación

## Mantenimiento y Monitoreo

### 1. Tareas Regulares

- **Revisar estadísticas**: Semanalmente
- **Limpiar registros**: Mensualmente
- **Analizar patrones**: Según necesidad

### 2. Alertas a Monitorear

- **Usuarios con múltiples intentos**: Posible problema de permisos
- **IPs con muchos intentos**: Posible ataque
- **URLs muy intentadas**: Recursos que necesitan mejor protección

### 3. Optimización

- **Índices de base de datos**: En campos de búsqueda frecuente
- **Limpieza automática**: Programar con cron jobs
- **Monitoreo de rendimiento**: Verificar impacto del middleware

## Conclusión

Este sistema proporciona una solución robusta y user-friendly para manejar intentos de acceso no autorizado, cumpliendo con los requerimientos de:

1. **No deslogueo inmediato**: Sistema de advertencias progresivas
2. **Múltiples oportunidades**: 3 intentos antes del deslogueo
3. **Información clara**: Mensajes explicativos para el usuario
4. **Monitoreo completo**: Herramientas de administración y análisis
5. **Seguridad robusta**: Protección efectiva contra ataques

El sistema es configurable, mantenible y proporciona una excelente experiencia tanto para usuarios como para administradores.
