"""
ciudadano/models.py
Modelos para la gestión de ciudadanos y su información relacionada.

Incluye:
- Ciudadano: Información básica del ciudadano
- CiudadanoTicket: Relación entre ciudadano y tickets
"""

from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone


class Ciudadano(models.Model):
    """
    Modelo para almacenar información de ciudadanos que solicitan servicios.

    Incluye validaciones para DPI guatemalteco y campos optimizados
    con índices para búsquedas rápidas.
    """

    # Validador para DPI guatemalteco (formato: 1234567890123)
    dpi_validator = RegexValidator(
        regex=r'^\d{13}$',
        message='El DPI debe tener exactamente 13 dígitos numéricos.'
    )

    # Validador para teléfono guatemalteco
    telefono_validator = RegexValidator(
        regex=r'^[0-9+\-\s()]{8,15}$',
        message='Ingrese un número de teléfono válido.'
    )

    GENERO_CHOICES = (
        (1, 'Hombre'),
        (2, 'Mujer'),
    )

    # Campos principales
    dpi = models.CharField(
        max_length=20,
        unique=True,
        validators=[dpi_validator],
        help_text='DPI del ciudadano (13 dígitos)',
        db_index=True  # Índice para búsquedas rápidas
    )
    nombre_completo = models.CharField(
        max_length=200,
        help_text='Nombre completo del ciudadano',
        db_index=True  # Índice para búsquedas por nombre
    )
    direccion = models.TextField(
        blank=True,
        help_text='Dirección completa del ciudadano'
    )
    telefono = models.CharField(
        max_length=20,
        blank=True,
        validators=[telefono_validator],
        help_text='Número de teléfono de contacto'
    )
    email = models.EmailField(
        max_length=100,
        blank=True,
        help_text='Correo electrónico del ciudadano'
    )
    fecha_nacimiento = models.DateField(
        blank=True,
        null=True,
        help_text='Fecha de nacimiento del ciudadano'
    )
    genero = models.IntegerField(
        choices=GENERO_CHOICES,
        blank=True,
        null=True,
        help_text='Género del ciudadano'
    )

    # Campos de auditoría
    fecha_registro = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha de registro en el sistema'
    )
    fecha_actualizacion = models.DateTimeField(
        auto_now=True,
        help_text='Última fecha de actualización'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Indica si el ciudadano está activo en el sistema'
    )

    class Meta:
        db_table = 'ciudadano'
        verbose_name = 'Ciudadano'
        verbose_name_plural = 'Ciudadanos'
        ordering = ['nombre_completo']
        indexes = [
            models.Index(fields=['dpi'], name='idx_ciudadano_dpi'),
            models.Index(fields=['nombre_completo'], name='idx_ciudadano_nombre'),
            models.Index(fields=['telefono'], name='idx_ciudadano_telefono'),
            models.Index(fields=['is_active'], name='idx_ciudadano_activo'),
        ]

    def __str__(self):
        return f"{self.nombre_completo} ({self.dpi})"

    def get_total_tickets(self):
        """Retorna el total de tickets del ciudadano."""
        return self.tickets.filter(is_active=True).count()

    def get_tickets_activos(self):
        """Retorna los tickets activos del ciudadano."""
        return self.tickets.filter(
            is_active=True,
            ticket__is_active=True,
            ticket__estado__in=[1, 2, 4]  # Abierto, En Progreso, Pendiente
        )

    def get_edad(self):
        """Calcula y retorna la edad del ciudadano."""
        if not self.fecha_nacimiento:
            return None

        from datetime import date
        today = date.today()
        return today.year - self.fecha_nacimiento.year - (
            (today.month, today.day) < (self.fecha_nacimiento.month, self.fecha_nacimiento.day)
        )

    def get_edad(self):
        """
        Calcula la edad del ciudadano basada en su fecha de nacimiento.

        Returns:
            int: Edad en años o None si no hay fecha de nacimiento
        """
        if self.fecha_nacimiento:
            today = timezone.now().date()
            return today.year - self.fecha_nacimiento.year - (
                (today.month, today.day) < (self.fecha_nacimiento.month, self.fecha_nacimiento.day)
            )
        return None

    def get_tickets_activos(self):
        """
        Obtiene los tickets activos del ciudadano.

        Returns:
            QuerySet: Tickets activos del ciudadano
        """
        return self.tickets.filter(ticket__is_active=True)

    def get_total_tickets(self):
        """
        Obtiene el total de tickets del ciudadano.

        Returns:
            int: Número total de tickets
        """
        return self.tickets.count()


class CiudadanoTicket(models.Model):
    """
    Modelo intermedio para la relación entre ciudadanos y tickets.

    Permite trazabilidad y consulta directa de la relación
    ciudadano-ticket con información adicional.
    """

    ciudadano = models.ForeignKey(
        Ciudadano,
        on_delete=models.CASCADE,
        related_name='tickets',
        help_text='Ciudadano asociado al ticket'
    )
    ticket = models.ForeignKey(
        'tickets.Ticket',  # Referencia lazy para evitar imports circulares
        on_delete=models.CASCADE,
        related_name='ciudadanos',
        help_text='Ticket asociado al ciudadano'
    )
    fecha_solicitud = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de la solicitud del ticket'
    )

    # Campos adicionales para la relación
    observaciones = models.TextField(
        blank=True,
        help_text='Observaciones específicas de esta relación'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Indica si la relación está activa'
    )

    class Meta:
        db_table = 'ciudadano_ticket'
        verbose_name = 'Ciudadano-Ticket'
        verbose_name_plural = 'Ciudadanos-Tickets'
        unique_together = ['ciudadano', 'ticket']  # Un ciudadano por ticket
        ordering = ['-fecha_solicitud']
        indexes = [
            models.Index(fields=['ciudadano', 'ticket'], name='idx_ciudadano_ticket'),
            models.Index(fields=['fecha_solicitud'], name='idx_fecha_solicitud'),
            models.Index(fields=['is_active'], name='idx_ciudadano_ticket_activo'),
        ]

    def __str__(self):
        return f"{self.ciudadano.nombre_completo} - Ticket #{self.ticket.id}"

    def get_tiempo_transcurrido(self):
        """
        Calcula el tiempo transcurrido desde la solicitud.

        Returns:
            timedelta: Tiempo transcurrido desde la solicitud
        """
        return timezone.now() - self.fecha_solicitud
