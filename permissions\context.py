"""
permissions/context.py
Context processor centralizado para permisos.

Proporciona un único context processor que agrega toda la información
de permisos necesaria para los templates, eliminando duplicaciones.
"""

from .core import PermissionHelper


def add_permission_context(request):
    """
    Context processor centralizado para añadir información de permisos a todos los templates.
    
    Reemplaza y unifica todos los sistemas de permisos anteriores, proporcionando
    una interfaz consistente para todos los templates.
    
    Args:
        request: HttpRequest object
        
    Returns:
        dict: Contexto con información completa de permisos
    """
    if not request.user.is_authenticated:
        return {
            'user_permissions': {
                'is_admin': False,
                'is_secretaria': False,
                'is_empleado': False,
                'is_supervisor': False,
                'role': 'Sin autenticar',
                'areas': [],
                'can_create_tickets': False,
                'can_assign_tickets': False,
                'can_view_all_tickets': False,
                'can_change_ticket_status': False,
                'can_delete_tickets': False,
                'can_manage_users': False,
                'can_create_notifications': False,
                'can_send_mass_notifications': False,
                'can_manage_all_notifications': False,
            },
            'sidebar_permissions': {
                'show_dashboard': False,
                'show_tickets': False,
                'show_create_ticket': False,
                'show_all_tickets': False,
                'show_my_tickets': False,
                'show_citizens': False,
                'show_users': False,
                'show_assignments': False,
                'show_notifications': False,
                'show_reports': False,
            },
            'quick_access_permissions': {},
            # Compatibilidad con sistema anterior de Home
            'permisos': {
                'es_administrador': False,
                'es_supervisor': False,
                'es_secretaria': False,
                'puede_gestionar_asignaciones': False,
                'puede_ver_reportes': False,
                'puede_gestionar_ciudadanos': False,
            }
        }
    
    user = request.user
    user_areas = PermissionHelper.get_user_areas(user)
    
    # Obtener información de roles
    is_admin = PermissionHelper.is_admin(user)
    is_supervisor = PermissionHelper.is_supervisor(user)
    is_secretaria = PermissionHelper.is_secretaria(user)
    is_empleado = PermissionHelper.is_empleado(user)
    
    return {
        'user_permissions': {
            'is_admin': is_admin,
            'is_secretaria': is_secretaria,
            'is_empleado': is_empleado,
            'is_supervisor': is_supervisor,
            'role': PermissionHelper.get_user_role(user),
            'areas': list(user_areas.values_list('name', flat=True)),
            'can_create_tickets': PermissionHelper.can_create_tickets(user),
            'can_assign_tickets': PermissionHelper.can_assign_tickets(user),
            'can_view_all_tickets': PermissionHelper.can_view_all_tickets(user),
            'can_change_ticket_status': PermissionHelper.can_change_ticket_status(user),
            'can_delete_tickets': PermissionHelper.can_delete_tickets(user),
            'can_manage_users': PermissionHelper.can_manage_users(user),
            'can_create_notifications': PermissionHelper.can_create_notifications(user),
            'can_send_mass_notifications': PermissionHelper.can_send_mass_notifications(user),
            'can_manage_all_notifications': PermissionHelper.can_manage_all_notifications(user),
        },
        'sidebar_permissions': PermissionHelper.get_sidebar_permissions(user),
        'quick_access_permissions': PermissionHelper.get_quick_access_permissions(user),
        
        # ========================================================================
        # COMPATIBILIDAD CON SISTEMA ANTERIOR
        # ========================================================================
        # Mantener variables del sistema anterior para evitar romper templates existentes
        'permisos': {
            'es_administrador': is_admin,
            'es_supervisor': is_supervisor,
            'es_secretaria': is_secretaria,
            'puede_gestionar_asignaciones': is_admin or is_supervisor,
            'puede_ver_reportes': is_admin,  # Solo administradores
            'puede_gestionar_ciudadanos': is_admin or is_supervisor or is_secretaria,
        }
    }
