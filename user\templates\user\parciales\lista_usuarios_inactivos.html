{% for usuario in usuarios %}
    <tr id="usuario-{{ usuario.id }}">
        <td>
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <i class="fas fa-user-slash"></i>
                </div>
                <div>
                    <h6 class="mb-0">{{ usuario.get_full_name }}</h6>
                    <small class="text-muted">@{{ usuario.username }}</small>
                </div>
            </div>
        </td>
        <td>
            <span class="badge bg-secondary">{{ usuario.dpi }}</span>
        </td>
        <td>
            <span class="badge bg-info">{{ usuario.cargo.nombre }}</span>
        </td>
        <td>
            {% with telefonos=usuario.celulares.all %}
                {% if telefonos %}
                    {% for cel in telefonos|slice:":2" %}
                        <div class="telefono-item mb-1">
                            <span class="badge bg-warning fs-6">
                                <i class="fas fa-phone me-1"></i>{{ cel.numero }}
                            </span>
                        </div>
                    {% endfor %}
                    {% if telefonos.count > 2 %}
                        <div class="telefono-item">
                            <span class="badge bg-secondary fs-6">
                                <i class="fas fa-plus me-1"></i>{{ telefonos.count|add:"-2" }} más
                            </span>
                        </div>
                    {% endif %}
                {% else %}
                    <span class="text-muted">
                        <i class="fas fa-phone-slash me-1"></i>Sin teléfono
                    </span>
                {% endif %}
            {% endwith %}
        </td>
        <td>
            {% with familiares=usuario.familiares.all %}
                {% if familiares %}
                    <span class="badge bg-success">
                        <i class="fas fa-users me-1"></i>{{ familiares.count }}
                    </span>
                {% else %}
                    <span class="text-muted">
                        <i class="fas fa-user-slash me-1"></i>Sin familiares
                    </span>
                {% endif %}
            {% endwith %}
        </td>
        <td>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-success"
                        onclick="activarUsuario({{ usuario.id }}, '{{ usuario.get_full_name }}')"
                        title="Activar usuario">
                    <i class="fas fa-user-check"></i>
                </button>
                <a href="{% url 'user:detalle_usuario_inactivo' usuario.pk %}"
                   class="btn btn-sm btn-outline-info" title="Ver detalles">
                    <i class="fas fa-eye"></i>
                </a>
            </div>
        </td>
    </tr>
{% endfor %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.telefono-item {
    margin-bottom: 2px;
}

.fs-6 {
    font-size: 0.875rem !important;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-weight: 500;
}
</style>
