{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Reportes de Tickets{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Reportes de Tickets</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-calendar me-1"></i>{{ fecha_desde|date:"d/m/Y" }} - {{ fecha_hasta|date:"d/m/Y" }}
                                    <span class="ms-3">
                                        <i class="fas fa-ticket-alt me-1"></i>{{ stats_generales.total_tickets }} ticket{{ stats_generales.total_tickets|pluralize }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#modalFiltros">
                                <i class="fas fa-filter me-2"></i>Filtrar Período
                            </button>
                            <a href="{% url 'tickets:lista_tickets' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Volver a Tickets
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas generales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-folder-open fa-2x"></i>
                    </div>
                    <h4 class="text-primary">{{ stats_generales.tickets_abiertos }}</h4>
                    <small class="text-muted">Abiertos</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                    <h4 class="text-warning">{{ stats_generales.tickets_en_progreso }}</h4>
                    <small class="text-muted">En Progreso</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="text-success">{{ stats_generales.tickets_cerrados }}</h4>
                    <small class="text-muted">Cerrados</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                    <h4 class="text-info">{{ stats_generales.tickets_pendientes }}</h4>
                    <small class="text-muted">Pendientes</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gráfico de tickets por día -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Tickets por Día (Últimos 7 días)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="chartTicketsPorDia" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Estadísticas por prioridad -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Por Prioridad
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="chartPrioridades" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas por área (solo administradores) -->
    {% if es_administrador and stats_areas %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>Rendimiento por Área
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Área</th>
                                        <th width="100">Total</th>
                                        <th width="100">Abiertos</th>
                                        <th width="100">Cerrados</th>
                                        <th width="150">% Resolución</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for area in stats_areas %}
                                        <tr>
                                            <td>{{ area.nombre }}</td>
                                            <td>
                                                <span class="badge bg-primary">{{ area.total }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">{{ area.abiertos }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ area.cerrados }}</span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-success" role="progressbar" 
                                                         style="width: {{ area.porcentaje_resolucion }}%">
                                                        {{ area.porcentaje_resolucion }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Top usuarios y tiempo promedio -->
    <div class="row">
        {% if puede_gestionar and top_usuarios %}
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Top Usuarios Asignados
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for usuario in top_usuarios %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-sm me-3">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ usuario.get_full_name|default:usuario.username }}</h6>
                                    <small class="text-muted">{{ usuario.total_asignaciones }} asignación{{ usuario.total_asignaciones|pluralize:"es" }}</small>
                                </div>
                                <span class="badge bg-info">{{ usuario.total_asignaciones }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Métricas de Tiempo
                    </h5>
                </div>
                <div class="card-body text-center">
                    {% if tiempo_promedio %}
                        <div class="mb-3">
                            <h2 class="text-primary">{{ tiempo_promedio }}</h2>
                            <p class="text-muted">Días promedio de resolución</p>
                        </div>
                    {% else %}
                        <div class="mb-3">
                            <h2 class="text-muted">N/A</h2>
                            <p class="text-muted">Sin datos suficientes</p>
                        </div>
                    {% endif %}
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-success">{{ stats_generales.tickets_cerrados }}</h5>
                            <small class="text-muted">Resueltos</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning">{{ stats_generales.tickets_abiertos|add:stats_generales.tickets_en_progreso }}</h5>
                            <small class="text-muted">Pendientes</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para filtros -->
<div class="modal fade" id="modalFiltros" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filtrar Período</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="GET">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="fecha_desde" class="form-label">Fecha Desde</label>
                            <input type="date" class="form-control" id="fecha_desde" name="fecha_desde" 
                                   value="{{ fecha_desde|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="fecha_hasta" class="form-label">Fecha Hasta</label>
                            <input type="date" class="form-control" id="fecha_hasta" name="fecha_hasta" 
                                   value="{{ fecha_hasta|date:'Y-m-d' }}">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Aplicar Filtros</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #6610f2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.avatar-sm {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #17a2b8, #138496);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.card {
    transition: transform 0.1s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de tickets por día
    const ctxDias = document.getElementById('chartTicketsPorDia').getContext('2d');
    new Chart(ctxDias, {
        type: 'line',
        data: {
            labels: [{% for dia in tickets_por_dia %}'{{ dia.fecha }}'{% if not forloop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Tickets Creados',
                data: [{% for dia in tickets_por_dia %}{{ dia.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Gráfico de prioridades
    const ctxPrioridades = document.getElementById('chartPrioridades').getContext('2d');
    new Chart(ctxPrioridades, {
        type: 'doughnut',
        data: {
            labels: ['Baja', 'Media', 'Alta', 'Crítica'],
            datasets: [{
                data: [{{ stats_prioridad.baja }}, {{ stats_prioridad.media }}, {{ stats_prioridad.alta }}, {{ stats_prioridad.critica }}],
                backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %}
