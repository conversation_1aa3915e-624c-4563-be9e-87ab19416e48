{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Gestionar Familiares - {{ usuario.get_full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con información del usuario -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">{{ usuario.get_full_name }}</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ usuario.username }} • 
                                    <i class="fas fa-briefcase me-1"></i>{{ usuario.cargo.nombre }}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarFamiliar">
                                <i class="fas fa-user-plus me-2"></i>Agregar Familiar
                            </button>
                            <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-left me-2"></i>Volver
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de familiares -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Familiares Registrados
                        <span class="badge bg-primary ms-2" id="contador-familiares">{{ familiares.count }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="lista-familiares">
                        {% if familiares %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th><i class="fas fa-user me-1"></i>Nombre</th>
                                            <th><i class="fas fa-heart me-1"></i>Parentesco</th>
                                            <th><i class="fas fa-phone me-1"></i>Teléfonos</th>
                                            <th><i class="fas fa-calendar me-1"></i>Fecha Registro</th>
                                            <th width="200"><i class="fas fa-cogs me-1"></i>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tbody-familiares">
                                        {% for familiar in familiares %}
                                        <tr id="familiar-{{ familiar.id }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <strong>{{ familiar.nombre }}</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ familiar.parentesco.parentesco }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success" id="telefonos-count-{{ familiar.id }}">
                                                    {{ familiar.celulares_emergencia.count }} número(s)
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ familiar.created_at|date:"d/m/Y H:i" }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-info" 
                                                            onclick="verDetallesFamiliar({{ familiar.id }}, '{{ familiar.nombre }}')"
                                                            title="Ver detalles">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="gestionarTelefonosFamiliar({{ familiar.id }}, '{{ familiar.nombre }}')"
                                                            title="Gestionar teléfonos">
                                                        <i class="fas fa-phone"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="eliminarFamiliar({{ familiar.id }}, '{{ familiar.nombre }}')"
                                                            title="Eliminar familiar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5" id="estado-vacio">
                                <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No hay familiares registrados</h5>
                                <p class="text-muted">Agrega el primer familiar para este usuario</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarFamiliar">
                                    <i class="fas fa-user-plus me-2"></i>Agregar Familiar
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para agregar familiar -->
<div class="modal fade" id="modalAgregarFamiliar" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Agregar Familiar
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="form-agregar-familiar">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre Completo <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nombre" name="nombre" 
                               placeholder="Nombre completo del familiar" required>
                    </div>
                    <div class="mb-3">
                        <label for="parentesco" class="form-label">Parentesco <span class="text-danger">*</span></label>
                        <select class="form-select" id="parentesco" name="parentesco" required>
                            <option value="">Selecciona el parentesco</option>
                            {% for parentesco in parentescos %}
                                <option value="{{ parentesco.parentesco }}">{{ parentesco.parentesco }}</option>
                            {% endfor %}
                            <option value="padre">Padre</option>
                            <option value="madre">Madre</option>
                            <option value="hermano">Hermano/a</option>
                            <option value="hijo">Hijo/a</option>
                            <option value="esposo">Esposo/a</option>
                            <option value="abuelo">Abuelo/a</option>
                            <option value="tio">Tío/a</option>
                            <option value="primo">Primo/a</option>
                            <option value="otro">Otro</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="telefono_emergencia" class="form-label">Teléfono de Emergencia</label>
                        <input type="text" class="form-control" id="telefono_emergencia" name="telefono_emergencia" 
                               placeholder="12345678" maxlength="8" pattern="[0-9]{8}">
                        <div class="form-text">Opcional: Ingresa 8 dígitos numéricos</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Guardar Familiar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.avatar-sm {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-agregar-familiar');
    const modal = new bootstrap.Modal(document.getElementById('modalAgregarFamiliar'));
    
    // Manejar envío del formulario
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Mostrar loading
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Guardando...';
        
        fetch('', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Cerrar modal
                modal.hide();
                
                // Mostrar mensaje de éxito
                Swal.fire({
                    icon: 'success',
                    title: '¡Éxito!',
                    text: data.message,
                    timer: 2000,
                    showConfirmButton: false
                });
                
                // Agregar familiar a la tabla
                agregarFamiliarATabla(data.familiar);
                
                // Limpiar formulario
                form.reset();
                
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor'
            });
        })
        .finally(() => {
            // Restaurar botón
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
    
    // Validación en tiempo real del teléfono
    document.getElementById('telefono_emergencia').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Solo números
        if (value.length > 8) value = value.slice(0, 8);
        e.target.value = value;
    });
});

function agregarFamiliarATabla(familiar) {
    const tbody = document.getElementById('tbody-familiares');
    const estadoVacio = document.getElementById('estado-vacio');
    const contador = document.getElementById('contador-familiares');
    
    // Si no hay tabla, crearla
    if (estadoVacio) {
        document.getElementById('lista-familiares').innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-user me-1"></i>Nombre</th>
                            <th><i class="fas fa-heart me-1"></i>Parentesco</th>
                            <th><i class="fas fa-phone me-1"></i>Teléfonos</th>
                            <th><i class="fas fa-calendar me-1"></i>Fecha Registro</th>
                            <th width="200"><i class="fas fa-cogs me-1"></i>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="tbody-familiares">
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // Agregar fila
    const newRow = document.createElement('tr');
    newRow.id = `familiar-${familiar.id}`;
    newRow.innerHTML = `
        <td>
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-2">
                    <i class="fas fa-user"></i>
                </div>
                <strong>${familiar.nombre}</strong>
            </div>
        </td>
        <td><span class="badge bg-info">${familiar.parentesco}</span></td>
        <td><span class="badge bg-success" id="telefonos-count-${familiar.id}">${familiar.telefonos_count} número(s)</span></td>
        <td><small class="text-muted">Ahora</small></td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-info" 
                        onclick="verDetallesFamiliar(${familiar.id}, '${familiar.nombre}')"
                        title="Ver detalles">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary" 
                        onclick="gestionarTelefonosFamiliar(${familiar.id}, '${familiar.nombre}')"
                        title="Gestionar teléfonos">
                    <i class="fas fa-phone"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" 
                        onclick="eliminarFamiliar(${familiar.id}, '${familiar.nombre}')"
                        title="Eliminar familiar">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    document.getElementById('tbody-familiares').appendChild(newRow);
    
    // Actualizar contador
    const currentCount = parseInt(contador.textContent) + 1;
    contador.textContent = currentCount;
}

function verDetallesFamiliar(id, nombre) {
    // Usar la nueva URL específica para gestión de familiares
    fetch(`/usuarios/usuarios/{{ usuario.pk }}/familiares/${id}/detalles-gestion/`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let telefonosHtml = '';
            if (data.telefonos && data.telefonos.length > 0) {
                telefonosHtml = data.telefonos.map(tel =>
                    `<div class="d-flex justify-content-between align-items-center mb-2">
                        <span><i class="fas fa-phone me-2"></i>${tel.numero}</span>
                        <span class="badge bg-${tel.is_active ? 'success' : 'secondary'}">${tel.is_active ? 'Activo' : 'Inactivo'}</span>
                    </div>`
                ).join('');
            } else {
                telefonosHtml = '<p class="text-muted">No hay teléfonos de emergencia registrados.</p>';
            }

            Swal.fire({
                title: `<i class="fas fa-user me-2"></i>Detalles de ${nombre}`,
                html: `
                    <div class="text-start">
                        <p><strong>Nombre:</strong> ${data.familiar.nombre}</p>
                        <p><strong>Parentesco:</strong> ${data.familiar.parentesco}</p>
                        <p><strong>Estado:</strong>
                            <span class="badge bg-${data.familiar.is_active ? 'success' : 'secondary'}">
                                ${data.familiar.is_active ? 'Activo' : 'Inactivo'}
                            </span>
                        </p>
                        <hr>
                        <h6><i class="fas fa-phone me-2"></i>Teléfonos de Emergencia:</h6>
                        ${telefonosHtml}
                    </div>
                `,
                width: '500px',
                confirmButtonText: 'Cerrar',
                confirmButtonColor: '#1A237E'
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'No se pudieron cargar los detalles del familiar'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error de conexión',
            text: 'No se pudo conectar con el servidor'
        });
    });
}

function gestionarTelefonosFamiliar(id, nombre) {
    // Reutilizar la funcionalidad existente de gestión de teléfonos
    Swal.fire({
        title: `<i class="fas fa-phone me-2"></i>Gestionar Teléfonos - ${nombre}`,
        html: `
            <div id="telefonos-container">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>Cargando teléfonos...</p>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-8">
                    <input type="text" id="nuevo-telefono" class="form-control"
                           placeholder="Nuevo número de teléfono"
                           pattern="[0-9\\+\\s\\(\\)\\-]{8,15}">
                </div>
                <div class="col-4">
                    <button type="button" id="agregar-telefono-btn" class="btn btn-success w-100">
                        <i class="fas fa-plus me-1"></i>Agregar
                    </button>
                </div>
            </div>
        `,
        width: '600px',
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'Cerrar',
        cancelButtonColor: '#6c757d',
        didOpen: () => {
            cargarTelefonosFamiliar(id);

            // Event listener para agregar teléfono
            document.getElementById('agregar-telefono-btn').addEventListener('click', function() {
                const nuevoTelefono = document.getElementById('nuevo-telefono').value.trim();
                if (nuevoTelefono) {
                    agregarTelefonoFamiliar(id, nuevoTelefono);
                } else {
                    Swal.showValidationMessage('Por favor ingresa un número de teléfono');
                }
            });
        }
    });
}

// Reutilizar funciones existentes del paso 3
function cargarTelefonosFamiliar(familiarId) {
    fetch(`/usuarios/usuarios/{{ usuario.pk }}/familiares/${familiarId}/telefonos-gestion/`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const container = document.getElementById('telefonos-container');
        if (data.success && data.telefonos.length > 0) {
            let telefonosHtml = data.telefonos.map(tel => `
                <div class="telefono-emergencia-item" data-telefono-id="${tel.id}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-phone me-2"></i>${tel.numero}</span>
                        <div>
                            <span class="badge bg-${tel.is_active ? 'success' : 'secondary'} me-2">
                                ${tel.is_active ? 'Activo' : 'Inactivo'}
                            </span>
                            <button type="button" class="btn btn-sm btn-outline-danger eliminar-telefono-emergencia"
                                    data-telefono-id="${tel.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = telefonosHtml;

            // Agregar event listeners para eliminar teléfonos
            container.querySelectorAll('.eliminar-telefono-emergencia').forEach(btn => {
                btn.addEventListener('click', function() {
                    const telefonoId = this.dataset.telefonoId;
                    eliminarTelefonoEmergencia(telefonoId, familiarId);
                });
            });
        } else {
            container.innerHTML = '<p class="text-muted text-center">No hay teléfonos de emergencia registrados.</p>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('telefonos-container').innerHTML =
            '<p class="text-danger text-center">Error al cargar teléfonos</p>';
    });
}

function agregarTelefonoFamiliar(familiarId, numero) {
    const formData = new FormData();
    formData.append('numero', numero);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/usuarios/familiar/${familiarId}/telefono/agregar/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('nuevo-telefono').value = '';
            cargarTelefonosFamiliar(familiarId);

            // Actualizar contador en la tabla principal
            const contador = document.getElementById(`telefonos-count-${familiarId}`);
            if (contador) {
                const currentCount = parseInt(contador.textContent.match(/\d+/)[0]) + 1;
                contador.textContent = `${currentCount} número(s)`;
            }

            Swal.showValidationMessage('');
        } else {
            Swal.showValidationMessage(data.message || 'Error al agregar teléfono');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.showValidationMessage('Error de conexión');
    });
}

function eliminarTelefonoEmergencia(telefonoId, familiarId) {
    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(`/usuarios/telefono/${telefonoId}/eliminar/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            cargarTelefonosFamiliar(familiarId);

            // Actualizar contador en la tabla principal
            const contador = document.getElementById(`telefonos-count-${familiarId}`);
            if (contador) {
                const currentCount = Math.max(0, parseInt(contador.textContent.match(/\d+/)[0]) - 1);
                contador.textContent = `${currentCount} número(s)`;
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function eliminarFamiliar(id, nombre) {
    Swal.fire({
        title: '¿Eliminar familiar?',
        text: `Se eliminará a ${nombre} y todos sus teléfonos de emergencia`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            // Mostrar loading
            Swal.fire({
                title: 'Eliminando...',
                text: 'Procesando solicitud',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Realizar eliminación real
            fetch(`{% url 'user:eliminar_familiar_usuario' usuario.pk 0 %}`.replace('0', id), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remover fila de la tabla
                    document.getElementById(`familiar-${id}`).remove();

                    // Actualizar contador
                    const contador = document.getElementById('contador-familiares');
                    const currentCount = parseInt(contador.textContent) - 1;
                    contador.textContent = currentCount;

                    // Si no quedan familiares, mostrar estado vacío
                    if (currentCount === 0) {
                        document.getElementById('lista-familiares').innerHTML = `
                            <div class="text-center py-5" id="estado-vacio">
                                <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No hay familiares registrados</h5>
                                <p class="text-muted">Agrega el primer familiar para este usuario</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarFamiliar">
                                    <i class="fas fa-user-plus me-2"></i>Agregar Familiar
                                </button>
                            </div>
                        `;
                    }

                    Swal.fire({
                        icon: 'success',
                        title: 'Eliminado',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Error al eliminar familiar'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}
</script>
{% endblock %}
