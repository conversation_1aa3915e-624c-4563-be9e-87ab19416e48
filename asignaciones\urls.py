"""
asignaciones/urls.py
URLs para la gestión de asignaciones de tickets.

Incluye rutas para:
- Listado de asignaciones
- Gestión de asignaciones por supervisor
- Historial de asignaciones
- Reportes de productividad
"""

from django.urls import path
from . import views

app_name = 'asignaciones'

urlpatterns = [
    # Listado y gestión de asignaciones
    path('', views.lista_asignaciones, name='lista_asignaciones'),
    path('mis-asignaciones/', views.mis_asignaciones, name='mis_asignaciones'),
    path('grupo/', views.asignaciones_grupo, name='asignaciones_grupo'),
    
    # Gestión de asignaciones (supervisores)
    path('asignar/', views.crear_asignacion, name='crear_asignacion'),
    path('ticket/<int:ticket_id>/asignar/', views.asignar_ticket, name='asignar_ticket'),
    path('ticket/<int:ticket_id>/desasignar/<int:usuario_id>/', views.desasignar_ticket, name='desasignar_ticket'),
    path('<int:asignacion_id>/editar/', views.editar_asignacion, name='editar_asignacion'),
    path('<int:asignacion_id>/iniciar/', views.iniciar_asignacion, name='iniciar_asignacion'),
    path('<int:asignacion_id>/finalizar/', views.finalizar_asignacion, name='finalizar_asignacion'),
    path('<int:asignacion_id>/cancelar/', views.cancelar_asignacion, name='cancelar_asignacion'),
    
    # Historial y reportes
    path('historial/', views.historial_asignaciones, name='historial_asignaciones'),
    path('reportes/', views.reportes_asignaciones, name='reportes_asignaciones'),
    path('productividad/', views.reporte_productividad, name='reporte_productividad'),
    
    # AJAX endpoints
    path('ajax/cambiar-estado/', views.cambiar_estado_ajax, name='cambiar_estado_ajax'),
    path('ajax/obtener-usuarios-grupo/', views.obtener_usuarios_grupo, name='usuarios_grupo_ajax'),
    path('ajax/estadisticas-usuario/', views.estadisticas_usuario_ajax, name='estadisticas_usuario'),
]
