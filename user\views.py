"""
user/views.py
Vistas para la gestión de usuarios y su información relacionada.
Usa el sistema centralizado de permisos para control de acceso.
"""
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Prefetch, Q
from django.http import JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods

from .models import CelularEmergencia, User, CargoUsuario, CelularUsuario, Familiar, Parentesco
from .forms import (
    CustomUserCreationForm, CustomUserChangeForm, CargoUsuarioForm,
    CelularUsuarioForm, FamiliarForm, CelularEmergenciaForm,
    UsuarioBasicoForm
)
from django.db import transaction

# Usar el sistema centralizado de permisos
from permissions.decorators import can_manage_users_required

# Decoradores personalizados
def staff_required(view_func):
    """
    Decorador que verifica si el usuario es staff.
    Redirige al login si no lo es.
    """
    decorated_view_func = login_required(permission_required('user.view_user', raise_exception=True)(view_func))
    return decorated_view_func

def ajax_required(view_func):
    """Decorador que verifica si la petición es AJAX."""
    def wrap(request, *args, **kwargs):
        if not request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'error': 'No permitido'}, status=400)
        return view_func(request, *args, **kwargs)
    return wrap

# Vistas de Usuario
@can_manage_users_required
def lista_usuarios(request):
    """
    Lista paginada de usuarios con búsqueda y filtros.

    Args:
        request: HttpRequest object

    Returns:
        Render del template con lista de usuarios o JSON para scroll infinito
    """
    queryset = User.objects.select_related('cargo').prefetch_related(
        'celulares',
        Prefetch('familiares', queryset=Familiar.objects.filter(is_active=True))
    ).filter(is_active=True)

    # Búsqueda
    busqueda = request.GET.get('q', '')
    if busqueda:
        queryset = queryset.filter(
            Q(username__icontains=busqueda) |
            Q(first_name__icontains=busqueda) |
            Q(last_name__icontains=busqueda) |
            Q(dpi__icontains=busqueda)
        )

    # Filtros
    cargo = request.GET.get('cargo')
    if cargo:
        queryset = queryset.filter(cargo_id=cargo)

    # Paginación
    paginator = Paginator(queryset, 20)
    page = request.GET.get('page', 1)
    usuarios = paginator.get_page(page)

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        html = render_to_string(
            'user/parciales/lista_usuarios.html',
            {'usuarios': usuarios},
            request=request
        )
        return JsonResponse({
            'html': html,
            'has_next': usuarios.has_next()
        })

    context = {
        'usuarios': usuarios,
        'cargos': CargoUsuario.objects.filter(is_active=True),
        'busqueda': busqueda
    }
    return render(request, 'user/lista_usuarios.html', context)


@can_manage_users_required
def lista_usuarios_inactivos(request):
    """
    Lista paginada de usuarios inactivos con búsqueda y filtros.

    Optimizada para manejar hasta 500 usuarios inactivos con carga eficiente:
    - select_related para evitar N+1 queries
    - prefetch_related para datos relacionados
    - Paginación para carga progresiva
    - AJAX para scroll infinito

    Args:
        request: HttpRequest object

    Returns:
        Render del template con lista de usuarios inactivos o JSON para scroll infinito
    """
    # Query optimizada con las mismas optimizaciones que usuarios activos
    queryset = User.objects.select_related('cargo').prefetch_related(
        'celulares',
        Prefetch('familiares', queryset=Familiar.objects.filter(is_active=True))
    ).filter(is_active=False)  # Solo usuarios inactivos

    # Búsqueda
    busqueda = request.GET.get('q', '')
    if busqueda:
        queryset = queryset.filter(
            Q(username__icontains=busqueda) |
            Q(first_name__icontains=busqueda) |
            Q(last_name__icontains=busqueda) |
            Q(dpi__icontains=busqueda)
        )

    # Filtros
    cargo = request.GET.get('cargo')
    if cargo:
        queryset = queryset.filter(cargo_id=cargo)

    # Paginación optimizada para usuarios inactivos (25 por página para mejor rendimiento)
    paginator = Paginator(queryset, 25)
    page = request.GET.get('page', 1)
    usuarios = paginator.get_page(page)

    # Respuesta AJAX para scroll infinito
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        html = render_to_string(
            'user/parciales/lista_usuarios_inactivos.html',
            {'usuarios': usuarios},
            request=request
        )
        return JsonResponse({
            'html': html,
            'has_next': usuarios.has_next()
        })

    # Contexto para template principal
    context = {
        'usuarios': usuarios,
        'cargos': CargoUsuario.objects.all(),
        'busqueda': busqueda,
        'cargo_seleccionado': cargo,
        'total_usuarios': queryset.count(),
        'titulo': 'Usuarios Inactivos'
    }

    return render(request, 'user/lista_usuarios_inactivos.html', context)


@permission_required('user.change_user')
@require_http_methods(["POST"])
def activar_usuario(request, pk):
    """
    Activa un usuario inactivo.

    Args:
        request: HttpRequest object
        pk: ID del usuario a activar

    Returns:
        JsonResponse: Resultado de la operación
    """
    try:
        usuario = get_object_or_404(User, pk=pk, is_active=False)
        usuario.is_active = True
        usuario.save()

        return JsonResponse({
            'success': True,
            'message': f'Usuario "{usuario.get_full_name()}" activado correctamente.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al activar usuario: {str(e)}'
        })


@permission_required('user.change_user')
@require_http_methods(["POST"])
def desactivar_usuario_definitivo(request, pk):
    """
    Desactiva un usuario activo.

    Args:
        request: HttpRequest object
        pk: ID del usuario a desactivar

    Returns:
        JsonResponse: Resultado de la operación
    """
    try:
        usuario = get_object_or_404(User, pk=pk, is_active=True)
        usuario.is_active = False
        usuario.save()

        return JsonResponse({
            'success': True,
            'message': f'Usuario "{usuario.get_full_name()}" desactivado correctamente.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al desactivar usuario: {str(e)}'
        })

@can_manage_users_required
def detalle_usuario(request, pk):
    """
    Muestra información detallada de un usuario específico.

    Args:
        request: HttpRequest object
        pk: Primary key del usuario

    Returns:
        Render del template con detalles del usuario
    """
    usuario = get_object_or_404(
        User.objects.select_related('cargo').prefetch_related(
            'celulares',
            'familiares__celulares_emergencia'
        ),
        pk=pk,
        is_active=True
    )

    context = {
        'usuario': usuario,
        'puede_editar': request.user.has_perm('user.change_user')
    }
    return render(request, 'user/detalle_usuario.html', context)


@staff_required
def detalle_usuario_inactivo(request, pk):
    """
    Muestra información detallada de un usuario inactivo específico.

    Vista específica para usuarios inactivos que no permite edición.
    Reutiliza la misma lógica de consulta optimizada que detalle_usuario.

    Args:
        request: HttpRequest object
        pk: Primary key del usuario inactivo

    Returns:
        Render del template con detalles del usuario inactivo
    """
    usuario = get_object_or_404(
        User.objects.select_related('cargo').prefetch_related(
            'celulares',
            'familiares__celulares_emergencia'
        ),
        pk=pk,
        is_active=False  # Solo usuarios inactivos
    )

    context = {
        'usuario': usuario,
        'puede_editar': False  # No se puede editar usuarios inactivos
    }
    return render(request, 'user/detalle_usuario_inactivo.html', context)

@can_manage_users_required
def crear_usuario_paso1(request):
    """
    Paso 1: Crear información básica del usuario.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Formulario del paso 1 o redirección al paso 2
    """
    if request.method == 'POST':
        form = UsuarioBasicoForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Crear usuario
                    usuario = form.save(commit=False)
                    usuario.save()

                    # Asignar grupo seleccionado
                    grupo = form.cleaned_data['grupo']
                    usuario.groups.set([grupo])

                    # Guardar ID del usuario en sesión para los siguientes pasos
                    request.session['usuario_creacion_id'] = usuario.id

                    messages.success(request, f'Usuario "{usuario.username}" creado exitosamente.')
                    return redirect('user:crear_usuario_paso2')

            except Exception as e:
                messages.error(request, f'Error al crear usuario: {str(e)}')
        else:
            messages.error(request, 'Por favor, corrige los errores en el formulario.')
    else:
        form = UsuarioBasicoForm()

    context = {
        'form': form,
        'paso_actual': 1,
        'total_pasos': 3
    }

    return render(request, 'user/crear_usuario_paso1.html', context)


def obtener_grupo_por_cargo(nombre_cargo):
    """
    Mapea el nombre del cargo al grupo correspondiente.

    Args:
        nombre_cargo (str): Nombre del cargo

    Returns:
        str: Nombre del grupo o None
    """
    mapeo_cargos = {
        'administrador': 'Admin',
        'admin': 'Admin',
        'secretaria': 'Secretaria',
        'supervisor': 'Admin',  # Los supervisores tienen permisos de Admin
        'empleado': 'Empleado',
    }

    return mapeo_cargos.get(nombre_cargo.lower())


@can_manage_users_required
def crear_usuario_paso2(request):
    """
    Paso 2: Agregar teléfonos del usuario.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Formulario del paso 2 o redirección al paso 3
    """
    # Verificar que existe usuario en sesión
    usuario_id = request.session.get('usuario_creacion_id')
    if not usuario_id:
        messages.error(request, 'Sesión expirada. Inicia el proceso nuevamente.')
        return redirect('user:crear_usuario_paso1')

    usuario = get_object_or_404(User, id=usuario_id)

    # Manejar AJAX para agregar teléfonos
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # Verificar si es eliminación de teléfono
        if 'eliminar_telefono' in request.POST:
            telefono_id = request.POST.get('telefono_id')
            try:
                telefono = CelularUsuario.objects.get(id=telefono_id, usuario=usuario)
                telefono.delete()
                return JsonResponse({'success': True, 'message': 'Teléfono eliminado correctamente'})
            except CelularUsuario.DoesNotExist:
                return JsonResponse({'success': False, 'message': 'Teléfono no encontrado'})

        # Agregar nuevo teléfono
        form = CelularUsuarioForm(request.POST)
        if form.is_valid():
            numero = form.cleaned_data['numero']

            # Verificar si el número ya existe para este usuario
            if CelularUsuario.objects.filter(usuario=usuario, numero=numero).exists():
                return JsonResponse({
                    'success': False,
                    'message': f'El número {numero} ya está registrado para este usuario'
                })

            celular = form.save(commit=False)
            celular.usuario = usuario
            # Convertir string a boolean para is_active
            is_active_value = request.POST.get('is_active', 'true')
            celular.is_active = is_active_value == 'true'
            celular.save()

            return JsonResponse({
                'success': True,
                'message': 'Teléfono agregado correctamente',
                'celular': {
                    'id': celular.id,
                    'numero': celular.numero,
                    'tipo': celular.get_tipo_display(),
                    'is_active': celular.is_active
                }
            })
        else:
            errors = []
            for field, field_errors in form.errors.items():
                for error in field_errors:
                    errors.append(f'{field}: {error}')
            return JsonResponse({
                'success': False,
                'message': 'Errores en el formulario: ' + ', '.join(errors)
            })

    # Continuar al paso 3
    if request.method == 'POST' and 'continuar' in request.POST:
        return redirect('user:crear_usuario_paso3')

    # Obtener teléfonos actuales (todos, activos e inactivos)
    telefonos = CelularUsuario.objects.filter(usuario=usuario)

    context = {
        'usuario': usuario,
        'form': CelularUsuarioForm(),
        'telefonos': telefonos,
        'tipos_telefono': CelularUsuario.TIPO_CHOICES,
        'paso_actual': 2,
        'total_pasos': 3
    }

    return render(request, 'user/crear_usuario_paso2.html', context)


@permission_required('user.add_user')
def crear_usuario_paso3(request):
    """
    Paso 3 del proceso de creación de usuario: Gestión de familiares.

    Esta vista maneja únicamente:
    - GET: Mostrar el formulario del paso 3 con familiares existentes
    - POST (AJAX): Crear nuevos familiares
    - POST (form): Finalizar el proceso de creación

    Todas las demás operaciones AJAX (eliminar, gestionar teléfonos)
    se manejan en vistas separadas para mantener responsabilidades claras.

    Args:
        request (HttpRequest): Objeto de petición HTTP

    Returns:
        HttpResponse: Renderizado del template del paso 3
        JsonResponse: Para peticiones AJAX de creación de familiares

    Raises:
        Http404: Si no existe el usuario en sesión
        PermissionDenied: Si el usuario no tiene permisos
    """
    # Verificar que existe usuario en sesión
    usuario_id = request.session.get('usuario_creacion_id')
    if not usuario_id:
        messages.error(request, 'Sesión expirada. Inicia el proceso nuevamente.')
        return redirect('user:crear_usuario_paso1')

    usuario = get_object_or_404(User, id=usuario_id)

    # Manejar AJAX solo para CREAR familiares
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return _crear_familiar_ajax(request, usuario)

    # Manejar POST normal para finalizar proceso
    if request.method == 'POST' and 'finalizar' in request.POST:
        # Limpiar sesión
        del request.session['usuario_creacion_id']
        messages.success(request, f'Usuario "{usuario.username}" creado completamente.')
        return redirect('user:detalle_usuario', pk=usuario.pk)

    # Obtener familiares actuales
    familiares = Familiar.objects.filter(usuario=usuario, is_active=True).select_related('parentesco')

    context = {
        'usuario': usuario,
        'form': FamiliarForm(),
        'familiares': familiares,
        'paso_actual': 3,
        'total_pasos': 3
    }

    return render(request, 'user/crear_usuario_paso3.html', context)


def _crear_familiar_ajax(request, usuario):
    """
    Función auxiliar para crear un familiar vía AJAX.

    Maneja únicamente la creación de familiares con su teléfono principal.
    Separada para mantener la responsabilidad única.

    Args:
        request (HttpRequest): Petición HTTP con datos del familiar
        usuario (User): Usuario al que pertenecerá el familiar

    Returns:
        JsonResponse: Resultado de la operación con datos del familiar creado

    Raises:
        JsonResponse con error si falla la validación o creación
    """
    # Extraer datos del formulario
    nombre = request.POST.get('nombre', '').strip()
    parentesco = request.POST.get('parentesco', '').strip()
    telefono_principal = request.POST.get('telefono_principal', '').strip()

    # Validaciones básicas
    if not nombre or not parentesco or not telefono_principal:
        return JsonResponse({
            'success': False,
            'message': 'Todos los campos son obligatorios'
        })

    # Verificar si ya existe un familiar con el mismo nombre
    if Familiar.objects.filter(usuario=usuario, nombre=nombre).exists():
        return JsonResponse({
            'success': False,
            'message': f'Ya existe un familiar con el nombre "{nombre}"'
        })

    try:
        # Obtener o crear el parentesco
        parentesco_obj, created = Parentesco.objects.get_or_create(
            parentesco=parentesco.title()
        )

        # Crear el familiar
        familiar = Familiar.objects.create(
            usuario=usuario,
            nombre=nombre,
            parentesco=parentesco_obj,
            is_active=True
        )

        # Crear el teléfono principal
        CelularEmergencia.objects.create(
            familiar=familiar,
            numero=telefono_principal,
            is_active=True
        )

        return JsonResponse({
            'success': True,
            'message': 'Familiar agregado correctamente',
            'familiar': {
                'id': familiar.id,
                'nombre': familiar.nombre,
                'parentesco': str(familiar.parentesco),
                'telefonos_count': 1
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al crear familiar: {str(e)}'
        })


@login_required
@require_http_methods(["GET"])
def familiar_detalles_ajax(request, familiar_id):
    """
    Vista AJAX para obtener detalles completos de un familiar.

    Retorna información del familiar y todos sus teléfonos de emergencia.
    Utilizada por el modal "Ver detalles" en el paso 3 de creación de usuario.

    Args:
        request (HttpRequest): Petición HTTP (debe ser GET)
        familiar_id (int): ID del familiar a consultar

    Returns:
        JsonResponse: Datos del familiar y sus teléfonos

    Response format:
        {
            'success': bool,
            'familiar': {
                'id': int,
                'nombre': str,
                'parentesco': str,
                'is_active': bool
            },
            'telefonos': [
                {
                    'id': int,
                    'numero': str,
                    'is_active': bool
                }
            ]
        }

    Raises:
        JsonResponse con error si no se encuentra el familiar o hay problemas de sesión
    """
    try:
        # Obtener el usuario en creación desde la sesión (CORREGIDO: usar la clave correcta)
        usuario_id = request.session.get('usuario_creacion_id')

        if not usuario_id:
            return JsonResponse({
                'success': False,
                'message': 'Sesión expirada. Reinicia el proceso de creación.'
            })

        familiar = Familiar.objects.get(id=familiar_id, usuario__id=usuario_id)
        telefonos = CelularEmergencia.objects.filter(familiar=familiar)

        return JsonResponse({
            'success': True,
            'familiar': {
                'id': familiar.id,
                'nombre': familiar.nombre,
                'parentesco': str(familiar.parentesco),
                'is_active': familiar.is_active
            },
            'telefonos': [
                {
                    'id': tel.id,
                    'numero': tel.numero,
                    'is_active': tel.is_active
                } for tel in telefonos
            ]
        })

    except Familiar.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Familiar no encontrado o no pertenece al usuario en creación.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error interno del servidor: {str(e)}'
        })


@login_required
@require_http_methods(["GET"])
def familiar_telefonos_ajax(request, familiar_id):
    """
    Vista AJAX para obtener únicamente los teléfonos de un familiar.

    Utilizada por el modal "Gestionar teléfonos" para cargar la lista actual
    de números de emergencia de un familiar específico.

    Args:
        request (HttpRequest): Petición HTTP (debe ser GET)
        familiar_id (int): ID del familiar cuyos teléfonos se consultan

    Returns:
        JsonResponse: Lista de teléfonos del familiar

    Response format:
        {
            'success': bool,
            'telefonos': [
                {
                    'id': int,
                    'numero': str,
                    'is_active': bool
                }
            ]
        }

    Raises:
        JsonResponse con error si no se encuentra el familiar o hay problemas de sesión
    """
    try:
        # Obtener el usuario en creación desde la sesión (CORREGIDO: usar la clave correcta)
        usuario_id = request.session.get('usuario_creacion_id')

        if not usuario_id:
            return JsonResponse({
                'success': False,
                'message': 'Sesión expirada. Reinicia el proceso de creación.'
            })

        familiar = Familiar.objects.get(id=familiar_id, usuario__id=usuario_id)
        telefonos = CelularEmergencia.objects.filter(familiar=familiar)

        return JsonResponse({
            'success': True,
            'telefonos': [
                {
                    'id': tel.id,
                    'numero': tel.numero,
                    'is_active': tel.is_active
                } for tel in telefonos
            ]
        })

    except Familiar.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Familiar no encontrado o no pertenece al usuario en creación.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error interno del servidor: {str(e)}'
        })


# Vista eliminada - duplicada con eliminar_familiar_usuario que es más específica y segura


@login_required
@require_http_methods(["POST"])
def telefono_agregar_ajax(request, familiar_id):
    """
    Vista AJAX para agregar un teléfono de emergencia a un familiar.

    Valida que el número no esté duplicado para el mismo familiar antes de crearlo.
    Utilizada por el modal "Gestionar teléfonos".

    Args:
        request (HttpRequest): Petición HTTP (debe ser POST con CSRF token)
        familiar_id (int): ID del familiar al que se agregará el teléfono

    POST Parameters:
        numero (str): Número de teléfono a agregar

    Returns:
        JsonResponse: Resultado de la operación

    Response format:
        {
            'success': bool,
            'message': str,
            'telefono': {  # Solo si success=True
                'id': int,
                'numero': str,
                'is_active': bool
            }
        }

    Raises:
        JsonResponse con error si hay problemas de validación, duplicados o sesión
    """
    try:
        # Obtener el usuario en creación desde la sesión
        usuario_id = request.session.get('usuario_creacion_id')

        if not usuario_id:
            return JsonResponse({
                'success': False,
                'message': 'Sesión expirada. Reinicia el proceso de creación.'
            })

        familiar = Familiar.objects.get(id=familiar_id, usuario__id=usuario_id)
        numero = request.POST.get('numero', '').strip()

        # Validar que se proporcionó un número
        if not numero:
            return JsonResponse({
                'success': False,
                'message': 'El número de teléfono es obligatorio.'
            })

        # Verificar que no esté duplicado para este familiar
        if CelularEmergencia.objects.filter(familiar=familiar, numero=numero).exists():
            return JsonResponse({
                'success': False,
                'message': f'El número "{numero}" ya está registrado para este familiar.'
            })

        # Crear el teléfono de emergencia
        telefono = CelularEmergencia.objects.create(
            familiar=familiar,
            numero=numero,
            is_active=True
        )

        return JsonResponse({
            'success': True,
            'message': 'Teléfono agregado correctamente.',
            'telefono': {
                'id': telefono.id,
                'numero': telefono.numero,
                'is_active': telefono.is_active
            }
        })

    except Familiar.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Familiar no encontrado o no pertenece al usuario en creación.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al agregar teléfono: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def telefono_eliminar_ajax(request, telefono_id):
    """
    Vista AJAX para eliminar un teléfono de emergencia.

    Realiza eliminación física del teléfono. Valida que el teléfono pertenezca
    al usuario en creación. Utilizada por el modal "Gestionar teléfonos".

    Args:
        request (HttpRequest): Petición HTTP (debe ser POST con CSRF token)
        telefono_id (int): ID del teléfono a eliminar

    Returns:
        JsonResponse: Resultado de la operación

    Response format:
        {
            'success': bool,
            'message': str
        }

    Raises:
        JsonResponse con error si no se encuentra el teléfono o hay problemas de sesión
    """
    try:
        # Obtener el usuario en creación desde la sesión
        usuario_id = request.session.get('usuario_creacion_id')

        if not usuario_id:
            return JsonResponse({
                'success': False,
                'message': 'Sesión expirada. Reinicia el proceso de creación.'
            })

        # Buscar el teléfono y validar que pertenezca al usuario en creación
        telefono = CelularEmergencia.objects.get(
            id=telefono_id,
            familiar__usuario__id=usuario_id
        )
        numero = telefono.numero

        # Eliminar físicamente el teléfono
        telefono.delete()

        return JsonResponse({
            'success': True,
            'message': f'Teléfono "{numero}" eliminado correctamente.'
        })

    except CelularEmergencia.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Teléfono no encontrado o no pertenece al usuario en creación.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al eliminar teléfono: {str(e)}'
        })


@permission_required('user.change_user')
def eliminar_telefono_ajax(request, telefono_id):
    """
    Elimina un teléfono vía AJAX.

    Args:
        request: HttpRequest object
        telefono_id: ID del teléfono a eliminar

    Returns:
        JsonResponse: Resultado de la operación
    """
    if request.method == 'POST':
        try:
            telefono = get_object_or_404(CelularUsuario, id=telefono_id, is_active=True)
            telefono.is_active = False
            telefono.save()

            return JsonResponse({'success': True, 'message': 'Teléfono eliminado correctamente'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Método no permitido'})


# Vista eliminada - duplicada con familiar_eliminar_ajax que hace eliminación física


# Vistas eliminadas - duplicadas con telefono_agregar_ajax y telefono_eliminar_ajax
# que tienen mejor implementación con eliminación física

@permission_required('user.change_user')
def editar_usuario(request, pk):
    """
    Edita información de un usuario existente.

    Args:
        request: HttpRequest object
        pk: Primary key del usuario

    Returns:
        Redirección a detalle de usuario o render del form con errores
    """
    usuario = get_object_or_404(User, pk=pk, is_active=True)
    if request.method == 'POST':
        form = CustomUserChangeForm(request.POST, instance=usuario)
        if form.is_valid():
            form.save()
            messages.success(request, 'Usuario actualizado exitosamente.')
            return redirect('user:detalle_usuario', pk=usuario.pk)
    else:
        form = CustomUserChangeForm(instance=usuario)

    return render(request, 'user/editar_usuario.html', {'form': form, 'usuario': usuario})

@permission_required('user.delete_user')
def desactivar_usuario(request, pk):
    """
    Desactiva un usuario (soft delete).

    Args:
        request: HttpRequest object
        pk: Primary key del usuario

    Returns:
        JsonResponse con estado de la operación
    """
    if request.method == 'POST':
        usuario = get_object_or_404(User, pk=pk)
        usuario.is_active = False
        usuario.save()
        messages.success(request, 'Usuario desactivado exitosamente.')
        return JsonResponse({'success': True})
    return JsonResponse({'error': 'Método no permitido'}, status=405)


@permission_required('user.change_user')
def eliminar_familiar_ajax(request, familiar_id):
    """
    Elimina un familiar vía AJAX.

    Args:
        request: HttpRequest object
        familiar_id: ID del familiar a eliminar

    Returns:
        JsonResponse: Resultado de la operación
    """
    if request.method == 'POST':
        try:
            familiar = get_object_or_404(Familiar, id=familiar_id, is_active=True)
            familiar.is_active = False
            familiar.save()

            return JsonResponse({'success': True, 'message': 'Familiar eliminado correctamente'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Método no permitido'})


@permission_required('user.change_user')
def agregar_telefono_emergencia_ajax(request, familiar_id):
    """
    Agrega un teléfono de emergencia a un familiar vía AJAX.

    Args:
        request: HttpRequest object
        familiar_id: ID del familiar

    Returns:
        JsonResponse: Resultado de la operación
    """
    if request.method == 'POST':
        familiar = get_object_or_404(Familiar, id=familiar_id, is_active=True)
        form = CelularEmergenciaForm(request.POST)

        if form.is_valid():
            celular = form.save(commit=False)
            celular.familiar = familiar
            celular.save()

            return JsonResponse({
                'success': True,
                'celular': {
                    'id': celular.id,
                    'numero': celular.numero
                }
            })
        else:
            return JsonResponse({'success': False, 'errors': form.errors})

    return JsonResponse({'success': False, 'error': 'Método no permitido'})


@permission_required('user.change_user')
def eliminar_telefono_emergencia_ajax(request, celular_id):
    """
    Elimina un teléfono de emergencia vía AJAX.

    Args:
        request: HttpRequest object
        celular_id: ID del teléfono de emergencia

    Returns:
        JsonResponse: Resultado de la operación
    """
    if request.method == 'POST':
        try:
            celular = get_object_or_404(CelularEmergencia, id=celular_id, is_active=True)
            celular.is_active = False
            celular.save()

            return JsonResponse({'success': True, 'message': 'Teléfono de emergencia eliminado correctamente'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Método no permitido'})

# Vistas de Teléfonos - Modernas con Modal
@permission_required('user.change_user')
def gestionar_telefonos(request, usuario_id):
    """
    Vista moderna para gestionar teléfonos de un usuario.

    Utiliza modales y AJAX para una experiencia fluida sin recargas de página.
    Mantiene consistencia con el diseño del sistema.

    Args:
        request (HttpRequest): Petición HTTP
        usuario_id (int): ID del usuario cuyos teléfonos se gestionan

    Returns:
        HttpResponse: Template con interfaz moderna de gestión
        JsonResponse: Para peticiones AJAX (agregar teléfonos)

    Raises:
        Http404: Si el usuario no existe o está inactivo
        PermissionDenied: Si no tiene permisos para cambiar usuarios
    """
    usuario = get_object_or_404(User, pk=usuario_id, is_active=True)

    # Manejar AJAX para agregar teléfonos
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        numero = request.POST.get('numero', '').strip()
        tipo = request.POST.get('tipo', '').strip()

        # Validaciones básicas
        if not numero or not tipo:
            return JsonResponse({
                'success': False,
                'message': 'Número y tipo son obligatorios.'
            })

        # Validar formato del número (8 dígitos)
        if not numero.isdigit() or len(numero) != 8:
            return JsonResponse({
                'success': False,
                'message': 'El número debe contener exactamente 8 dígitos numéricos.'
            })

        # Verificar que no esté duplicado
        if CelularUsuario.objects.filter(usuario=usuario, numero=numero, is_active=True).exists():
            return JsonResponse({
                'success': False,
                'message': f'El número {numero} ya está registrado para este usuario.'
            })

        try:
            # Crear el teléfono
            celular = CelularUsuario.objects.create(
                usuario=usuario,
                numero=numero,
                tipo=tipo,
                is_active=True
            )

            return JsonResponse({
                'success': True,
                'message': 'Teléfono agregado correctamente.',
                'telefono': {
                    'id': celular.id,
                    'numero': celular.numero,
                    'tipo': celular.get_tipo_display(),
                    'tipo_value': celular.tipo
                }
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error al agregar teléfono: {str(e)}'
            })

    # Vista GET - Mostrar interfaz moderna
    context = {
        'usuario': usuario,
        'telefonos': usuario.celulares.filter(is_active=True).order_by('numero'),
        'tipos_telefono': CelularUsuario.TIPO_CHOICES
    }
    return render(request, 'user/gestionar_telefonos_moderno.html', context)

# Vistas de Familiares - Modernas con Modal
@permission_required('user.change_user')
def gestionar_familiares(request, usuario_id):
    """
    Vista moderna para gestionar familiares de un usuario.

    Utiliza modales y AJAX para una experiencia fluida sin recargas de página.
    Mantiene consistencia con el diseño del sistema.

    Args:
        request (HttpRequest): Petición HTTP
        usuario_id (int): ID del usuario cuyos familiares se gestionan

    Returns:
        HttpResponse: Template con interfaz moderna de gestión
        JsonResponse: Para peticiones AJAX (agregar familiares)

    Raises:
        Http404: Si el usuario no existe o está inactivo
        PermissionDenied: Si no tiene permisos para cambiar usuarios
    """
    usuario = get_object_or_404(User, pk=usuario_id, is_active=True)

    # Manejar AJAX para agregar familiares
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        nombre = request.POST.get('nombre', '').strip()
        parentesco = request.POST.get('parentesco', '').strip()
        telefono_emergencia = request.POST.get('telefono_emergencia', '').strip()

        # Validaciones básicas
        if not nombre or not parentesco:
            return JsonResponse({
                'success': False,
                'message': 'Nombre y parentesco son obligatorios.'
            })

        # Verificar que no esté duplicado el nombre
        if Familiar.objects.filter(usuario=usuario, nombre=nombre, is_active=True).exists():
            return JsonResponse({
                'success': False,
                'message': f'Ya existe un familiar con el nombre "{nombre}".'
            })

        # Validar teléfono si se proporciona
        if telefono_emergencia and (not telefono_emergencia.isdigit() or len(telefono_emergencia) != 8):
            return JsonResponse({
                'success': False,
                'message': 'El teléfono de emergencia debe contener exactamente 8 dígitos numéricos.'
            })

        try:
            # Obtener o crear el parentesco
            parentesco_obj, created = Parentesco.objects.get_or_create(
                parentesco=parentesco.title()
            )

            # Crear el familiar
            familiar = Familiar.objects.create(
                usuario=usuario,
                nombre=nombre,
                parentesco=parentesco_obj,
                is_active=True
            )

            # Crear teléfono de emergencia si se proporciona
            telefonos_count = 0
            if telefono_emergencia:
                CelularEmergencia.objects.create(
                    familiar=familiar,
                    numero=telefono_emergencia,
                    is_active=True
                )
                telefonos_count = 1

            return JsonResponse({
                'success': True,
                'message': 'Familiar agregado correctamente.',
                'familiar': {
                    'id': familiar.id,
                    'nombre': familiar.nombre,
                    'parentesco': str(familiar.parentesco),
                    'telefonos_count': telefonos_count
                }
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error al agregar familiar: {str(e)}'
            })

    # Vista GET - Mostrar interfaz moderna
    context = {
        'usuario': usuario,
        'familiares': usuario.familiares.filter(is_active=True).prefetch_related('celulares_emergencia').order_by('nombre'),
        'parentescos': Parentesco.objects.all().order_by('parentesco')
    }
    return render(request, 'user/gestionar_familiares_moderno.html', context)


@permission_required('user.change_user')
@require_http_methods(["GET"])
def familiar_detalles_gestion_ajax(request, usuario_id, familiar_id):
    """
    Vista AJAX para obtener detalles de un familiar en gestión de usuarios existentes.

    Similar a familiar_detalles_ajax pero adaptada para gestión de usuarios existentes.
    Obtiene el usuario desde el parámetro URL en lugar de la sesión.

    Args:
        request (HttpRequest): Petición HTTP (debe ser GET)
        usuario_id (int): ID del usuario propietario del familiar
        familiar_id (int): ID del familiar a consultar

    Returns:
        JsonResponse: Datos del familiar y sus teléfonos
    """
    try:
        # Verificar que el usuario existe y está activo
        usuario = get_object_or_404(User, pk=usuario_id, is_active=True)

        # Buscar el familiar y validar que pertenezca al usuario
        familiar = Familiar.objects.get(id=familiar_id, usuario=usuario, is_active=True)
        telefonos = CelularEmergencia.objects.filter(familiar=familiar)

        return JsonResponse({
            'success': True,
            'familiar': {
                'id': familiar.id,
                'nombre': familiar.nombre,
                'parentesco': str(familiar.parentesco),
                'is_active': familiar.is_active
            },
            'telefonos': [
                {
                    'id': tel.id,
                    'numero': tel.numero,
                    'is_active': tel.is_active
                } for tel in telefonos
            ]
        })

    except Familiar.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Familiar no encontrado o no pertenece al usuario.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error interno del servidor: {str(e)}'
        })


@permission_required('user.change_user')
@require_http_methods(["GET"])
def familiar_telefonos_gestion_ajax(request, usuario_id, familiar_id):
    """
    Vista AJAX para obtener teléfonos de un familiar en gestión de usuarios existentes.

    Similar a familiar_telefonos_ajax pero adaptada para gestión de usuarios existentes.
    Obtiene el usuario desde el parámetro URL en lugar de la sesión.

    Args:
        request (HttpRequest): Petición HTTP (debe ser GET)
        usuario_id (int): ID del usuario propietario del familiar
        familiar_id (int): ID del familiar cuyos teléfonos se consultan

    Returns:
        JsonResponse: Lista de teléfonos del familiar
    """
    try:
        # Verificar que el usuario existe y está activo
        usuario = get_object_or_404(User, pk=usuario_id, is_active=True)

        # Buscar el familiar y validar que pertenezca al usuario
        familiar = Familiar.objects.get(id=familiar_id, usuario=usuario, is_active=True)
        telefonos = CelularEmergencia.objects.filter(familiar=familiar)

        return JsonResponse({
            'success': True,
            'telefonos': [
                {
                    'id': tel.id,
                    'numero': tel.numero,
                    'is_active': tel.is_active
                } for tel in telefonos
            ]
        })

    except Familiar.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Familiar no encontrado o no pertenece al usuario.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error interno del servidor: {str(e)}'
        })


@permission_required('user.change_user')
@require_http_methods(["POST"])
def eliminar_familiar_usuario(request, usuario_id, familiar_id):
    """
    Vista AJAX para eliminar físicamente un familiar de un usuario.

    Elimina el familiar y todos sus teléfonos de emergencia asociados.
    Utilizada desde la gestión de familiares de usuario.

    Args:
        request (HttpRequest): Petición HTTP (debe ser POST con CSRF token)
        usuario_id (int): ID del usuario propietario del familiar
        familiar_id (int): ID del familiar a eliminar

    Returns:
        JsonResponse: Resultado de la operación

    Response format:
        {
            'success': bool,
            'message': str
        }

    Raises:
        JsonResponse con error si no se encuentra el familiar o hay problemas de sesión
    """
    try:
        # Verificar que el usuario existe y está activo
        usuario = get_object_or_404(User, pk=usuario_id, is_active=True)

        # Buscar el familiar y validar que pertenezca al usuario
        familiar = Familiar.objects.get(id=familiar_id, usuario=usuario, is_active=True)
        nombre_familiar = familiar.nombre

        # Eliminar físicamente el familiar (CASCADE eliminará los teléfonos)
        familiar.delete()

        return JsonResponse({
            'success': True,
            'message': f'Familiar "{nombre_familiar}" eliminado correctamente.'
        })

    except Familiar.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Familiar no encontrado o no pertenece al usuario.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al eliminar familiar: {str(e)}'
        })


# Agregar estas vistas al archivo views.py existente

# Vistas de Teléfonos
@permission_required('user.change_user')
def desactivar_telefono(request, usuario_id, telefono_id):
    """
    Elimina físicamente un teléfono de usuario.

    Args:
        request: HttpRequest object
        usuario_id: ID del usuario
        telefono_id: ID del teléfono
    """
    if request.method == 'POST':
        telefono = get_object_or_404(
            CelularUsuario,
            pk=telefono_id,
            usuario_id=usuario_id,
            is_active=True
        )
        telefono.delete()  # Eliminación física
        return JsonResponse({'success': True, 'message': 'Teléfono eliminado correctamente'})
    return JsonResponse({'error': 'Método no permitido'}, status=405)

# Vistas de Familiares
@permission_required('user.change_user')
def editar_familiar(request, usuario_id, familiar_id):
    """
    Edita la información de un familiar y sus teléfonos.

    Args:
        request: HttpRequest object
        usuario_id: ID del usuario
        familiar_id: ID del familiar
    """
    familiar = get_object_or_404(
        Familiar,
        pk=familiar_id,
        usuario_id=usuario_id,
        is_active=True
    )

    if request.method == 'POST':
        form = FamiliarForm(request.POST, instance=familiar)
        if form.is_valid():
            form.save()
            return JsonResponse({'success': True})
        return JsonResponse({'errors': form.errors}, status=400)

    form = FamiliarForm(instance=familiar)
    return render(request, 'user/editar_familiar.html', {'form': form, 'familiar': familiar})

@permission_required('user.change_user')
def desactivar_familiar(request, usuario_id, familiar_id):
    """
    Desactiva un familiar (soft delete).

    Args:
        request: HttpRequest object
        usuario_id: ID del usuario
        familiar_id: ID del familiar
    """
    if request.method == 'POST':
        familiar = get_object_or_404(
            Familiar,
            pk=familiar_id,
            usuario_id=usuario_id,
            is_active=True
        )
        familiar.is_active = False
        familiar.save()
        return JsonResponse({'success': True})
    return JsonResponse({'error': 'Método no permitido'}, status=405)

# Vistas de Cargos
@staff_required
def lista_cargos(request):
    """
    Lista los cargos disponibles con opción de búsqueda.
    """
    cargos = CargoUsuario.objects.filter(is_active=True)

    # Búsqueda
    busqueda = request.GET.get('q', '')
    if busqueda:
        cargos = cargos.filter(nombre__icontains=busqueda)

    context = {
        'cargos': cargos,
        'busqueda': busqueda
    }
    return render(request, 'user/lista_cargos.html', context)

@permission_required('user.add_cargousuario')
def crear_cargo(request):
    """
    Crea un nuevo cargo.
    """
    if request.method == 'POST':
        form = CargoUsuarioForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Cargo creado exitosamente.')
            return redirect('user:lista_cargos')
    else:
        form = CargoUsuarioForm()

    return render(request, 'user/crear_cargo.html', {'form': form})

@permission_required('user.change_cargousuario')
def editar_cargo(request, pk):
    """
    Edita un cargo existente.
    """
    cargo = get_object_or_404(CargoUsuario, pk=pk, is_active=True)

    if request.method == 'POST':
        form = CargoUsuarioForm(request.POST, instance=cargo)
        if form.is_valid():
            form.save()
            messages.success(request, 'Cargo actualizado exitosamente.')
            return redirect('user:lista_cargos')
    else:
        form = CargoUsuarioForm(instance=cargo)

    return render(request, 'user/editar_cargo.html', {'form': form, 'cargo': cargo})

@permission_required('user.delete_cargousuario')
def desactivar_cargo(request, pk):
    """
    Desactiva un cargo (soft delete).
    """
    if request.method == 'POST':
        cargo = get_object_or_404(CargoUsuario, pk=pk)
        cargo.is_active = False
        cargo.save()
        messages.success(request, 'Cargo desactivado exitosamente.')
        return JsonResponse({'success': True})
    return JsonResponse({'error': 'Método no permitido'}, status=405)

# Vistas API AJAX
@ajax_required
@staff_required
def buscar_usuarios(request):
    """
    Endpoint AJAX para búsqueda dinámica de usuarios.
    Retorna resultados en formato JSON.
    """
    query = request.GET.get('q', '')
    if len(query) < 3:
        return JsonResponse({'results': []})

    usuarios = User.objects.filter(
        Q(username__icontains=query) |
        Q(first_name__icontains=query) |
        Q(last_name__icontains=query) |
        Q(dpi__icontains=query),
        is_active=True
    )[:10]

    results = [{
        'id': user.id,
        'text': f"{user.get_full_name()} ({user.username})",
        'dpi': user.dpi
    } for user in usuarios]

    return JsonResponse({'results': results})

@ajax_required
@permission_required('user.change_user')
def cambiar_estado_usuario(request, pk):
    """
    Endpoint AJAX para cambiar el estado activo/inactivo de un usuario.
    """
    if request.method == 'POST':
        usuario = get_object_or_404(User, pk=pk)
        estado = request.POST.get('estado')

        if estado in ['true', 'false']:
            usuario.is_active = estado == 'true'
            usuario.save()
            return JsonResponse({'success': True})

        return JsonResponse({'error': 'Estado inválido'}, status=400)
    return JsonResponse({'error': 'Método no permitido'}, status=405)

@ajax_required
@staff_required
def lista_cargos_json(request):
    """
    Endpoint AJAX que retorna lista de cargos en formato JSON.
    Útil para select dinámicos en formularios.
    """
    cargos = CargoUsuario.objects.filter(is_active=True)
    data = [{
        'id': cargo.id,
        'nombre': cargo.nombre
    } for cargo in cargos]
    return JsonResponse({'cargos': data})