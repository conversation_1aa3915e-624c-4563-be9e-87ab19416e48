"""
Comando de Django para crear ciudadanos de prueba con datos aleatorios.

Uso:
    python manage.py crear_ciudadanos_prueba

Este comando crea ciudadanos de prueba con:
- Nombres y apellidos aleatorios
- DPIs únicos
- Teléfonos y emails aleatorios
- Fechas de nacimiento variadas
"""

import random
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from ciudadano.models import Ciudadano

class Command(BaseCommand):
    help = 'Crea ciudadanos de prueba con datos aleatorios'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cantidad',
            type=int,
            default=20,
            help='Cantidad de ciudadanos a crear (default: 20)'
        )

    def handle(self, *args, **options):
        cantidad = options['cantidad']
        
        # Listas de nombres aleatorios
        nombres_masculinos = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'
        ]
        
        nombres_femeninos = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>f<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>'
        ]
        
        a<PERSON><PERSON><PERSON> = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'López', 'Martínez', 'Sánchez',
            'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno',
            'Muñoz', 'Álvarez', 'Romero', 'Alonso', 'Gutiérrez', 'Navarro', 'Torres',
            'Domínguez', 'Vázquez', 'Ramos', 'Gil', 'Ramírez', 'Serrano', 'Blanco'
        ]
        
        ciudadanos_creados = 0
        ciudadanos_existentes = 0
        
        self.stdout.write(f'Creando {cantidad} ciudadanos de prueba...')
        
        for i in range(cantidad):
            # Generar datos aleatorios
            genero_num = random.choice([1, 2])  # 1=Hombre, 2=Mujer
            if genero_num == 1:
                nombre = random.choice(nombres_masculinos)
            else:
                nombre = random.choice(nombres_femeninos)
            
            apellido1 = random.choice(apellidos)
            apellido2 = random.choice(apellidos)
            nombre_completo = f"{nombre} {apellido1} {apellido2}"
            
            # Generar DPI único
            dpi = self.generar_dpi_unico()
            
            # Fecha de nacimiento aleatoria (entre 18 y 80 años)
            today = date.today()
            start_date = today - timedelta(days=80*365)
            end_date = today - timedelta(days=18*365)
            fecha_nacimiento = start_date + timedelta(
                days=random.randint(0, (end_date - start_date).days)
            )
            
            # Generar teléfono (80% de probabilidad)
            telefono = self.generar_telefono() if random.random() < 0.8 else ''
            
            # Generar email (60% de probabilidad)
            email = ''
            if random.random() < 0.6:
                email = f"{nombre.lower()}.{apellido1.lower()}@email.com"
            
            # Generar dirección (70% de probabilidad)
            direccion = ''
            if random.random() < 0.7:
                zona = random.randint(1, 21)
                calle = random.randint(1, 50)
                casa = random.randint(1, 100)
                direccion = f"Zona {zona}, {calle} calle {casa}-{random.randint(10, 99)}"
            
            try:
                # Crear el ciudadano
                ciudadano = Ciudadano.objects.create(
                    dpi=dpi,
                    nombre_completo=nombre_completo,
                    direccion=direccion,
                    telefono=telefono,
                    email=email,
                    fecha_nacimiento=fecha_nacimiento,
                    genero=genero_num
                )
                
                ciudadanos_creados += 1
                
                if ciudadanos_creados % 5 == 0:
                    self.stdout.write(f'Creados {ciudadanos_creados} ciudadanos...')
                    
            except Exception as e:
                ciudadanos_existentes += 1
                self.stdout.write(
                    self.style.WARNING(f'Error al crear ciudadano: {str(e)}')
                )
        
        # Resumen
        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Proceso completado:\n'
                f'   - Ciudadanos creados: {ciudadanos_creados}\n'
                f'   - Errores: {ciudadanos_existentes}\n'
                f'   - Total procesados: {ciudadanos_creados + ciudadanos_existentes}'
            )
        )
        
        # Estadísticas
        total_ciudadanos = Ciudadano.objects.filter(is_active=True).count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Estadísticas del sistema:\n'
                f'   - Total ciudadanos activos: {total_ciudadanos}'
            )
        )

    def generar_dpi_unico(self):
        """Genera un DPI único de 13 dígitos"""
        while True:
            # Generar DPI de 13 dígitos
            dpi = ''.join([str(random.randint(0, 9)) for _ in range(13)])
            
            # Verificar que no exista
            if not Ciudadano.objects.filter(dpi=dpi, is_active=True).exists():
                return dpi

    def generar_telefono(self):
        """Genera un número de teléfono de 8 dígitos"""
        # Primer dígito entre 2-9, resto aleatorio
        primer_digito = random.randint(2, 9)
        resto = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        return f"{primer_digito}{resto}"
