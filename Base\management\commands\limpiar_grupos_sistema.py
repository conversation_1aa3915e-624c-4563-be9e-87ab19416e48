"""
Base/management/commands/limpiar_grupos_sistema.py
Comando para limpiar y reorganizar los grupos del sistema.

Uso: python manage.py limpiar_grupos_sistema
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from user.models import User


class Command(BaseCommand):
    help = 'Limpia y reorganiza los grupos del sistema'

    def handle(self, *args, **options):
        """
        Limpia los grupos antiguos del sistema y mantiene solo las áreas de trabajo.
        """
        self.stdout.write(
            self.style.SUCCESS('Limpiando grupos del sistema...')
        )

        # Grupos del sistema que ya no necesitamos
        grupos_obsoletos = ['Admin', 'Empleado']
        
        for grupo_nombre in grupos_obsoletos:
            try:
                grupo = Group.objects.get(name=grupo_nombre)
                
                # Mover usuarios a grupos apropiados
                usuarios = grupo.user_set.all()
                for usuario in usuarios:
                    if grupo_nombre == 'Admin':
                        # Mover admins al grupo Administración
                        nuevo_grupo, _ = Group.objects.get_or_create(name='Administración')
                        usuario.groups.clear()
                        usuario.groups.add(nuevo_grupo)
                        self.stdout.write(f'  ✓ Usuario {usuario.username} movido a Administración')
                    
                    elif grupo_nombre == 'Empleado':
                        # Mover empleados a Servicios Generales por defecto
                        nuevo_grupo, _ = Group.objects.get_or_create(name='Servicios Generales')
                        usuario.groups.clear()
                        usuario.groups.add(nuevo_grupo)
                        self.stdout.write(f'  ✓ Usuario {usuario.username} movido a Servicios Generales')
                
                # Eliminar el grupo obsoleto
                grupo.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Grupo "{grupo_nombre}" eliminado')
                )
                
            except Group.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'⚠ Grupo "{grupo_nombre}" no existe')
                )

        # Mostrar resumen final
        self.stdout.write('\n' + '='*50)
        self.stdout.write('Grupos actuales en el sistema:')
        
        for grupo in Group.objects.all().order_by('name'):
            usuarios_count = grupo.user_set.filter(is_active=True).count()
            self.stdout.write(f'  • {grupo.name}: {usuarios_count} usuarios')

        self.stdout.write(
            self.style.SUCCESS('\n¡Limpieza completada exitosamente!')
        )
