{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Ticket #{{ ticket.id }} - {{ ticket.titulo }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header del ticket -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">
                                    Ticket #{{ ticket.id }}
                                    <span class="badge bg-{{ ticket.get_estado_display_color }} ms-2">
                                        {{ ticket.get_estado_display }}
                                    </span>
                                    <span class="badge bg-{{ ticket.get_prioridad_display_color }} ms-1">
                                        {{ ticket.get_prioridad_display|title }}
                                    </span>
                                </h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ ticket.creado_por.get_full_name|default:ticket.creado_por.username }}
                                    <span class="ms-3">
                                        <i class="fas fa-calendar me-1"></i>{{ ticket.fecha_creacion|date:"d/m/Y H:i" }}
                                    </span>
                                    <span class="ms-3">
                                        <i class="fas fa-building me-1"></i>{{ ticket.grupo.name }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'tickets:lista_tickets' %}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>Volver
                            </a>
                            <a href="{% url 'tickets:generar_pdf' ticket.id %}" class="btn btn-outline-success me-2" target="_blank">
                                <i class="fas fa-print me-2"></i>Imprimir Ticket
                            </a>
                            {% if puede_editar %}
                                <a href="{% url 'tickets:editar_ticket' ticket.id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>Editar
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Información principal del ticket -->
        <div class="col-lg-8">
            <!-- Detalles del ticket -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Información del Ticket
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">{{ ticket.titulo }}</h6>
                    <p class="text-muted mb-3">{{ ticket.descripcion|linebreaks }}</p>
                    
                    {% if ticket.direccion %}
                        <div class="mb-3">
                            <strong>Dirección:</strong>
                            <p class="text-muted mb-0">{{ ticket.direccion|linebreaks }}</p>
                        </div>
                    {% endif %}

                    {% if ciudadano %}
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-user me-2"></i>Ciudadano Asociado
                            </h6>
                            <p class="mb-1"><strong>{{ ciudadano.nombre_completo }}</strong></p>
                            <p class="mb-1">DPI: {{ ciudadano.dpi }}</p>
                            {% if ciudadano.telefono %}
                                <p class="mb-1">Teléfono: {{ ciudadano.telefono }}</p>
                            {% endif %}
                            {% if ciudadano.email %}
                                <p class="mb-0">Email: {{ ciudadano.email }}</p>
                            {% endif %}
                        </div>
                    {% endif %}

                    <!-- Imágenes del ticket -->
                    {% if ticket.imagenes.all %}
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fas fa-images me-2"></i>Imágenes del Ticket
                            </h6>
                            <div class="row g-3">
                                {% for imagen in ticket.imagenes.all %}
                                    {% if imagen.is_active %}
                                        <div class="col-md-4">
                                            <div class="card">
                                                <img src="{{ imagen.imagen.url }}"
                                                     class="card-img-top"
                                                     style="height: 200px; object-fit: cover; cursor: pointer;"
                                                     data-bs-toggle="modal"
                                                     data-bs-target="#modalImagen{{ imagen.id }}"
                                                     alt="{{ imagen.descripcion|default:'Imagen del ticket' }}">
                                                <div class="card-body p-2">
                                                    {% if imagen.descripcion %}
                                                        <small class="text-muted">{{ imagen.descripcion }}</small><br>
                                                    {% endif %}
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>{{ imagen.fecha_subida|date:"d/m/Y H:i" }}
                                                    </small><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-weight me-1"></i>{{ imagen.get_tamaño_legible }}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Modal para ver imagen completa -->
                                        <div class="modal fade" id="modalImagen{{ imagen.id }}" tabindex="-1">
                                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">
                                                            {{ imagen.descripcion|default:'Imagen del ticket' }}
                                                        </h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body text-center">
                                                        <img src="{{ imagen.imagen.url }}"
                                                             class="img-fluid"
                                                             alt="{{ imagen.descripcion|default:'Imagen del ticket' }}">
                                                        <div class="mt-3">
                                                            <small class="text-muted">
                                                                Subida el {{ imagen.fecha_subida|date:"d/m/Y H:i" }}
                                                                {% if imagen.subida_por %}
                                                                    por {{ imagen.subida_por.get_full_name|default:imagen.subida_por.username }}
                                                                {% endif %}
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <a href="{{ imagen.imagen.url }}"
                                                           target="_blank"
                                                           class="btn btn-primary">
                                                            <i class="fas fa-external-link-alt me-2"></i>Ver en tamaño completo
                                                        </a>
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                            Cerrar
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Historial del ticket -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historial del Ticket
                    </h5>
                </div>
                <div class="card-body">
                    {% if historial %}
                        <div class="timeline">
                            {% for registro in historial %}
                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ registro.accion }}</h6>
                                                <p class="text-muted mb-1">{{ registro.get_detalles_formateados }}</p>
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    {{ registro.usuario.get_full_name|default:registro.usuario.username|default:"Sistema" }}
                                                </small>
                                            </div>
                                            <small class="text-muted">{{ registro.fecha|date:"d/m/Y H:i" }}</small>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted mb-0">
                            <i class="fas fa-info-circle me-2"></i>No hay registros en el historial
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Panel lateral -->
        <div class="col-lg-4">
            <!-- Acciones rápidas -->
            {% if puede_cambiar_estado or puede_asignar %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Acciones Rápidas
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if puede_cambiar_estado %}
                            <button type="button" class="btn btn-outline-warning btn-sm w-100 mb-2" 
                                    data-bs-toggle="modal" data-bs-target="#modalCambiarEstado">
                                <i class="fas fa-exchange-alt me-2"></i>Cambiar Estado
                            </button>
                        {% endif %}
                        
                        {% if puede_asignar %}
                            <button type="button" class="btn btn-outline-success btn-sm w-100 mb-2" 
                                    data-bs-toggle="modal" data-bs-target="#modalAsignar">
                                <i class="fas fa-user-plus me-2"></i>Asignar Usuario
                            </button>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <!-- Asignaciones actuales -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2"></i>Asignaciones Actuales
                    </h6>
                </div>
                <div class="card-body">
                    {% if asignaciones_activas %}
                        {% for asignacion in asignaciones_activas %}
                            <div class="d-flex align-items-center justify-content-between mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fs-6">
                                            {{ asignacion.usuario.get_full_name|default:asignacion.usuario.username }}
                                        </h6>
                                        <small class="text-muted">
                                            <span class="badge bg-{{ asignacion.get_estado_display_color }}">
                                                {{ asignacion.get_estado_display }}
                                            </span>
                                        </small>
                                    </div>
                                </div>
                                {% if puede_asignar %}
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="desasignarUsuario({{ asignacion.usuario.id }}, '{{ asignacion.usuario.get_full_name|default:asignacion.usuario.username }}')"
                                            title="Desasignar">
                                        <i class="fas fa-user-minus"></i>
                                    </button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted mb-0">
                            <i class="fas fa-info-circle me-2"></i>No hay usuarios asignados
                        </p>
                    {% endif %}
                </div>
            </div>

            <!-- Información adicional -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-info me-2"></i>Información Adicional
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-primary mb-1">{{ ticket.get_tiempo_transcurrido.days }}</h6>
                                <small class="text-muted">Días transcurridos</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-info mb-1">{{ asignaciones_activas.count }}</h6>
                            <small class="text-muted">Usuario{{ asignaciones_activas.count|pluralize }} asignado{{ asignaciones_activas.count|pluralize }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Cambiar Estado -->
{% if puede_cambiar_estado %}
    <div class="modal fade" id="modalCambiarEstado" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exchange-alt me-2"></i>Cambiar Estado del Ticket
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="form-cambiar-estado">
                    {% csrf_token %}
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="nuevo_estado" class="form-label">Nuevo Estado</label>
                            <select class="form-select" id="nuevo_estado" name="estado" required>
                                <option value="">Seleccione un estado</option>
                                {% for valor, display in estados_disponibles %}
                                    <option value="{{ valor }}">{{ display }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="comentario_estado" class="form-label">Comentario (Opcional)</label>
                            <textarea class="form-control" id="comentario_estado" name="comentario" 
                                      rows="3" placeholder="Agregue un comentario sobre el cambio de estado..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>Cambiar Estado
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endif %}

<!-- Modal Asignar Usuario Mejorado -->
{% if puede_asignar %}
    <div class="modal fade" id="modalAsignar" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Asignar Usuario al Ticket #{{ ticket.id }}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="form-asignar-usuario">
                    {% csrf_token %}
                    <div class="modal-body">
                        <!-- Información del ticket -->
                        <div class="alert alert-info mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-ticket-alt fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ ticket.titulo }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>{{ ticket.grupo.name }}
                                        <span class="ms-3">
                                            <i class="fas fa-flag me-1"></i>{{ ticket.get_prioridad_display }}
                                        </span>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Buscador de usuarios -->
                        <div class="mb-3">
                            <label for="buscar-usuario-ticket" class="form-label">
                                <i class="fas fa-search me-1"></i>Buscar Usuario
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="buscar-usuario-ticket"
                                       placeholder="Buscar por nombre, email o área..." autocomplete="off">
                                <button type="button" class="btn btn-outline-secondary" id="limpiar-busqueda-ticket">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="form-text">Escriba para filtrar la lista de usuarios disponibles</div>
                        </div>

                        <!-- Lista de usuarios -->
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-users me-1"></i>Usuarios Disponibles
                                <span class="badge bg-primary ms-2" id="contador-usuarios-ticket">{{ usuarios_disponibles_asignacion|length }}</span>
                            </label>
                            <div class="border rounded" style="max-height: 300px; overflow-y: auto;" id="lista-usuarios-ticket">
                                {% for usuario in usuarios_disponibles_asignacion %}
                                    <div class="usuario-item-ticket p-3 border-bottom" data-usuario-id="{{ usuario.id }}"
                                         data-nombre="{{ usuario.get_full_name|default:usuario.username|lower }}"
                                         data-email="{{ usuario.email|lower }}"
                                         data-areas="{% for grupo in usuario.groups.all %}{{ grupo.name|lower }} {% endfor %}">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="radio" name="usuario_seleccionado_ticket"
                                                       value="{{ usuario.id }}" id="usuario_ticket_{{ usuario.id }}">
                                            </div>
                                            <div class="avatar-sm me-3">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ usuario.get_full_name|default:usuario.username }}</h6>
                                                <div class="d-flex align-items-center text-sm">
                                                    {% if usuario.email %}
                                                        <span class="text-muted me-3">
                                                            <i class="fas fa-envelope me-1"></i>{{ usuario.email }}
                                                        </span>
                                                    {% endif %}
                                                    {% if es_administrador %}
                                                        <div class="areas-usuario">
                                                            {% for grupo in usuario.groups.all %}
                                                                <span class="badge bg-secondary me-1">{{ grupo.name }}</span>
                                                            {% endfor %}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <button type="button" class="btn btn-sm btn-outline-success seleccionar-usuario-ticket"
                                                        data-usuario-id="{{ usuario.id }}"
                                                        data-usuario-nombre="{{ usuario.get_full_name|default:usuario.username }}">
                                                    <i class="fas fa-check me-1"></i>Seleccionar
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                {% empty %}
                                    <div class="text-center py-4 text-muted">
                                        <i class="fas fa-users-slash fa-2x mb-2"></i>
                                        <p>No hay usuarios disponibles para asignar</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Usuario seleccionado -->
                        <div id="usuario-seleccionado-ticket" class="alert alert-success" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="alert-heading mb-1">
                                        <i class="fas fa-user-check me-2"></i>Usuario Seleccionado
                                    </h6>
                                    <p class="mb-0" id="info-usuario-seleccionado-ticket"></p>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerSeleccionTicket()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Nota opcional -->
                        <div class="mb-3">
                            <label for="nota_asignacion" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>Nota de Asignación <span class="text-muted">(Opcional)</span>
                            </label>
                            <textarea class="form-control" id="nota_asignacion" name="nota" rows="3"
                                      placeholder="Instrucciones especiales, prioridades o comentarios para el usuario asignado..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancelar
                        </button>
                        <button type="submit" class="btn btn-success" id="btn-asignar-usuario-ticket" disabled>
                            <i class="fas fa-user-plus me-2"></i>Asignar Usuario
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endif %}

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.avatar-sm {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.fs-6 {
    font-size: 0.875rem;
}

.badge {
    font-weight: 500;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Manejar cambio de estado
    const formCambiarEstado = document.getElementById('form-cambiar-estado');
    if (formCambiarEstado) {
        formCambiarEstado.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch("{% url 'tickets:cambiar_estado' ticket.id %}", {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Estado Actualizado',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        });
    }
    
    // Manejar asignación de usuario
    const formAsignarUsuario = document.getElementById('form-asignar-usuario');
    if (formAsignarUsuario) {
        formAsignarUsuario.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch("{% url 'asignaciones:asignar_ticket' ticket.id %}", {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Usuario Asignado',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        });
    }

    // Configurar modal de asignación mejorado
    configurarModalAsignacionTicket();
});

function configurarModalAsignacionTicket() {
    const buscador = document.getElementById('buscar-usuario-ticket');
    const limpiarBtn = document.getElementById('limpiar-busqueda-ticket');
    const listaUsuarios = document.getElementById('lista-usuarios-ticket');
    const contador = document.getElementById('contador-usuarios-ticket');
    const usuarioSeleccionado = document.getElementById('usuario-seleccionado-ticket');
    const btnAsignar = document.getElementById('btn-asignar-usuario-ticket');
    const formAsignar = document.getElementById('form-asignar-usuario');

    let usuarioSeleccionadoId = null;

    // Configurar buscador
    if (buscador) {
        buscador.addEventListener('input', function() {
            const termino = this.value.toLowerCase();
            filtrarUsuariosTicket(termino);
        });
    }

    // Limpiar búsqueda
    if (limpiarBtn) {
        limpiarBtn.addEventListener('click', function() {
            buscador.value = '';
            filtrarUsuariosTicket('');
        });
    }

    // Configurar botones de selección
    document.querySelectorAll('.seleccionar-usuario-ticket').forEach(btn => {
        btn.addEventListener('click', function() {
            const usuarioId = this.dataset.usuarioId;
            const usuarioNombre = this.dataset.usuarioNombre;
            seleccionarUsuarioTicket(usuarioId, usuarioNombre);
        });
    });

    // Configurar radios
    document.querySelectorAll('input[name="usuario_seleccionado_ticket"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                const usuarioItem = this.closest('.usuario-item-ticket');
                const usuarioId = this.value;
                const usuarioNombre = usuarioItem.querySelector('h6').textContent;
                seleccionarUsuarioTicket(usuarioId, usuarioNombre);
            }
        });
    });

    // Configurar formulario
    if (formAsignar) {
        formAsignar.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!usuarioSeleccionadoId) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Usuario requerido',
                    text: 'Por favor seleccione un usuario para asignar'
                });
                return;
            }

            enviarAsignacionTicket();
        });
    }

    function filtrarUsuariosTicket(termino) {
        const items = document.querySelectorAll('.usuario-item-ticket');
        let visibles = 0;

        items.forEach(item => {
            const nombre = item.dataset.nombre || '';
            const email = item.dataset.email || '';
            const areas = item.dataset.areas || '';

            const coincide = nombre.includes(termino) ||
                           email.includes(termino) ||
                           areas.includes(termino);

            if (coincide) {
                item.style.display = 'block';
                visibles++;
            } else {
                item.style.display = 'none';
            }
        });

        contador.textContent = visibles;
    }

    function seleccionarUsuarioTicket(usuarioId, usuarioNombre) {
        usuarioSeleccionadoId = usuarioId;

        // Actualizar radio
        const radio = document.getElementById(`usuario_ticket_${usuarioId}`);
        if (radio) radio.checked = true;

        // Mostrar información del usuario seleccionado
        document.getElementById('info-usuario-seleccionado-ticket').textContent = usuarioNombre;
        usuarioSeleccionado.style.display = 'block';

        // Habilitar botón
        btnAsignar.disabled = false;

        // Scroll al usuario seleccionado
        usuarioSeleccionado.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    function enviarAsignacionTicket() {
        const formData = new FormData();
        formData.append('usuario_id', usuarioSeleccionadoId);
        formData.append('nota', document.getElementById('nota_asignacion').value);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        btnAsignar.disabled = true;
        btnAsignar.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Asignando...';

        fetch("{% url 'asignaciones:asignar_ticket' ticket.id %}", {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Usuario Asignado',
                    text: data.message,
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor'
            });
        })
        .finally(() => {
            btnAsignar.disabled = false;
            btnAsignar.innerHTML = '<i class="fas fa-user-plus me-2"></i>Asignar Usuario';
        });
    }
}

function removerSeleccionTicket() {
    usuarioSeleccionadoId = null;
    document.getElementById('usuario-seleccionado-ticket').style.display = 'none';
    document.getElementById('btn-asignar-usuario-ticket').disabled = true;

    // Limpiar radios
    document.querySelectorAll('input[name="usuario_seleccionado_ticket"]').forEach(radio => {
        radio.checked = false;
    });
}

function desasignarUsuario(usuarioId, nombreUsuario) {
    Swal.fire({
        title: '¿Desasignar usuario?',
        text: `Se desasignará a ${nombreUsuario} de este ticket`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, desasignar',
        cancelButtonText: 'Cancelar',
        input: 'textarea',
        inputPlaceholder: 'Motivo de la desasignación (opcional)...'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
            formData.append('motivo', result.value || '');
            
            fetch(`{% url 'asignaciones:desasignar_ticket' ticket.id 0 %}`.replace('0', usuarioId), {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Usuario Desasignado',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}
</script>

{% csrf_token %}
{% endblock %}
