"""
ciudadano/urls.py
URLs para la gestión de ciudadanos del sistema.

Incluye rutas para:
- Listado de ciudadanos
- Registro de ciudadanos
- Búsqueda de ciudadanos
- Historial de tickets por ciudadano
"""

from django.urls import path
from . import views

app_name = 'ciudadano'

urlpatterns = [
    # Listado y búsqueda de ciudadanos
    path('', views.lista_ciudadanos, name='lista_ciudadanos'),
    path('buscar/', views.buscar_ciudadano, name='buscar_ciudadano'),
    
    # CRUD de ciudadanos
    path('crear/', views.crear_ciudadano, name='crear_ciudadano'),
    path('<int:ciudadano_id>/', views.detalle_ciudadano, name='detalle_ciudadano'),
    path('<int:ciudadano_id>/editar/', views.editar_ciudadano, name='editar_ciudadano'),
    
    # Historial y tickets del ciudadano
    path('<int:ciudadano_id>/tickets/', views.tickets_ciudadano, name='tickets_ciudadano'),
    path('<int:ciudadano_id>/historial/', views.historial_ciudadano, name='historial_ciudadano'),
    
    # AJAX endpoints
    path('ajax/buscar-por-dpi/', views.buscar_por_dpi_ajax, name='buscar_dpi_ajax'),
    path('ajax/validar-dpi/', views.validar_dpi_ajax, name='validar_dpi_ajax'),
    path('ajax/autocompletar/', views.autocompletar_ciudadano, name='autocompletar'),
]
