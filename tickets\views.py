"""
tickets/views.py
Vistas para la gestión completa de tickets del sistema.

Implementa las reglas de negocio según el rol del usuario:
- Empleados: Solo ven tickets asignados a ellos
- Supervisores: Gestionan tickets de su área
- Secretarias: Acceso completo a todos los tickets
- Administradores: Control total del sistema

Todas las vistas incluyen:
- Decoradores de seguridad apropiados
- Validaciones de permisos por rol
- Optimizaciones de consultas con select_related/prefetch_related
- Documentación detallada
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.auth.models import Group
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.core.paginator import Paginator
from django.db.models import Q, Count, Case, When, IntegerField, Max
from django.db import models
from django.template.loader import render_to_string
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.http import HttpResponse
from datetime import datetime, timedelta
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import mm
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage
from reportlab.lib.enums import TA_CENTER
import qrcode
from io import BytesIO

# Usar el sistema centralizado de permisos
from permissions.decorators import (
    can_create_tickets_required,
    can_assign_tickets_required,
    ticket_access_required,
    admin_required
)
from permissions.utils import filter_tickets_for_user

from .models import Ticket, HistorialTicket, TicketImagen
from .forms import TicketForm, MultipleImageUploadForm
from asignaciones.models import AsignacionTicket
from ciudadano.models import Ciudadano, CiudadanoTicket
from notificaciones.models import Notificacion
from user.models import User

# ============================================================================
# FUNCIONES AUXILIARES PARA PERMISOS Y ROLES
# ============================================================================

def _es_administrador(user):
    """Verifica si el usuario es administrador."""
    return (user.cargo and user.cargo.nombre == 'Administrador') or user.is_superuser

def _es_secretaria(user):
    """Verifica si el usuario pertenece al grupo Secretaria."""
    return user.groups.filter(name='Secretaria').exists()

def _es_supervisor(user):
    """Verifica si el usuario es supervisor de su área."""
    return user.is_supervisor

def _get_area_usuario(user):
    """Obtiene el área/grupo del usuario."""
    return user.groups.first()

def _puede_gestionar_asignaciones(user):
    """Verifica si el usuario puede gestionar asignaciones."""
    return _es_administrador(user) or _es_supervisor(user)


# ============================================================================
# VISTAS PRINCIPALES DE TICKETS
# ============================================================================

@login_required
def lista_tickets(request):
    """
    Lista paginada de tickets con filtros y búsqueda según permisos del usuario.

    Reglas de visibilidad:
    - Empleado: Solo tickets asignados a él
    - Supervisor: Tickets de su área
    - Secretaria: Todos los tickets
    - Administrador: Todos los tickets

    Args:
        request: HttpRequest object con información del usuario autenticado

    Returns:
        HttpResponse: Render del template con lista de tickets filtrada
        JsonResponse: Para peticiones AJAX (scroll infinito)

    Security:
        - Requiere autenticación (@login_required)
        - Filtra tickets según permisos del usuario
    """
    user = request.user

    # Obtener queryset base según permisos del usuario
    queryset = _get_tickets_queryset_by_user(user)

    # Aplicar filtros de búsqueda
    queryset = _apply_search_filters(queryset, request.GET)

    # Aplicar filtros adicionales
    queryset = _apply_additional_filters(queryset, request.GET)

    # Optimizar consultas
    queryset = queryset.select_related(
        'creado_por', 'grupo'
    ).prefetch_related(
        'asignaciones__usuario',
        'ciudadanos__ciudadano'
    ).order_by('-fecha_creacion')

    # Paginación
    paginator = Paginator(queryset, 20)
    page = request.GET.get('page', 1)
    tickets = paginator.get_page(page)

    # Respuesta AJAX para scroll infinito
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        html = render_to_string(
            'tickets/parciales/lista_tickets.html',
            {'tickets': tickets, 'user': user},
            request=request
        )
        return JsonResponse({
            'html': html,
            'has_next': tickets.has_next()
        })

    # Contexto para template principal
    context = {
        'tickets': tickets,
        'total_tickets': queryset.count(),
        'filtros_aplicados': _get_applied_filters(request.GET),
        'areas_disponibles': _get_areas_disponibles(user),
        'puede_crear_ticket': _puede_crear_ticket(user),
        'rol_usuario': _get_rol_display(user)
    }

    return render(request, 'tickets/lista_tickets.html', context)


def _get_tickets_queryset_by_user(user):
    """
    Obtiene el queryset de tickets según los permisos del usuario.

    Args:
        user: Usuario autenticado

    Returns:
        QuerySet: Tickets filtrados según permisos del usuario
    """
    if _es_administrador(user) or _es_secretaria(user):
        # Administradores y secretarias ven todos los tickets
        return Ticket.objects.filter(is_active=True)

    elif _es_supervisor(user):
        # Supervisores ven tickets de su área
        area = _get_area_usuario(user)
        if area:
            return Ticket.objects.filter(is_active=True, grupo=area)
        else:
            return Ticket.objects.none()

    else:
        # Empleados solo ven tickets asignados a ellos
        return Ticket.objects.filter(
            is_active=True,
            asignaciones__usuario=user,
            asignaciones__is_active=True
        ).distinct()


def _apply_search_filters(queryset, get_params):
    """
    Aplica filtros de búsqueda al queryset.

    Args:
        queryset: QuerySet base
        get_params: Parámetros GET de la petición

    Returns:
        QuerySet: Queryset con filtros de búsqueda aplicados
    """
    busqueda = get_params.get('q', '').strip()
    if busqueda:
        queryset = queryset.filter(
            Q(titulo__icontains=busqueda) |
            Q(descripcion__icontains=busqueda) |
            Q(id__icontains=busqueda) |
            Q(ciudadanos__ciudadano__nombre_completo__icontains=busqueda) |
            Q(ciudadanos__ciudadano__dpi__icontains=busqueda)
        ).distinct()

    return queryset


def _apply_additional_filters(queryset, get_params):
    """
    Aplica filtros adicionales (estado, prioridad, área) al queryset.

    Args:
        queryset: QuerySet base
        get_params: Parámetros GET de la petición

    Returns:
        QuerySet: Queryset con filtros adicionales aplicados
    """
    # Filtro por estado
    estado = get_params.get('estado')
    if estado:
        try:
            queryset = queryset.filter(estado=int(estado))
        except (ValueError, TypeError):
            pass

    # Filtro por prioridad
    prioridad = get_params.get('prioridad')
    if prioridad and prioridad in ['baja', 'media', 'alta', 'critica']:
        queryset = queryset.filter(prioridad=prioridad)

    # Filtro por área
    area = get_params.get('area')
    if area:
        try:
            queryset = queryset.filter(grupo_id=int(area))
        except (ValueError, TypeError):
            pass

    return queryset


def _get_applied_filters(get_params):
    """
    Obtiene los filtros aplicados para mostrar en la interfaz.

    Args:
        get_params: Parámetros GET de la petición

    Returns:
        dict: Diccionario con filtros aplicados
    """
    return {
        'busqueda': get_params.get('q', ''),
        'estado': get_params.get('estado', ''),
        'prioridad': get_params.get('prioridad', ''),
        'area': get_params.get('area', '')
    }


def _get_areas_disponibles(user):
    """
    Obtiene las áreas disponibles para filtrar según permisos del usuario.

    Args:
        user: Usuario autenticado

    Returns:
        QuerySet: Áreas disponibles para el usuario
    """
    if _es_administrador(user) or _es_secretaria(user):
        return Group.objects.all().order_by('name')
    elif _es_supervisor(user):
        area = _get_area_usuario(user)
        return Group.objects.filter(id=area.id) if area else Group.objects.none()
    else:
        return Group.objects.none()


def _puede_crear_ticket(user):
    """
    Verifica si el usuario puede crear tickets.

    Args:
        user: Usuario autenticado

    Returns:
        bool: True si puede crear tickets, False en caso contrario
    """
    return _es_administrador(user) or _es_secretaria(user) or _es_supervisor(user)


def _get_rol_display(user):
    """
    Obtiene el nombre del rol del usuario para mostrar.

    Args:
        user: Usuario autenticado

    Returns:
        str: Nombre del rol del usuario
    """
    if _es_administrador(user):
        return 'Administrador'
    elif _es_secretaria(user):
        return 'Secretaria'
    elif _es_supervisor(user):
        return 'Supervisor'
    else:
        return 'Empleado'

def dashboard_tickets(request):
    return HttpResponse("Dashboard de tickets - En desarrollo")

@can_create_tickets_required
@csrf_protect
def crear_ticket(request):
    """
    Crea un nuevo ticket según los permisos del usuario.

    Reglas de creación:
    - Empleados: No pueden crear tickets
    - Supervisores: Pueden crear tickets para su área
    - Secretarias: Pueden crear tickets para cualquier área
    - Administradores: Control total

    Args:
        request: HttpRequest object con datos del formulario

    Returns:
        HttpResponse: Render del formulario o redirección tras crear

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Validación de permisos por rol
    """
    user = request.user

    # Verificar permisos para crear tickets
    if not _puede_crear_ticket(user):
        messages.error(request, 'No tienes permisos para crear tickets.')
        return redirect('tickets:lista_tickets')

    if request.method == 'POST':
        return _procesar_creacion_ticket(request)

    # GET: Mostrar formulario
    context = {
        'areas_disponibles': _get_areas_disponibles_creacion(user),
        'ciudadanos_recientes': _get_ciudadanos_recientes(),
        'prioridades_disponibles': _get_prioridades_disponibles(user),
        'rol_usuario': _get_rol_display(user)
    }

    return render(request, 'tickets/crear_ticket.html', context)


def _procesar_creacion_ticket(request):
    """
    Procesa la creación de un nuevo ticket.

    Args:
        request: HttpRequest object con datos del formulario

    Returns:
        HttpResponse: Redirección o render con errores
    """
    user = request.user

    # Obtener datos del formulario
    titulo = request.POST.get('titulo', '').strip()
    descripcion = request.POST.get('descripcion', '').strip()
    prioridad = request.POST.get('prioridad', 'media')
    area_id = request.POST.get('area')
    direccion = request.POST.get('direccion', '').strip()
    ciudadano_id = request.POST.get('ciudadano_id')

    # Validaciones básicas
    errores = _validar_datos_ticket(titulo, descripcion, area_id, prioridad, user)
    if errores:
        for error in errores:
            messages.error(request, error)
        return redirect('tickets:crear_ticket')

    try:
        # Obtener área
        area = get_object_or_404(Group, id=area_id)

        # Verificar permisos para asignar a esta área
        if not _puede_asignar_a_area(user, area):
            messages.error(request, 'No tienes permisos para crear tickets en esta área.')
            return redirect('tickets:crear_ticket')

        # Crear el ticket
        ticket = Ticket.objects.create(
            titulo=titulo,
            descripcion=descripcion,
            prioridad=prioridad,
            grupo=area,
            direccion=direccion,
            creado_por=user
        )

        # Asociar ciudadano si se proporcionó
        if ciudadano_id:
            try:
                ciudadano = Ciudadano.objects.get(id=ciudadano_id, is_active=True)
                CiudadanoTicket.objects.create(
                    ciudadano=ciudadano,
                    ticket=ticket
                )
            except Ciudadano.DoesNotExist:
                pass  # Continuar sin ciudadano si no existe

        # Procesar imágenes subidas
        imagenes_procesadas = _procesar_imagenes_ticket(request, ticket, user)

        # Registrar en historial
        detalles_historial = {
            'area': area.name,
            'prioridad': prioridad,
            'creado_por': user.username
        }
        if imagenes_procesadas > 0:
            detalles_historial['imagenes_subidas'] = imagenes_procesadas

        HistorialTicket.registrar_cambio(
            ticket=ticket,
            usuario=user,
            accion='Ticket creado',
            detalles=detalles_historial
        )

        # Crear notificación para supervisores del área
        _crear_notificacion_nuevo_ticket(ticket, user)

        mensaje_exito = f'Ticket #{ticket.id} creado exitosamente.'
        if imagenes_procesadas > 0:
            mensaje_exito += f' Se subieron {imagenes_procesadas} imagen(es).'

        messages.success(request, mensaje_exito)
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    except Exception as e:
        messages.error(request, f'Error al crear el ticket: {str(e)}')
        return redirect('tickets:crear_ticket')


def _get_areas_disponibles_creacion(user):
    """
    Obtiene las áreas disponibles para crear tickets según permisos del usuario.

    Args:
        user: Usuario autenticado

    Returns:
        QuerySet: Áreas disponibles para crear tickets
    """
    if _es_administrador(user) or _es_secretaria(user):
        return Group.objects.all().order_by('name')
    elif _es_supervisor(user):
        area = _get_area_usuario(user)
        return Group.objects.filter(id=area.id) if area else Group.objects.none()
    else:
        return Group.objects.none()


def _get_ciudadanos_recientes():
    """
    Obtiene los ciudadanos registrados recientemente para el formulario.

    Returns:
        QuerySet: Ciudadanos recientes activos
    """
    return Ciudadano.objects.filter(
        is_active=True
    ).order_by('-fecha_registro')[:20]


def _get_prioridades_disponibles(user):
    """
    Obtiene las prioridades disponibles según el rol del usuario.

    Args:
        user: Usuario autenticado

    Returns:
        list: Lista de tuplas (valor, display) de prioridades disponibles
    """
    if _es_administrador(user) or _es_secretaria(user):
        # Administradores y secretarias pueden usar todas las prioridades
        return [
            ('baja', 'Baja'),
            ('media', 'Media'),
            ('alta', 'Alta'),
            ('critica', 'Crítica')
        ]
    elif _es_supervisor(user):
        # Supervisores pueden usar hasta prioridad alta
        return [
            ('baja', 'Baja'),
            ('media', 'Media'),
            ('alta', 'Alta')
        ]
    else:
        # Empleados solo pueden crear con prioridad normal (aunque no deberían crear)
        return [('media', 'Media')]


def _validar_datos_ticket(titulo, descripcion, area_id, prioridad, user):
    """
    Valida los datos del formulario de creación de ticket.

    Args:
        titulo: Título del ticket
        descripcion: Descripción del ticket
        area_id: ID del área asignada
        prioridad: Prioridad del ticket
        user: Usuario que crea el ticket

    Returns:
        list: Lista de errores encontrados
    """
    errores = []

    if not titulo or len(titulo) < 5:
        errores.append('El título debe tener al menos 5 caracteres.')

    if not descripcion or len(descripcion) < 10:
        errores.append('La descripción debe tener al menos 10 caracteres.')

    if not area_id:
        errores.append('Debe seleccionar un área responsable.')

    if prioridad not in ['baja', 'media', 'alta', 'critica']:
        errores.append('Prioridad inválida.')

    # Validar que el usuario puede usar esta prioridad
    prioridades_permitidas = [p[0] for p in _get_prioridades_disponibles(user)]
    if prioridad not in prioridades_permitidas:
        errores.append('No tienes permisos para usar esta prioridad.')

    return errores


def _puede_asignar_a_area(user, area):
    """
    Verifica si el usuario puede asignar tickets a un área específica.

    Args:
        user: Usuario autenticado
        area: Área/grupo a verificar

    Returns:
        bool: True si puede asignar a esta área, False en caso contrario
    """
    if _es_administrador(user) or _es_secretaria(user):
        return True
    elif _es_supervisor(user):
        user_area = _get_area_usuario(user)
        return user_area and user_area.id == area.id
    else:
        return False


def _procesar_imagenes_ticket(request, ticket, user):
    """
    Procesa las imágenes subidas para un ticket.

    Args:
        request: HttpRequest object con archivos
        ticket: Instancia del ticket
        user: Usuario que sube las imágenes

    Returns:
        int: Número de imágenes procesadas exitosamente
    """
    imagenes_procesadas = 0

    try:
        # Obtener archivos de imagen del request
        imagenes = request.FILES.getlist('imagenes')

        if imagenes:
            # Validar número máximo de imágenes
            if len(imagenes) > 3:
                messages.warning(request, 'Solo se pueden subir máximo 3 imágenes. Se procesaron las primeras 3.')
                imagenes = imagenes[:3]

            # Procesar cada imagen
            for i, imagen in enumerate(imagenes, 1):
                try:
                    # Validar tamaño
                    if imagen.size > 5 * 1024 * 1024:  # 5MB
                        messages.warning(request, f'La imagen {imagen.name} es muy grande (máximo 5MB). Se omitió.')
                        continue

                    # Validar tipo
                    allowed_extensions = ['jpg', 'jpeg', 'png', 'webp']
                    ext = imagen.name.split('.')[-1].lower()
                    if ext not in allowed_extensions:
                        messages.warning(request, f'La imagen {imagen.name} no tiene un formato válido. Se omitió.')
                        continue

                    # Crear registro de imagen
                    ticket_imagen = TicketImagen.objects.create(
                        ticket=ticket,
                        imagen=imagen,
                        descripcion=f'Imagen {i}',
                        orden=i,
                        subida_por=user
                    )

                    imagenes_procesadas += 1

                except Exception as e:
                    messages.warning(request, f'Error al procesar la imagen {imagen.name}: {str(e)}')
                    continue

    except Exception as e:
        messages.warning(request, f'Error al procesar las imágenes: {str(e)}')

    return imagenes_procesadas


def _procesar_cambios_imagenes(request, ticket, user):
    """
    Procesa los cambios en imágenes durante la edición de un ticket.

    Maneja:
    - Eliminación de imágenes existentes
    - Adición de nuevas imágenes
    - Validación del límite máximo de 3 imágenes

    Args:
        request: HttpRequest object con datos del formulario
        ticket: Instancia del ticket
        user: Usuario que realiza los cambios

    Returns:
        dict: Diccionario con contadores de imágenes agregadas y eliminadas
    """
    resultado = {
        'agregadas': 0,
        'eliminadas': 0
    }

    try:
        # 1. Procesar eliminación de imágenes
        imagenes_a_eliminar = request.POST.getlist('eliminar_imagen')
        for imagen_id in imagenes_a_eliminar:
            try:
                imagen = TicketImagen.objects.get(
                    id=imagen_id,
                    ticket=ticket,
                    is_active=True
                )
                imagen.is_active = False
                imagen.save()
                resultado['eliminadas'] += 1
            except TicketImagen.DoesNotExist:
                continue

        # 2. Contar imágenes activas actuales después de eliminaciones
        imagenes_actuales = ticket.imagenes.filter(is_active=True).count()

        # 3. Procesar nuevas imágenes
        nuevas_imagenes = request.FILES.getlist('nuevas_imagenes')
        if nuevas_imagenes:
            # Validar que no se exceda el límite de 3 imágenes
            espacio_disponible = 3 - imagenes_actuales

            if espacio_disponible <= 0:
                messages.warning(request, 'Ya tienes el máximo de 3 imágenes. Elimina algunas para agregar nuevas.')
            else:
                # Limitar nuevas imágenes al espacio disponible
                if len(nuevas_imagenes) > espacio_disponible:
                    messages.warning(request, f'Solo se pueden agregar {espacio_disponible} imagen(es) más. Se procesaron las primeras {espacio_disponible}.')
                    nuevas_imagenes = nuevas_imagenes[:espacio_disponible]

                # Obtener el siguiente número de orden
                ultimo_orden = ticket.imagenes.filter(is_active=True).aggregate(
                    max_orden=models.Max('orden')
                )['max_orden'] or 0

                # Procesar cada nueva imagen
                for i, imagen in enumerate(nuevas_imagenes, 1):
                    try:
                        # Validar tamaño
                        if imagen.size > 5 * 1024 * 1024:  # 5MB
                            messages.warning(request, f'La imagen {imagen.name} es muy grande (máximo 5MB). Se omitió.')
                            continue

                        # Validar tipo
                        allowed_extensions = ['jpg', 'jpeg', 'png', 'webp']
                        ext = imagen.name.split('.')[-1].lower()
                        if ext not in allowed_extensions:
                            messages.warning(request, f'La imagen {imagen.name} no tiene un formato válido. Se omitió.')
                            continue

                        # Crear registro de imagen
                        TicketImagen.objects.create(
                            ticket=ticket,
                            imagen=imagen,
                            descripcion=f'Imagen {ultimo_orden + i}',
                            orden=ultimo_orden + i,
                            subida_por=user
                        )

                        resultado['agregadas'] += 1

                    except Exception as e:
                        messages.warning(request, f'Error al procesar la imagen {imagen.name}: {str(e)}')
                        continue

    except Exception as e:
        messages.warning(request, f'Error al procesar cambios en imágenes: {str(e)}')

    return resultado


def _crear_notificacion_nuevo_ticket(ticket, creado_por):
    """
    Crea notificación para supervisores del área cuando se crea un nuevo ticket.

    Args:
        ticket: Instancia del ticket creado
        creado_por: Usuario que creó el ticket
    """
    try:
        # Crear notificación
        notificacion = Notificacion.objects.create(
            mensaje=f'Nuevo ticket #{ticket.id}: {ticket.titulo}',
            tipo='ticket',
            ticket=ticket,
            titulo='Nuevo Ticket Asignado',
            creado_por=creado_por
        )

        # Enviar a supervisores del área
        supervisores = User.objects.filter(
            groups=ticket.grupo,
            is_supervisor=True,
            is_active=True
        )

        for supervisor in supervisores:
            notificacion.enviar_a_usuario(supervisor)

    except Exception as e:
        # Log error pero no interrumpir el flujo
        print(f"Error al crear notificación: {e}")


def _crear_notificacion_cambio_estado(ticket, usuario, estado_anterior, estado_nuevo):
    """
    Crea notificación cuando se cambia el estado de un ticket.

    Args:
        ticket: Instancia del ticket
        usuario: Usuario que cambió el estado
        estado_anterior: Estado anterior del ticket
        estado_nuevo: Nuevo estado del ticket
    """
    try:
        # Crear notificación
        notificacion = Notificacion.objects.create(
            mensaje=f'Ticket #{ticket.id} cambió de {estado_anterior} a {estado_nuevo}',
            tipo='ticket',
            ticket=ticket,
            titulo='Estado de Ticket Actualizado',
            creado_por=usuario
        )

        # Enviar a usuarios relevantes
        usuarios_notificar = set()

        # Notificar al creador del ticket
        if ticket.creado_por != usuario:
            usuarios_notificar.add(ticket.creado_por)

        # Notificar a usuarios asignados
        for asignacion in ticket.asignaciones.filter(is_active=True):
            if asignacion.usuario != usuario:
                usuarios_notificar.add(asignacion.usuario)

        # Notificar a supervisores del área
        supervisores = User.objects.filter(
            groups=ticket.grupo,
            is_supervisor=True,
            is_active=True
        ).exclude(id=usuario.id)

        for supervisor in supervisores:
            usuarios_notificar.add(supervisor)

        # Enviar notificaciones
        for usuario_notificar in usuarios_notificar:
            notificacion.enviar_a_usuario(usuario_notificar)

    except Exception as e:
        print(f"Error al crear notificación de cambio de estado: {e}")


def _crear_notificacion_asignacion(ticket, asignado_por, usuario_asignado):
    """
    Crea notificación cuando se asigna un ticket a un usuario.

    Args:
        ticket: Instancia del ticket
        asignado_por: Usuario que realizó la asignación
        usuario_asignado: Usuario al que se asignó el ticket
    """
    try:
        # Crear notificación
        notificacion = Notificacion.objects.create(
            mensaje=f'Te han asignado el ticket #{ticket.id}: {ticket.titulo}',
            tipo='ticket',
            ticket=ticket,
            titulo='Nuevo Ticket Asignado',
            creado_por=asignado_por
        )

        # Enviar al usuario asignado
        notificacion.enviar_a_usuario(usuario_asignado)

        # También notificar al creador del ticket si es diferente
        if ticket.creado_por != asignado_por and ticket.creado_por != usuario_asignado:
            notificacion_creador = Notificacion.objects.create(
                mensaje=f'Ticket #{ticket.id} ha sido asignado a {usuario_asignado.get_full_name() or usuario_asignado.username}',
                tipo='ticket',
                ticket=ticket,
                titulo='Ticket Asignado',
                creado_por=asignado_por
            )
            notificacion_creador.enviar_a_usuario(ticket.creado_por)

    except Exception as e:
        print(f"Error al crear notificación de asignación: {e}")


def _crear_notificacion_desasignacion(ticket, desasignado_por, usuario_desasignado, motivo):
    """
    Crea notificación cuando se desasigna un ticket de un usuario.

    Args:
        ticket: Instancia del ticket
        desasignado_por: Usuario que realizó la desasignación
        usuario_desasignado: Usuario del que se desasignó el ticket
        motivo: Motivo de la desasignación
    """
    try:
        # Crear notificación
        mensaje = f'Has sido desasignado del ticket #{ticket.id}: {ticket.titulo}'
        if motivo:
            mensaje += f' - Motivo: {motivo}'

        notificacion = Notificacion.objects.create(
            mensaje=mensaje,
            tipo='warning',
            ticket=ticket,
            titulo='Ticket Desasignado',
            creado_por=desasignado_por
        )

        # Enviar al usuario desasignado
        notificacion.enviar_a_usuario(usuario_desasignado)

    except Exception as e:
        print(f"Error al crear notificación de desasignación: {e}")

@ticket_access_required
def detalle_ticket(request, ticket_id):
    """
    Muestra el detalle completo de un ticket según permisos del usuario.

    Incluye:
    - Información básica del ticket
    - Historial de cambios
    - Asignaciones actuales
    - Ciudadano asociado
    - Acciones disponibles según rol

    Args:
        request: HttpRequest object
        ticket_id: ID del ticket a mostrar

    Returns:
        HttpResponse: Render del template con detalles del ticket
        HttpResponseForbidden: Si no tiene permisos para ver el ticket

    Security:
        - Requiere autenticación (@login_required)
        - Verifica permisos para ver el ticket específico
    """
    user = request.user

    # Obtener ticket con datos relacionados optimizados
    ticket = get_object_or_404(
        Ticket.objects.select_related(
            'creado_por', 'grupo'
        ).prefetch_related(
            'asignaciones__usuario',
            'ciudadanos__ciudadano',
            'historial__usuario',
            'imagenes__subida_por'
        ),
        id=ticket_id,
        is_active=True
    )

    # Verificar permisos para ver este ticket
    if not _puede_ver_ticket(user, ticket):
        messages.error(request, 'No tienes permisos para ver este ticket.')
        return redirect('tickets:lista_tickets')

    # Obtener información adicional
    asignaciones_activas = ticket.asignaciones.filter(is_active=True)
    ciudadano = ticket.get_ciudadano()
    historial = ticket.historial.all()[:20]  # Últimos 20 registros

    # Determinar acciones disponibles para el usuario
    acciones_disponibles = _get_acciones_disponibles(user, ticket)

    context = {
        'ticket': ticket,
        'asignaciones_activas': asignaciones_activas,
        'ciudadano': ciudadano,
        'historial': historial,
        'acciones_disponibles': acciones_disponibles,
        'puede_editar': _puede_editar_ticket(user, ticket),
        'puede_asignar': _puede_asignar_ticket(user, ticket),
        'puede_cambiar_estado': _puede_cambiar_estado(user, ticket),
        'usuarios_disponibles_asignacion': _get_usuarios_disponibles_asignacion(user, ticket),
        'estados_disponibles': _get_estados_disponibles(user, ticket),
        'rol_usuario': _get_rol_display(user)
    }

    return render(request, 'tickets/detalle_ticket.html', context)


def _puede_ver_ticket(user, ticket):
    """
    Verifica si el usuario puede ver un ticket específico.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        bool: True si puede ver el ticket, False en caso contrario
    """
    if _es_administrador(user) or _es_secretaria(user):
        return True
    elif _es_supervisor(user):
        user_area = _get_area_usuario(user)
        return user_area and user_area.id == ticket.grupo.id
    else:
        # Empleado: solo si está asignado al ticket
        return ticket.asignaciones.filter(usuario=user, is_active=True).exists()


def _puede_editar_ticket(user, ticket):
    """
    Verifica si el usuario puede editar un ticket específico.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        bool: True si puede editar el ticket, False en caso contrario
    """
    if _es_administrador(user):
        return True
    elif _es_secretaria(user):
        # Secretaria puede editar tickets que creó
        return ticket.creado_por == user
    elif _es_supervisor(user):
        user_area = _get_area_usuario(user)
        return user_area and user_area.id == ticket.grupo.id
    else:
        return False


def _puede_asignar_ticket(user, ticket):
    """
    Verifica si el usuario puede asignar un ticket específico.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        bool: True si puede asignar el ticket, False en caso contrario
    """
    if _es_administrador(user):
        return True
    elif _es_supervisor(user):
        user_area = _get_area_usuario(user)
        return user_area and user_area.id == ticket.grupo.id
    else:
        return False


def _puede_cambiar_estado(user, ticket):
    """
    Verifica si el usuario puede cambiar el estado de un ticket específico.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        bool: True si puede cambiar el estado, False en caso contrario
    """
    if _es_administrador(user):
        return True
    elif _es_supervisor(user):
        user_area = _get_area_usuario(user)
        return user_area and user_area.id == ticket.grupo.id
    else:
        # Empleado: solo si está asignado al ticket
        return ticket.asignaciones.filter(usuario=user, is_active=True).exists()


def _get_acciones_disponibles(user, ticket):
    """
    Obtiene las acciones disponibles para el usuario en un ticket específico.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        dict: Diccionario con acciones disponibles
    """
    return {
        'puede_editar': _puede_editar_ticket(user, ticket),
        'puede_asignar': _puede_asignar_ticket(user, ticket),
        'puede_cambiar_estado': _puede_cambiar_estado(user, ticket),
        'puede_comentar': _puede_ver_ticket(user, ticket),  # Si puede ver, puede comentar
        'puede_finalizar': _puede_cambiar_estado(user, ticket) and ticket.estado != 3
    }


def _get_usuarios_disponibles_asignacion(user, ticket):
    """
    Obtiene los usuarios disponibles para asignar al ticket.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        QuerySet: Usuarios disponibles para asignación
    """
    if not _puede_asignar_ticket(user, ticket):
        return User.objects.none()

    if _es_administrador(user):
        # Administrador puede asignar a cualquier usuario activo
        return User.objects.filter(is_active=True).order_by('first_name', 'last_name')
    else:
        # Supervisor puede asignar solo a usuarios de su área
        return User.objects.filter(
            groups=ticket.grupo,
            is_active=True
        ).order_by('first_name', 'last_name')


def _get_estados_disponibles(user, ticket):
    """
    Obtiene los estados disponibles para cambiar según el usuario y ticket.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        list: Lista de tuplas (valor, display) de estados disponibles
    """
    if not _puede_cambiar_estado(user, ticket):
        return []

    estados_base = [
        (1, 'Abierto'),
        (2, 'En Progreso'),
        (3, 'Cerrado'),
        (4, 'Pendiente')
    ]

    # Filtrar estados según el estado actual y rol
    if _es_administrador(user) or _es_secretaria(user) or _es_supervisor(user):
        # Pueden cambiar a cualquier estado
        return estados_base
    else:
        # Empleados tienen transiciones limitadas
        estado_actual = ticket.estado
        if estado_actual == 1:  # Abierto -> En Progreso
            return [(2, 'En Progreso')]
        elif estado_actual == 2:  # En Progreso -> Cerrado o Pendiente
            return [(3, 'Cerrado'), (4, 'Pendiente')]
        elif estado_actual == 4:  # Pendiente -> En Progreso
            return [(2, 'En Progreso')]
        else:
            return []

@login_required
@csrf_protect
def editar_ticket(request, ticket_id):
    """
    Edita un ticket existente según permisos del usuario.

    Reglas de edición:
    - Administradores: Pueden editar cualquier ticket
    - Secretarias: Solo tickets que crearon
    - Supervisores: Tickets de su área
    - Empleados: No pueden editar tickets

    Args:
        request: HttpRequest object con datos del formulario
        ticket_id: ID del ticket a editar

    Returns:
        HttpResponse: Render del formulario o redirección tras editar

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Validación de permisos por rol
    """
    user = request.user

    # Obtener ticket con imágenes
    ticket = get_object_or_404(
        Ticket.objects.select_related('creado_por', 'grupo').prefetch_related('imagenes'),
        id=ticket_id,
        is_active=True
    )

    # Verificar permisos
    if not _puede_editar_ticket(user, ticket):
        messages.error(request, 'No tienes permisos para editar este ticket.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    if request.method == 'POST':
        return _procesar_edicion_ticket(request, ticket)

    # GET: Mostrar formulario con datos actuales
    context = {
        'ticket': ticket,
        'areas_disponibles': _get_areas_disponibles_edicion(user, ticket),
        'prioridades_disponibles': _get_prioridades_disponibles(user),
        'ciudadano_actual': ticket.get_ciudadano(),
        'ciudadanos_recientes': _get_ciudadanos_recientes(),
        'imagenes_actuales': ticket.imagenes.filter(is_active=True).order_by('orden', 'fecha_subida'),
        'puede_agregar_imagenes': ticket.imagenes.filter(is_active=True).count() < 3,
        'rol_usuario': _get_rol_display(user),
        'editando': True
    }

    return render(request, 'tickets/editar_ticket.html', context)


def _procesar_edicion_ticket(request, ticket):
    """
    Procesa la edición de un ticket existente.

    Args:
        request: HttpRequest object con datos del formulario
        ticket: Instancia del ticket a editar

    Returns:
        HttpResponse: Redirección o render con errores
    """
    user = request.user

    # Obtener datos del formulario
    titulo = request.POST.get('titulo', '').strip()
    descripcion = request.POST.get('descripcion', '').strip()
    prioridad = request.POST.get('prioridad', ticket.prioridad)
    area_id = request.POST.get('area', ticket.grupo.id)
    direccion = request.POST.get('direccion', '').strip()
    ciudadano_id = request.POST.get('ciudadano_id')

    # Validaciones básicas
    errores = _validar_datos_ticket(titulo, descripcion, area_id, prioridad, user)
    if errores:
        for error in errores:
            messages.error(request, error)
        return redirect('tickets:editar_ticket', ticket_id=ticket.id)

    try:
        # Obtener área
        area = get_object_or_404(Group, id=area_id)

        # Verificar permisos para asignar a esta área
        if not _puede_asignar_a_area(user, area):
            messages.error(request, 'No tienes permisos para asignar tickets a esta área.')
            return redirect('tickets:editar_ticket', ticket_id=ticket.id)

        # Guardar valores anteriores para el historial
        valores_anteriores = {
            'titulo': ticket.titulo,
            'descripcion': ticket.descripcion,
            'prioridad': ticket.prioridad,
            'area': ticket.grupo.name,
            'direccion': ticket.direccion
        }

        # Verificar si cambió el área
        area_cambio = valores_anteriores['area'] != area.name

        # Actualizar el ticket
        ticket.titulo = titulo
        ticket.descripcion = descripcion
        ticket.prioridad = prioridad
        ticket.grupo = area
        ticket.direccion = direccion
        ticket.save()

        # Si cambió el área, desactivar asignaciones actuales
        if area_cambio:
            _manejar_cambio_area_ticket(ticket, user, valores_anteriores['area'], area.name)

        # Actualizar ciudadano asociado
        _actualizar_ciudadano_ticket(ticket, ciudadano_id)

        # Procesar cambios en imágenes
        imagenes_procesadas = _procesar_cambios_imagenes(request, ticket, user)

        # Registrar cambios en historial
        cambios = []
        if valores_anteriores['titulo'] != titulo:
            cambios.append(f"Título: '{valores_anteriores['titulo']}' → '{titulo}'")
        if valores_anteriores['descripcion'] != descripcion:
            cambios.append(f"Descripción actualizada")
        if valores_anteriores['prioridad'] != prioridad:
            cambios.append(f"Prioridad: {valores_anteriores['prioridad']} → {prioridad}")
        if valores_anteriores['area'] != area.name:
            cambios.append(f"Área: {valores_anteriores['area']} → {area.name}")
        if valores_anteriores['direccion'] != direccion:
            cambios.append(f"Dirección actualizada")

        # Agregar cambios de imágenes al historial
        if imagenes_procesadas['agregadas'] > 0:
            cambios.append(f"Se agregaron {imagenes_procesadas['agregadas']} imagen(es)")
        if imagenes_procesadas['eliminadas'] > 0:
            cambios.append(f"Se eliminaron {imagenes_procesadas['eliminadas']} imagen(es)")

        if cambios:
            HistorialTicket.registrar_cambio(
                ticket=ticket,
                usuario=user,
                accion='Ticket editado',
                detalles={
                    'cambios': cambios,
                    'editado_por': user.username
                }
            )

            # Crear notificación si hay cambios significativos
            if len(cambios) > 0:
                _crear_notificacion_edicion_ticket(ticket, user, cambios)

        mensaje_exito = f'Ticket #{ticket.id} editado exitosamente.'
        if imagenes_procesadas['agregadas'] > 0 or imagenes_procesadas['eliminadas'] > 0:
            mensaje_exito += f' Imágenes: +{imagenes_procesadas["agregadas"]} -{imagenes_procesadas["eliminadas"]}'

        messages.success(request, mensaje_exito)
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    except Exception as e:
        messages.error(request, f'Error al editar el ticket: {str(e)}')
        return redirect('tickets:editar_ticket', ticket_id=ticket.id)


def _manejar_cambio_area_ticket(ticket, usuario, area_anterior, area_nueva):
    """
    Maneja el cambio de área de un ticket, desactivando asignaciones actuales.

    Args:
        ticket: Instancia del ticket
        usuario: Usuario que realizó el cambio
        area_anterior: Nombre del área anterior
        area_nueva: Nombre del área nueva
    """
    # Desactivar todas las asignaciones actuales
    asignaciones_desactivadas = ticket.asignaciones.filter(is_active=True).update(
        is_active=False,
        fecha_finalizacion=timezone.now()
    )

    # Registrar en historial
    if asignaciones_desactivadas > 0:
        HistorialTicket.registrar_cambio(
            ticket=ticket,
            usuario=usuario,
            accion='Asignaciones desactivadas por cambio de área',
            detalles={
                'area_anterior': area_anterior,
                'area_nueva': area_nueva,
                'asignaciones_desactivadas': asignaciones_desactivadas,
                'motivo': 'Cambio de área del ticket'
            }
        )


def _get_areas_disponibles_edicion(user, ticket):
    """
    Obtiene las áreas disponibles para editar un ticket según permisos del usuario.

    Args:
        user: Usuario autenticado
        ticket: Ticket que se está editando

    Returns:
        QuerySet: Áreas disponibles para editar
    """
    if _es_administrador(user):
        return Group.objects.all().order_by('name')
    elif _es_secretaria(user):
        # Secretaria puede cambiar a cualquier área
        return Group.objects.all().order_by('name')
    elif _es_supervisor(user):
        # Supervisor solo puede mantener en su área
        user_area = _get_area_usuario(user)
        return Group.objects.filter(id=user_area.id) if user_area else Group.objects.none()
    else:
        return Group.objects.none()


def _actualizar_ciudadano_ticket(ticket, ciudadano_id):
    """
    Actualiza el ciudadano asociado al ticket.

    Args:
        ticket: Instancia del ticket
        ciudadano_id: ID del nuevo ciudadano o None para remover
    """
    from ciudadano.models import CiudadanoTicket

    # Remover asociación actual
    CiudadanoTicket.objects.filter(ticket=ticket).delete()

    # Agregar nueva asociación si se proporcionó
    if ciudadano_id:
        try:
            ciudadano = Ciudadano.objects.get(id=ciudadano_id, is_active=True)
            CiudadanoTicket.objects.create(
                ciudadano=ciudadano,
                ticket=ticket
            )
        except Ciudadano.DoesNotExist:
            pass  # Continuar sin ciudadano si no existe


def _crear_notificacion_edicion_ticket(ticket, usuario, cambios):
    """
    Crea notificación cuando se edita un ticket.

    Args:
        ticket: Instancia del ticket editado
        usuario: Usuario que editó el ticket
        cambios: Lista de cambios realizados
    """
    try:
        # Crear notificación
        mensaje_cambios = ', '.join(cambios[:3])  # Mostrar máximo 3 cambios
        if len(cambios) > 3:
            mensaje_cambios += f' y {len(cambios) - 3} cambio(s) más'

        notificacion = Notificacion.objects.create(
            mensaje=f'Ticket #{ticket.id} editado: {mensaje_cambios}',
            tipo='info',
            ticket=ticket,
            titulo='Ticket Editado',
            creado_por=usuario
        )

        # Enviar a usuarios relevantes (excluyendo quien editó)
        usuarios_notificar = set()

        # Notificar al creador del ticket
        if ticket.creado_por != usuario:
            usuarios_notificar.add(ticket.creado_por)

        # Notificar a usuarios asignados
        for asignacion in ticket.asignaciones.filter(is_active=True):
            if asignacion.usuario != usuario:
                usuarios_notificar.add(asignacion.usuario)

        # Enviar notificaciones
        for usuario_notificar in usuarios_notificar:
            notificacion.enviar_a_usuario(usuario_notificar)

    except Exception as e:
        print(f"Error al crear notificación de edición: {e}")

@login_required
@csrf_protect
@require_http_methods(["POST"])
def cambiar_estado_ticket(request, ticket_id):
    """
    Cambia el estado de un ticket según permisos del usuario.

    Reglas de cambio de estado:
    - Empleados: Transiciones limitadas en tickets asignados
    - Supervisores: Control total en tickets de su área
    - Secretarias/Administradores: Control total

    Args:
        request: HttpRequest object con datos del formulario
        ticket_id: ID del ticket a modificar

    Returns:
        JsonResponse: Resultado de la operación para AJAX
        HttpResponse: Redirección para peticiones normales

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo métodos POST (@require_http_methods)
        - Validación de permisos por rol
    """
    user = request.user

    # Obtener ticket
    ticket = get_object_or_404(
        Ticket.objects.select_related('creado_por', 'grupo'),
        id=ticket_id,
        is_active=True
    )

    # Verificar permisos
    if not _puede_cambiar_estado(user, ticket):
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'No tienes permisos para cambiar el estado de este ticket.'
            })
        messages.error(request, 'No tienes permisos para cambiar el estado de este ticket.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    # Obtener nuevo estado
    nuevo_estado = request.POST.get('estado')
    comentario = request.POST.get('comentario', '').strip()

    try:
        nuevo_estado = int(nuevo_estado)
    except (ValueError, TypeError):
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Estado inválido.'
            })
        messages.error(request, 'Estado inválido.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    # Validar que el estado es válido
    estados_validos = [choice[0] for choice in Ticket.ESTADO_CHOICES]
    if nuevo_estado not in estados_validos:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Estado no válido.'
            })
        messages.error(request, 'Estado no válido.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    # Verificar que el usuario puede cambiar a este estado específico
    estados_disponibles = _get_estados_disponibles(user, ticket)
    estados_permitidos = [estado[0] for estado in estados_disponibles]

    if nuevo_estado not in estados_permitidos:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'No tienes permisos para cambiar a este estado.'
            })
        messages.error(request, 'No tienes permisos para cambiar a este estado.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket.id)

    # Realizar el cambio de estado
    estado_anterior = ticket.estado
    estado_anterior_display = ticket.get_estado_display()

    ticket.estado = nuevo_estado

    # Si se cierra el ticket, establecer fecha de finalización
    if nuevo_estado == 3:  # Cerrado
        ticket.fecha_finalizacion = timezone.now()

    ticket.save()

    # Registrar en historial
    nuevo_estado_display = dict(Ticket.ESTADO_CHOICES)[nuevo_estado]
    detalles = {
        'estado_anterior': estado_anterior,
        'estado_nuevo': nuevo_estado,
        'estado_anterior_display': estado_anterior_display,
        'estado_nuevo_display': nuevo_estado_display
    }

    if comentario:
        detalles['comentario'] = comentario

    HistorialTicket.registrar_cambio(
        ticket=ticket,
        usuario=user,
        accion=f'Estado cambiado de {estado_anterior_display} a {nuevo_estado_display}',
        detalles=detalles
    )

    # Crear notificaciones
    _crear_notificacion_cambio_estado(ticket, user, estado_anterior_display, nuevo_estado_display)

    # Respuesta
    mensaje = f'Estado del ticket #{ticket.id} cambiado a {nuevo_estado_display}'
    if comentario:
        mensaje += f' con comentario: "{comentario}"'

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': mensaje,
            'nuevo_estado': nuevo_estado,
            'nuevo_estado_display': nuevo_estado_display,
            'nuevo_estado_color': ticket.get_estado_display_color()
        })

    messages.success(request, mensaje)
    return redirect('tickets:detalle_ticket', ticket_id=ticket.id)






@login_required
@require_http_methods(["GET"])
def filtrar_tickets_ajax(request):
    """
    Filtrado AJAX de tickets con múltiples criterios.

    Permite filtrar tickets por estado, prioridad, área, usuario asignado
    y devuelve resultados parciales para actualizar la lista sin recargar.

    Args:
        request: HttpRequest object con parámetros de filtro

    Returns:
        JsonResponse: HTML parcial con tickets filtrados

    Security:
        - Requiere autenticación (@login_required)
        - Solo métodos GET (@require_http_methods)
        - Filtros por permisos de usuario
    """
    user = request.user

    # Obtener parámetros de filtro
    estado = request.GET.get('estado')
    prioridad = request.GET.get('prioridad')
    area_id = request.GET.get('area')
    usuario_asignado_id = request.GET.get('usuario_asignado')
    fecha_desde = request.GET.get('fecha_desde')
    fecha_hasta = request.GET.get('fecha_hasta')

    # Query base según permisos del usuario
    tickets_queryset = _get_tickets_queryset_by_user(user)

    # Aplicar filtros
    if estado:
        tickets_queryset = tickets_queryset.filter(estado=estado)

    if prioridad:
        tickets_queryset = tickets_queryset.filter(prioridad=prioridad)

    if area_id and _es_administrador(user):
        tickets_queryset = tickets_queryset.filter(grupo_id=area_id)

    if usuario_asignado_id:
        tickets_queryset = tickets_queryset.filter(
            asignaciones__usuario_id=usuario_asignado_id,
            asignaciones__is_active=True
        )

    if fecha_desde:
        try:
            fecha_desde_obj = datetime.strptime(fecha_desde, '%Y-%m-%d').date()
            tickets_queryset = tickets_queryset.filter(fecha_creacion__date__gte=fecha_desde_obj)
        except ValueError:
            pass

    if fecha_hasta:
        try:
            fecha_hasta_obj = datetime.strptime(fecha_hasta, '%Y-%m-%d').date()
            tickets_queryset = tickets_queryset.filter(fecha_creacion__date__lte=fecha_hasta_obj)
        except ValueError:
            pass

    # Ordenar y paginar
    tickets = tickets_queryset.order_by('-fecha_creacion')[:20]

    # Renderizar template parcial
    html = render_to_string(
        'tickets/parciales/lista_tickets.html',
        {'tickets': tickets, 'user': user},
        request=request
    )

    return JsonResponse({
        'success': True,
        'html': html,
        'total': tickets_queryset.count(),
        'filtros_aplicados': {
            'estado': estado,
            'prioridad': prioridad,
            'area_id': area_id,
            'usuario_asignado_id': usuario_asignado_id,
            'fecha_desde': fecha_desde,
            'fecha_hasta': fecha_hasta
        }
    })

@login_required
@require_http_methods(["GET"])
def buscar_tickets_ajax(request):
    """
    Búsqueda AJAX de tickets con autocompletado.

    Permite búsqueda en tiempo real por título, descripción, ID de ticket,
    nombre de ciudadano o DPI con resultados instantáneos.

    Args:
        request: HttpRequest object con parámetro 'q' para búsqueda

    Returns:
        JsonResponse: Lista de tickets encontrados con información básica

    Security:
        - Requiere autenticación (@login_required)
        - Solo métodos GET (@require_http_methods)
        - Filtros por permisos de usuario
    """
    user = request.user

    # Obtener término de búsqueda
    termino = request.GET.get('q', '').strip()

    if len(termino) < 2:
        return JsonResponse({
            'success': True,
            'tickets': [],
            'mensaje': 'Ingrese al menos 2 caracteres para buscar'
        })

    # Query base según permisos del usuario
    tickets_queryset = _get_tickets_queryset_by_user(user)

    # Búsqueda por múltiples campos
    tickets = tickets_queryset.filter(
        Q(titulo__icontains=termino) |
        Q(descripcion__icontains=termino) |
        Q(id__icontains=termino) |
        Q(ciudadanos__ciudadano__nombre_completo__icontains=termino) |
        Q(ciudadanos__ciudadano__dpi__icontains=termino)
    ).distinct().order_by('-fecha_creacion')[:15]  # Máximo 15 resultados

    # Formatear resultados para JSON
    resultados = []
    for ticket in tickets:
        # Obtener ciudadano principal si existe
        ciudadano_principal = ticket.ciudadanos.filter(is_active=True).first()
        ciudadano_info = None
        if ciudadano_principal:
            ciudadano_info = {
                'nombre': ciudadano_principal.ciudadano.nombre_completo,
                'dpi': ciudadano_principal.ciudadano.dpi
            }

        # Contar asignaciones activas
        asignaciones_count = ticket.asignaciones.filter(is_active=True).count()

        resultados.append({
            'id': ticket.id,
            'titulo': ticket.titulo,
            'descripcion': ticket.descripcion[:100] + '...' if len(ticket.descripcion) > 100 else ticket.descripcion,
            'estado': {
                'valor': ticket.estado,
                'display': ticket.get_estado_display(),
                'color': ticket.get_estado_display_color()
            },
            'prioridad': {
                'valor': ticket.prioridad,
                'display': ticket.get_prioridad_display(),
                'color': ticket.get_prioridad_display_color()
            },
            'area': ticket.grupo.name,
            'fecha_creacion': ticket.fecha_creacion.strftime('%d/%m/%Y %H:%M'),
            'ciudadano': ciudadano_info,
            'asignaciones_count': asignaciones_count,
            'url': f'/tickets/{ticket.id}/'
        })

    return JsonResponse({
        'success': True,
        'tickets': resultados,
        'total': len(resultados),
        'termino': termino
    })

@login_required
@require_http_methods(["GET"])
def cargar_mas_tickets(request):
    """
    Carga más tickets para scroll infinito.

    Implementa paginación AJAX para cargar tickets adicionales
    sin recargar la página completa.

    Args:
        request: HttpRequest object con parámetros de paginación

    Returns:
        JsonResponse: HTML parcial con más tickets

    Security:
        - Requiere autenticación (@login_required)
        - Solo métodos GET (@require_http_methods)
        - Filtros por permisos de usuario
    """
    user = request.user

    # Obtener parámetros de paginación
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 20))

    # Obtener filtros aplicados (si los hay)
    estado = request.GET.get('estado')
    prioridad = request.GET.get('prioridad')
    area_id = request.GET.get('area')
    busqueda = request.GET.get('q', '').strip()

    # Query base según permisos del usuario
    tickets_queryset = _get_tickets_queryset_by_user(user)

    # Aplicar filtros si existen
    if estado:
        tickets_queryset = tickets_queryset.filter(estado=estado)

    if prioridad:
        tickets_queryset = tickets_queryset.filter(prioridad=prioridad)

    if area_id and _es_administrador(user):
        tickets_queryset = tickets_queryset.filter(grupo_id=area_id)

    if busqueda:
        tickets_queryset = tickets_queryset.filter(
            Q(titulo__icontains=busqueda) |
            Q(descripcion__icontains=busqueda) |
            Q(id__icontains=busqueda)
        )

    # Paginación
    paginator = Paginator(tickets_queryset.order_by('-fecha_creacion'), per_page)

    try:
        tickets_page = paginator.page(page)
    except:
        return JsonResponse({
            'success': False,
            'message': 'Página no encontrada',
            'has_next': False
        })

    # Renderizar template parcial
    html = render_to_string(
        'tickets/parciales/lista_tickets.html',
        {'tickets': tickets_page, 'user': user},
        request=request
    )

    return JsonResponse({
        'success': True,
        'html': html,
        'has_next': tickets_page.has_next(),
        'next_page': tickets_page.next_page_number() if tickets_page.has_next() else None,
        'current_page': page,
        'total_pages': paginator.num_pages,
        'total_tickets': paginator.count
    })

@login_required
def reportes_tickets(request):
    """
    Vista de reportes y análisis de tickets.

    Genera reportes detallados con gráficos y estadísticas
    sobre el rendimiento del sistema de tickets.

    Args:
        request: HttpRequest object con parámetros de filtro opcionales

    Returns:
        HttpResponse: Render del template con reportes

    Security:
        - Requiere autenticación (@login_required)
        - Datos filtrados por permisos de usuario
    """
    user = request.user

    # Obtener rango de fechas (último mes por defecto)
    fecha_hasta = timezone.now().date()
    fecha_desde = fecha_hasta - timedelta(days=30)

    # Permitir override de fechas
    if request.GET.get('fecha_desde'):
        try:
            fecha_desde = datetime.strptime(request.GET.get('fecha_desde'), '%Y-%m-%d').date()
        except ValueError:
            pass

    if request.GET.get('fecha_hasta'):
        try:
            fecha_hasta = datetime.strptime(request.GET.get('fecha_hasta'), '%Y-%m-%d').date()
        except ValueError:
            pass

    # Query base según permisos del usuario
    tickets_queryset = _get_tickets_queryset_by_user(user).filter(
        fecha_creacion__date__gte=fecha_desde,
        fecha_creacion__date__lte=fecha_hasta
    )

    # Estadísticas generales
    stats_generales = {
        'total_tickets': tickets_queryset.count(),
        'tickets_abiertos': tickets_queryset.filter(estado=1).count(),
        'tickets_en_progreso': tickets_queryset.filter(estado=2).count(),
        'tickets_cerrados': tickets_queryset.filter(estado=3).count(),
        'tickets_pendientes': tickets_queryset.filter(estado=4).count(),
    }

    # Estadísticas por prioridad
    stats_prioridad = {
        'baja': tickets_queryset.filter(prioridad=1).count(),
        'media': tickets_queryset.filter(prioridad=2).count(),
        'alta': tickets_queryset.filter(prioridad=3).count(),
        'critica': tickets_queryset.filter(prioridad=4).count(),
    }

    # Estadísticas por área (solo para administradores)
    stats_areas = []
    if _es_administrador(user):
        areas = Group.objects.all()
        for area in areas:
            area_tickets = tickets_queryset.filter(grupo=area)
            stats_areas.append({
                'nombre': area.name,
                'total': area_tickets.count(),
                'abiertos': area_tickets.filter(estado=1).count(),
                'cerrados': area_tickets.filter(estado=3).count(),
                'porcentaje_resolucion': round(
                    (area_tickets.filter(estado=3).count() / max(area_tickets.count(), 1)) * 100, 1
                )
            })

    # Tickets por día (últimos 7 días)
    tickets_por_dia = []
    for i in range(7):
        fecha = fecha_hasta - timedelta(days=i)
        count = tickets_queryset.filter(fecha_creacion__date=fecha).count()
        tickets_por_dia.append({
            'fecha': fecha.strftime('%d/%m'),
            'count': count
        })
    tickets_por_dia.reverse()

    # Top 5 usuarios con más tickets asignados
    if _puede_gestionar_asignaciones(user):
        from django.db.models import Count
        top_usuarios = User.objects.filter(
            asignaciones_recibidas__ticket__in=tickets_queryset,
            asignaciones_recibidas__is_active=True
        ).annotate(
            total_asignaciones=Count('asignaciones_recibidas')
        ).order_by('-total_asignaciones')[:5]
    else:
        top_usuarios = []

    # Tiempo promedio de resolución
    tickets_cerrados = tickets_queryset.filter(estado=3)
    tiempo_promedio = None
    if tickets_cerrados.exists():
        # Calcular tiempo promedio (simplificado)
        total_dias = 0
        count = 0
        for ticket in tickets_cerrados[:100]:  # Muestra de 100 tickets
            historial_cierre = ticket.historial.filter(
                accion__icontains='cerrado'
            ).first()
            if historial_cierre:
                dias = (historial_cierre.fecha - ticket.fecha_creacion).days
                total_dias += dias
                count += 1

        if count > 0:
            tiempo_promedio = round(total_dias / count, 1)

    context = {
        'stats_generales': stats_generales,
        'stats_prioridad': stats_prioridad,
        'stats_areas': stats_areas,
        'tickets_por_dia': tickets_por_dia,
        'top_usuarios': top_usuarios,
        'tiempo_promedio': tiempo_promedio,
        'fecha_desde': fecha_desde,
        'fecha_hasta': fecha_hasta,
        'es_administrador': _es_administrador(user),
        'puede_gestionar': _puede_gestionar_asignaciones(user)
    }

    return render(request, 'tickets/reportes_tickets.html', context)

@login_required
@require_http_methods(["GET"])
def estadisticas_tickets(request):
    """
    API AJAX para obtener estadísticas de tickets en tiempo real.

    Devuelve estadísticas actualizadas para dashboards y widgets
    sin necesidad de recargar la página.

    Args:
        request: HttpRequest object con parámetros opcionales

    Returns:
        JsonResponse: Estadísticas en formato JSON

    Security:
        - Requiere autenticación (@login_required)
        - Solo métodos GET (@require_http_methods)
        - Datos filtrados por permisos de usuario
    """
    user = request.user

    # Obtener período (hoy, semana, mes)
    periodo = request.GET.get('periodo', 'hoy')

    # Calcular fechas según período
    ahora = timezone.now()
    if periodo == 'hoy':
        fecha_desde = ahora.replace(hour=0, minute=0, second=0, microsecond=0)
    elif periodo == 'semana':
        fecha_desde = ahora - timedelta(days=7)
    elif periodo == 'mes':
        fecha_desde = ahora - timedelta(days=30)
    else:
        fecha_desde = ahora - timedelta(days=1)

    # Query base según permisos del usuario
    tickets_queryset = _get_tickets_queryset_by_user(user)
    tickets_periodo = tickets_queryset.filter(fecha_creacion__gte=fecha_desde)

    # Estadísticas básicas
    stats = {
        'total_tickets': tickets_queryset.count(),
        'tickets_periodo': tickets_periodo.count(),
        'tickets_abiertos': tickets_queryset.filter(estado=1).count(),
        'tickets_en_progreso': tickets_queryset.filter(estado=2).count(),
        'tickets_cerrados': tickets_queryset.filter(estado=3).count(),
        'tickets_pendientes': tickets_queryset.filter(estado=4).count(),
    }

    # Estadísticas por prioridad
    prioridades = {
        'baja': tickets_queryset.filter(prioridad=1).count(),
        'media': tickets_queryset.filter(prioridad=2).count(),
        'alta': tickets_queryset.filter(prioridad=3).count(),
        'critica': tickets_queryset.filter(prioridad=4).count(),
    }

    # Tickets sin asignar
    tickets_sin_asignar = tickets_queryset.filter(
        asignaciones__isnull=True
    ).count()

    # Mis asignaciones (si es empleado)
    mis_asignaciones = 0
    if not _es_administrador(user):
        mis_asignaciones = AsignacionTicket.objects.filter(
            usuario=user,
            is_active=True
        ).count()

    # Tendencia (comparar con período anterior)
    if periodo == 'hoy':
        fecha_anterior = fecha_desde - timedelta(days=1)
    elif periodo == 'semana':
        fecha_anterior = fecha_desde - timedelta(days=7)
    else:
        fecha_anterior = fecha_desde - timedelta(days=30)

    tickets_anterior = tickets_queryset.filter(
        fecha_creacion__gte=fecha_anterior,
        fecha_creacion__lt=fecha_desde
    ).count()

    # Calcular tendencia
    if tickets_anterior > 0:
        tendencia = round(((tickets_periodo.count() - tickets_anterior) / tickets_anterior) * 100, 1)
    else:
        tendencia = 100 if tickets_periodo.count() > 0 else 0

    # Estadísticas por área (solo administradores)
    areas_stats = []
    if _es_administrador(user):
        areas = Group.objects.all()[:5]  # Top 5 áreas
        for area in areas:
            area_tickets = tickets_queryset.filter(grupo=area)
            areas_stats.append({
                'nombre': area.name,
                'total': area_tickets.count(),
                'abiertos': area_tickets.filter(estado=1).count(),
                'cerrados': area_tickets.filter(estado=3).count()
            })

    # Respuesta JSON
    return JsonResponse({
        'success': True,
        'periodo': periodo,
        'fecha_actualizacion': ahora.strftime('%d/%m/%Y %H:%M:%S'),
        'estadisticas': stats,
        'prioridades': prioridades,
        'tickets_sin_asignar': tickets_sin_asignar,
        'mis_asignaciones': mis_asignaciones,
        'tendencia': {
            'porcentaje': tendencia,
            'direccion': 'up' if tendencia > 0 else 'down' if tendencia < 0 else 'stable'
        },
        'areas': areas_stats,
        'usuario': {
            'nombre': user.get_full_name() or user.username,
            'es_administrador': _es_administrador(user),
            'es_supervisor': _es_supervisor(user)
        }
    })


@login_required
def generar_pdf_ticket(request, ticket_id):
    """
    Genera un PDF optimizado para impresora de 3 pulgadas con QR code.

    El PDF incluye:
    - Título del ticket
    - Nombre del ciudadano
    - QR code que apunta a la URL pública
    - Pie de página de la municipalidad

    Args:
        request: HttpRequest object
        ticket_id: ID del ticket

    Returns:
        HttpResponse: PDF generado
    """
    user = request.user

    # Obtener ticket
    ticket = get_object_or_404(
        Ticket.objects.select_related('creado_por', 'grupo').prefetch_related('ciudadanos__ciudadano'),
        id=ticket_id,
        is_active=True
    )

    # Verificar permisos
    if not _puede_ver_ticket(user, ticket):
        messages.error(request, 'No tienes permisos para ver este ticket.')
        return redirect('tickets:lista_tickets')

    # Obtener ciudadano
    ciudadano = ticket.get_ciudadano()

    # Crear respuesta HTTP para PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="ticket_{ticket.id}.pdf"'

    # Configurar tamaño de página para impresora de 3 pulgadas (72mm de ancho)
    # Convertir mm a puntos (1mm = 2.834645669 puntos)
    ancho_mm = 58  # Reducir ancho para menos espacio en blanco
    alto_mm = 110  # Aumentar altura para acomodar QR más grande
    ancho_puntos = ancho_mm * 2.834645669
    alto_puntos = alto_mm * 2.834645669

    # Crear PDF
    buffer = BytesIO()
    p = canvas.Canvas(buffer, pagesize=(ancho_puntos, alto_puntos))

    # Configurar fuentes y tamaños
    titulo_size = 10
    texto_size = 8
    pie_size = 6

    # Posición inicial (desde arriba)
    y_position = alto_puntos - 20

    # Título del ticket (centrado)
    p.setFont("Helvetica-Bold", titulo_size)
    titulo_texto = f"Ticket #{ticket.id}"
    titulo_width = p.stringWidth(titulo_texto, "Helvetica-Bold", titulo_size)
    p.drawString((ancho_puntos - titulo_width) / 2, y_position, titulo_texto)
    y_position -= 15

    # Título del problema (centrado, con wrap)
    p.setFont("Helvetica", texto_size)
    titulo_ticket = ticket.titulo
    if len(titulo_ticket) > 30:
        # Dividir en líneas
        palabras = titulo_ticket.split()
        lineas = []
        linea_actual = ""

        for palabra in palabras:
            if len(linea_actual + " " + palabra) <= 30:
                linea_actual += " " + palabra if linea_actual else palabra
            else:
                if linea_actual:
                    lineas.append(linea_actual)
                linea_actual = palabra

        if linea_actual:
            lineas.append(linea_actual)

        for linea in lineas[:2]:  # Máximo 2 líneas para ahorrar espacio
            linea_width = p.stringWidth(linea, "Helvetica", texto_size)
            p.drawString((ancho_puntos - linea_width) / 2, y_position, linea)
            y_position -= 10
    else:
        titulo_width = p.stringWidth(titulo_ticket, "Helvetica", texto_size)
        p.drawString((ancho_puntos - titulo_width) / 2, y_position, titulo_ticket)
        y_position -= 12

    # Ciudadano (si existe)
    if ciudadano:
        y_position -= 3
        p.setFont("Helvetica-Bold", texto_size)
        ciudadano_label = "Ciudadano:"
        label_width = p.stringWidth(ciudadano_label, "Helvetica-Bold", texto_size)
        p.drawString((ancho_puntos - label_width) / 2, y_position, ciudadano_label)
        y_position -= 10

        p.setFont("Helvetica", texto_size)
        nombre_ciudadano = ciudadano.nombre_completo
        if len(nombre_ciudadano) > 30:  # Reducir límite para texto más compacto
            nombre_ciudadano = nombre_ciudadano[:17] + "..."

        nombre_width = p.stringWidth(nombre_ciudadano, "Helvetica", texto_size)
        p.drawString((ancho_puntos - nombre_width) / 2, y_position, nombre_ciudadano)
        y_position -= 15

    # Generar QR Code
    try:
        from reportlab.lib.utils import ImageReader

        qr_url = request.build_absolute_uri(f'/consulta/{ticket.token}/')
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=2,
            border=1,
        )
        qr.add_data(qr_url)
        qr.make(fit=True)

        # Crear imagen QR
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Guardar QR en memoria usando BytesIO
        qr_buffer = BytesIO()
        qr_img.save(qr_buffer, format='PNG')
        qr_buffer.seek(0)

        # Usar ImageReader para que ReportLab pueda leer el BytesIO
        qr_reader = ImageReader(qr_buffer)

        # Agregar QR al PDF (centrado)
        qr_size = 120  # Aumentar tamaño del QR (doble del anterior)
        qr_x = (ancho_puntos - qr_size) / 2
        p.drawImage(qr_reader, qr_x, y_position - qr_size, qr_size, qr_size)
        y_position -= qr_size + 8

        # Cerrar buffer
        qr_buffer.close()

    except Exception as e:
        # Si falla el QR, continuar sin él
        print(f"Error generando QR: {e}")
        y_position -= 10

    # Fecha de creación (formato Guatemala)
    from django.utils import timezone
    import pytz

    # Convertir a zona horaria de Guatemala
    guatemala_tz = pytz.timezone('America/Guatemala')
    fecha_guatemala = ticket.fecha_creacion.astimezone(guatemala_tz)

    p.setFont("Helvetica", pie_size)
    fecha_texto = f"Fecha: {fecha_guatemala.strftime('%d/%m/%Y %I:%M %p')}"
    fecha_width = p.stringWidth(fecha_texto, "Helvetica", pie_size)
    p.drawString((ancho_puntos - fecha_width) / 2, y_position, fecha_texto)
    y_position -= 10

    # Pie de página
    p.setFont("Helvetica-Bold", pie_size)
    municipalidad_texto = "Municipalidad de Estanzuela"
    municipalidad_width = p.stringWidth(municipalidad_texto, "Helvetica-Bold", pie_size)
    p.drawString((ancho_puntos - municipalidad_width) / 2, y_position, municipalidad_texto)
    y_position -= 8

    p.setFont("Helvetica", pie_size)
    slogan_texto = "Un gobierno de puertas Abiertas"
    slogan_width = p.stringWidth(slogan_texto, "Helvetica", pie_size)
    p.drawString((ancho_puntos - slogan_width) / 2, y_position, slogan_texto)

    # Finalizar PDF
    p.showPage()
    p.save()

    # Obtener contenido del buffer
    pdf_content = buffer.getvalue()
    buffer.close()

    # Escribir contenido a la respuesta
    response.write(pdf_content)

    return response


@login_required
@require_http_methods(["POST"])
def eliminar_imagen_ticket(request, imagen_id):
    """
    Elimina una imagen específica de un ticket.

    Args:
        request: HttpRequest object
        imagen_id: ID de la imagen a eliminar

    Returns:
        JsonResponse: Respuesta JSON con el resultado
    """
    try:
        # Obtener la imagen
        imagen = get_object_or_404(
            TicketImagen.objects.select_related('ticket'),
            id=imagen_id,
            is_active=True
        )

        # Verificar permisos para editar el ticket
        if not _puede_editar_ticket(request.user, imagen.ticket):
            return JsonResponse({
                'success': False,
                'error': 'No tienes permisos para eliminar esta imagen.'
            }, status=403)

        # Marcar imagen como inactiva (soft delete)
        imagen.is_active = False
        imagen.save()

        # Registrar en historial
        HistorialTicket.registrar_cambio(
            ticket=imagen.ticket,
            usuario=request.user,
            accion='Imagen eliminada',
            detalles={
                'imagen_id': imagen.id,
                'descripcion': imagen.descripcion,
                'eliminada_por': request.user.username
            }
        )

        return JsonResponse({
            'success': True,
            'message': 'Imagen eliminada exitosamente.'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error al eliminar la imagen: {str(e)}'
        }, status=500)
