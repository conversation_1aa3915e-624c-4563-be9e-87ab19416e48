"""
notificaciones/forms.py
Formularios para el sistema de notificaciones manuales.

Incluye:
- NotificacionForm: Formulario base para crear notificaciones
- NotificacionUsuarioForm: Para notificaciones a usuarios específicos
- NotificacionGrupoForm: Para notificaciones a grupos
- NotificacionMasivaForm: Para envío masivo con múltiples destinatarios
"""

from django import forms
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.html import format_html
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML, Div
from crispy_forms.bootstrap import Alert
from .models import Notificacion, NotificacionUsuario, NotificacionGrupo

User = get_user_model()


class NotificacionBaseForm(forms.ModelForm):
    """
    Formulario base para notificaciones con campos comunes.
    """
    
    class Meta:
        model = Notificacion
        fields = ['titulo', 'mensaje', 'tipo', 'url_accion']
        widgets = {
            'titulo': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Título de la notificación (opcional)'
            }),
            'mensaje': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Escriba el mensaje de la notificación...',
                'maxlength': 500
            }),
            'tipo': forms.Select(attrs={
                'class': 'form-select'
            }),
            'url_accion': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://ejemplo.com/accion (opcional)'
            })
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Configurar crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.attrs = {'novalidate': ''}
        
        # Hacer el mensaje requerido
        self.fields['mensaje'].required = True
        
        # Añadir help texts
        self.fields['titulo'].help_text = 'Título opcional para la notificación'
        self.fields['mensaje'].help_text = 'Mensaje principal (máximo 500 caracteres)'
        self.fields['tipo'].help_text = 'Seleccione el tipo de notificación'
        self.fields['url_accion'].help_text = 'URL opcional para redireccionar al hacer clic'
    
    def clean_mensaje(self):
        """Validación personalizada para el mensaje."""
        mensaje = self.cleaned_data.get('mensaje')
        if mensaje:
            mensaje = mensaje.strip()
            if len(mensaje) < 5:
                raise ValidationError('El mensaje debe tener al menos 5 caracteres.')
            if len(mensaje) > 500:
                raise ValidationError('El mensaje no puede exceder 500 caracteres.')
        return mensaje
    
    def save(self, commit=True):
        """Guarda la notificación asignando el usuario creador."""
        notificacion = super().save(commit=False)
        if self.user:
            notificacion.creado_por = self.user
        if commit:
            notificacion.save()
        return notificacion


class NotificacionUsuarioForm(NotificacionBaseForm):
    """
    Formulario para crear notificaciones dirigidas a usuarios específicos.
    """

    usuarios_selected = forms.CharField(
        widget=forms.HiddenInput(),
        required=True,
        help_text='IDs de usuarios seleccionados separados por coma'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Layout con crispy forms (sin incluir usuarios_selected ya que está en el template)
        self.helper.layout = Layout(
            Alert(
                content='<i class="fas fa-info-circle"></i> Complete los campos para crear una notificación dirigida a usuarios específicos.',
                css_class='alert-info'
            ),
            Fieldset(
                'Información de la Notificación',
                Row(
                    Column('titulo', css_class='col-md-6'),
                    Column('tipo', css_class='col-md-6'),
                ),
                'mensaje',
                'url_accion'
            ),
            Fieldset(
                'Destinatarios',
                HTML('''
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-users"></i> Usuarios Destinatarios</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#usuariosModal">
                                <i class="fas fa-search"></i> Seleccionar Usuarios
                            </button>
                        </div>
                        <div id="usuarios-preview" class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No hay usuarios seleccionados
                            </div>
                        </div>
                    </div>
                ''')
            ),
            Div(
                Submit('submit', 'Crear y Enviar Notificación', css_class='btn btn-primary btn-lg'),
                HTML('<a href="/notificaciones/" class="btn btn-secondary btn-lg ms-2">Cancelar</a>'),
                css_class='text-center mt-4'
            )
        )
    
    def _user_label(self, user):
        """Genera un label personalizado para cada usuario."""
        nombre = user.get_full_name() or user.username
        cargo = f" ({user.cargo.nombre})" if user.cargo else ""
        grupos = ", ".join([g.name for g in user.groups.all()[:2]])
        if grupos:
            grupos = f" - {grupos}"
        return f"{nombre}{cargo}{grupos}"
    
    def clean_usuarios_selected(self):
        """Validación para usuarios seleccionados."""
        usuarios_ids = self.cleaned_data.get('usuarios_selected')
        if not usuarios_ids:
            raise ValidationError('Debe seleccionar al menos un usuario.')

        try:
            ids_list = [int(id.strip()) for id in usuarios_ids.split(',') if id.strip()]
        except ValueError:
            raise ValidationError('IDs de usuarios inválidos.')

        if len(ids_list) == 0:
            raise ValidationError('Debe seleccionar al menos un usuario.')

        if len(ids_list) > 50:
            raise ValidationError('No puede seleccionar más de 50 usuarios a la vez.')

        # Verificar que los usuarios existen y están activos
        usuarios = User.objects.filter(id__in=ids_list, is_active=True)
        if usuarios.count() != len(ids_list):
            raise ValidationError('Algunos usuarios seleccionados no son válidos.')

        return usuarios
    
    def save(self, commit=True):
        """Guarda la notificación y la envía a los usuarios seleccionados."""
        notificacion = super().save(commit=commit)
        
        if commit:
            usuarios = self.cleaned_data['usuarios_selected']
            notificaciones_creadas = []

            for usuario in usuarios:
                notif_usuario = notificacion.enviar_a_usuario(usuario)
                notificaciones_creadas.append(notif_usuario)

            # Almacenar información adicional para el response
            notificacion._usuarios_notificados = len(notificaciones_creadas)
        
        return notificacion


class NotificacionGrupoForm(NotificacionBaseForm):
    """
    Formulario para crear notificaciones dirigidas a grupos específicos.
    """

    grupos_selected = forms.CharField(
        widget=forms.HiddenInput(),
        required=True,
        help_text='IDs de grupos seleccionados separados por coma'
    )
    
    crear_individuales = forms.BooleanField(
        required=False,
        initial=True,
        help_text='Crear notificaciones individuales para cada miembro del grupo',
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Layout con crispy forms (sin incluir grupos_selected ya que está en el template)
        self.helper.layout = Layout(
            Alert(
                content='<i class="fas fa-users-cog"></i> Complete los campos para crear una notificación dirigida a grupos específicos.',
                css_class='alert-info'
            ),
            Fieldset(
                'Información de la Notificación',
                Row(
                    Column('titulo', css_class='col-md-6'),
                    Column('tipo', css_class='col-md-6'),
                ),
                'mensaje',
                'url_accion'
            ),
            Fieldset(
                'Destinatarios',
                HTML('''
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-layer-group"></i> Grupos Destinatarios</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#gruposModal">
                                <i class="fas fa-search"></i> Seleccionar Grupos
                            </button>
                        </div>
                        <div id="grupos-preview" class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No hay grupos seleccionados
                            </div>
                        </div>
                    </div>
                '''),
                HTML('<hr>'),
                'crear_individuales'
            ),
            Div(
                Submit('submit', 'Crear y Enviar Notificación', css_class='btn btn-success btn-lg'),
                HTML('<a href="/notificaciones/" class="btn btn-secondary btn-lg ms-2">Cancelar</a>'),
                css_class='text-center mt-4'
            )
        )
    
    def _group_label(self, group):
        """Genera un label personalizado para cada grupo."""
        miembros_count = group.user_set.filter(is_active=True).count()
        return f"{group.name} ({miembros_count} miembros activos)"
    
    def clean_grupos_selected(self):
        """Validación para grupos seleccionados."""
        grupos_ids = self.cleaned_data.get('grupos_selected')
        if not grupos_ids:
            raise ValidationError('Debe seleccionar al menos un grupo.')

        try:
            ids_list = [int(id.strip()) for id in grupos_ids.split(',') if id.strip()]
        except ValueError:
            raise ValidationError('IDs de grupos inválidos.')

        if len(ids_list) == 0:
            raise ValidationError('Debe seleccionar al menos un grupo.')

        # Verificar que los grupos existen
        grupos = Group.objects.filter(id__in=ids_list)
        if grupos.count() != len(ids_list):
            raise ValidationError('Algunos grupos seleccionados no son válidos.')

        return grupos
    
    def save(self, commit=True):
        """Guarda la notificación y la envía a los grupos seleccionados."""
        notificacion = super().save(commit=commit)
        
        if commit:
            grupos = self.cleaned_data['grupos_selected']
            crear_individuales = self.cleaned_data.get('crear_individuales', True)

            notificaciones_grupo = []
            total_individuales = 0

            for grupo in grupos:
                notif_grupo = notificacion.enviar_a_grupo(grupo)
                notificaciones_grupo.append(notif_grupo)

                if crear_individuales:
                    individuales = notif_grupo.crear_notificaciones_individuales()
                    total_individuales += individuales

            # Almacenar información adicional para el response
            notificacion._grupos_notificados = len(notificaciones_grupo)
            notificacion._individuales_creadas = total_individuales
        
        return notificacion


class NotificacionMasivaForm(NotificacionBaseForm):
    """
    Formulario simplificado para crear notificaciones masivas a todos los usuarios.
    """

    confirmacion = forms.BooleanField(
        required=True,
        label='Confirmo que deseo enviar esta notificación a TODOS los usuarios activos del sistema',
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Esta acción enviará la notificación a todos los usuarios activos'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Layout simplificado con crispy forms
        self.helper.layout = Layout(
            Alert(
                content='<i class="fas fa-exclamation-triangle"></i> <strong>¡ATENCIÓN!</strong> Esta notificación se enviará automáticamente a TODOS los usuarios activos del sistema.',
                css_class='alert-danger'
            ),
            Fieldset(
                'Información de la Notificación',
                Row(
                    Column('titulo', css_class='col-md-6'),
                    Column('tipo', css_class='col-md-6'),
                ),
                'mensaje',
                'url_accion'
            ),
            Fieldset(
                'Confirmación',
                HTML('<div class="alert alert-warning"><i class="fas fa-users"></i> <strong>Destinatarios:</strong> Todos los usuarios activos del sistema</div>'),
                'confirmacion'
            ),
            Div(
                Submit('submit', 'Enviar Notificación Masiva', css_class='btn btn-danger btn-lg', onclick='return confirmarEnvioMasivo()'),
                HTML('<a href="/notificaciones/" class="btn btn-secondary btn-lg ms-2">Cancelar</a>'),
                css_class='text-center mt-4'
            )
        )

    def _user_label(self, user):
        """Genera un label personalizado para cada usuario."""
        nombre = user.get_full_name() or user.username
        cargo = f" ({user.cargo.nombre})" if user.cargo else ""
        return f"{nombre}{cargo}"

    def _group_label(self, group):
        """Genera un label personalizado para cada grupo."""
        miembros_count = group.user_set.filter(is_active=True).count()
        return f"{group.name} ({miembros_count} miembros)"

    def clean_confirmacion(self):
        """Validación de confirmación."""
        confirmacion = self.cleaned_data.get('confirmacion')
        if not confirmacion:
            raise ValidationError('Debe confirmar que desea enviar la notificación a todos los usuarios.')
        return confirmacion

    def save(self, commit=True):
        """Guarda la notificación y la envía a todos los usuarios activos."""
        notificacion = super().save(commit=commit)

        if commit:
            # Enviar a todos los usuarios activos
            usuarios_activos = User.objects.filter(is_active=True)
            usuarios_notificados = 0

            for usuario in usuarios_activos:
                notificacion.enviar_a_usuario(usuario)
                usuarios_notificados += 1

            # Almacenar estadísticas
            notificacion._usuarios_notificados = usuarios_notificados
            notificacion._es_masiva = True

        return notificacion


class FiltroNotificacionesForm(forms.Form):
    """
    Formulario para filtrar notificaciones en el listado.
    """

    tipo = forms.ChoiceField(
        choices=[('', 'Todos los tipos')] + list(Notificacion.TIPO_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    fecha_desde = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    fecha_hasta = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    creado_por = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        empty_label='Todos los creadores',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Configurar crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'row g-3'

        self.helper.layout = Layout(
            Row(
                Column('tipo', css_class='col-md-3'),
                Column('fecha_desde', css_class='col-md-3'),
                Column('fecha_hasta', css_class='col-md-3'),
                Column('creado_por', css_class='col-md-3'),
            ),
            Div(
                Submit('filtrar', 'Filtrar', css_class='btn btn-primary'),
                HTML('<a href="?" class="btn btn-outline-secondary ms-2">Limpiar</a>'),
                css_class='col-12 text-center mt-3'
            )
        )
