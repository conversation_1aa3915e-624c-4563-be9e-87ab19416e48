[{"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "messages", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "messages", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "AppConfig", "importPath": "django.apps", "description": "django.apps", "isExtraImport": true, "detail": "django.apps", "documentation": {}}, {"label": "AppConfig", "importPath": "django.apps", "description": "django.apps", "isExtraImport": true, "detail": "django.apps", "documentation": {}}, {"label": "AppConfig", "importPath": "django.apps", "description": "django.apps", "isExtraImport": true, "detail": "django.apps", "documentation": {}}, {"label": "AppConfig", "importPath": "django.apps", "description": "django.apps", "isExtraImport": true, "detail": "django.apps", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "migrations", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "migrations", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "migrations", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "TestCase", "importPath": "django.test", "description": "django.test", "isExtraImport": true, "detail": "django.test", "documentation": {}}, {"label": "TestCase", "importPath": "django.test", "description": "django.test", "isExtraImport": true, "detail": "django.test", "documentation": {}}, {"label": "TestCase", "importPath": "django.test", "description": "django.test", "isExtraImport": true, "detail": "django.test", "documentation": {}}, {"label": "TestCase", "importPath": "django.test", "description": "django.test", "isExtraImport": true, "detail": "django.test", "documentation": {}}, {"label": "render", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "render", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "render", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "redirect", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "render", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "redirect", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "get_object_or_404", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "get_asgi_application", "importPath": "django.core.asgi", "description": "django.core.asgi", "isExtraImport": true, "detail": "django.core.asgi", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "include", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "reverse", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "get_wsgi_application", "importPath": "django.core.wsgi", "description": "django.core.wsgi", "isExtraImport": true, "detail": "django.core.wsgi", "documentation": {}}, {"label": "authenticate", "importPath": "django.contrib.auth", "description": "django.contrib.auth", "isExtraImport": true, "detail": "django.contrib.auth", "documentation": {}}, {"label": "login", "importPath": "django.contrib.auth", "description": "django.contrib.auth", "isExtraImport": true, "detail": "django.contrib.auth", "documentation": {}}, {"label": "logout", "importPath": "django.contrib.auth", "description": "django.contrib.auth", "isExtraImport": true, "detail": "django.contrib.auth", "documentation": {}}, {"label": "get_user_model", "importPath": "django.contrib.auth", "description": "django.contrib.auth", "isExtraImport": true, "detail": "django.contrib.auth", "documentation": {}}, {"label": "django.contrib.auth.models", "kind": 6, "isExtraImport": true, "importPath": "django.contrib.auth.models", "description": "django.contrib.auth.models", "detail": "django.contrib.auth.models", "documentation": {}}, {"label": "Group", "importPath": "django.contrib.auth.models", "description": "django.contrib.auth.models", "isExtraImport": true, "detail": "django.contrib.auth.models", "documentation": {}}, {"label": "AbstractUser", "importPath": "django.contrib.auth.models", "description": "django.contrib.auth.models", "isExtraImport": true, "detail": "django.contrib.auth.models", "documentation": {}}, {"label": "django.contrib.auth.validators", "kind": 6, "isExtraImport": true, "importPath": "django.contrib.auth.validators", "description": "django.contrib.auth.validators", "detail": "django.contrib.auth.validators", "documentation": {}}, {"label": "django.db.models.deletion", "kind": 6, "isExtraImport": true, "importPath": "django.db.models.deletion", "description": "django.db.models.deletion", "detail": "django.db.models.deletion", "documentation": {}}, {"label": "django.utils.timezone", "kind": 6, "isExtraImport": true, "importPath": "django.utils.timezone", "description": "django.utils.timezone", "detail": "django.utils.timezone", "documentation": {}}, {"label": "settings", "importPath": "django.conf", "description": "django.conf", "isExtraImport": true, "detail": "django.conf", "documentation": {}}, {"label": "settings", "importPath": "django.conf", "description": "django.conf", "isExtraImport": true, "detail": "django.conf", "documentation": {}}, {"label": "settings", "importPath": "django.conf", "description": "django.conf", "isExtraImport": true, "detail": "django.conf", "documentation": {}}, {"label": "forms", "importPath": "django", "description": "django", "isExtraImport": true, "detail": "django", "documentation": {}}, {"label": "forms", "importPath": "django", "description": "django", "isExtraImport": true, "detail": "django", "documentation": {}}, {"label": "GroupAdmin", "importPath": "django.contrib.auth.admin", "description": "django.contrib.auth.admin", "isExtraImport": true, "detail": "django.contrib.auth.admin", "documentation": {}}, {"label": "UserCreationForm", "importPath": "django.contrib.auth.forms", "description": "django.contrib.auth.forms", "isExtraImport": true, "detail": "django.contrib.auth.forms", "documentation": {}}, {"label": "UserChangeForm", "importPath": "django.contrib.auth.forms", "description": "django.contrib.auth.forms", "isExtraImport": true, "detail": "django.contrib.auth.forms", "documentation": {}}, {"label": "ValidationError", "importPath": "django.core.exceptions", "description": "django.core.exceptions", "isExtraImport": true, "detail": "django.core.exceptions", "documentation": {}}, {"label": "timezone", "importPath": "django.utils", "description": "django.utils", "isExtraImport": true, "detail": "django.utils", "documentation": {}}, {"label": "static", "importPath": "django.conf.urls.static", "description": "django.conf.urls.static", "isExtraImport": true, "detail": "django.conf.urls.static", "documentation": {}}, {"label": "login_required", "importPath": "django.contrib.auth.decorators", "description": "django.contrib.auth.decorators", "isExtraImport": true, "detail": "django.contrib.auth.decorators", "documentation": {}}, {"label": "permission_required", "importPath": "django.contrib.auth.decorators", "description": "django.contrib.auth.decorators", "isExtraImport": true, "detail": "django.contrib.auth.decorators", "documentation": {}}, {"label": "Paginator", "importPath": "django.core.paginator", "description": "django.core.paginator", "isExtraImport": true, "detail": "django.core.paginator", "documentation": {}}, {"label": "Prefetch", "importPath": "django.db.models", "description": "django.db.models", "isExtraImport": true, "detail": "django.db.models", "documentation": {}}, {"label": "Q", "importPath": "django.db.models", "description": "django.db.models", "isExtraImport": true, "detail": "django.db.models", "documentation": {}}, {"label": "JsonResponse", "importPath": "django.http", "description": "django.http", "isExtraImport": true, "detail": "django.http", "documentation": {}}, {"label": "render_to_string", "importPath": "django.template.loader", "description": "django.template.loader", "isExtraImport": true, "detail": "django.template.loader", "documentation": {}}, {"label": "method_decorator", "importPath": "django.utils.decorators", "description": "django.utils.decorators", "isExtraImport": true, "detail": "django.utils.decorators", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "BaseConfig", "kind": 6, "importPath": "Base.apps", "description": "Base.apps", "peekOfCode": "class BaseConfig(AppConfig):\n    default_auto_field = \"django.db.models.BigAutoField\"\n    name = \"Base\"", "detail": "Base.apps", "documentation": {}}, {"label": "application", "kind": 5, "importPath": "core.asgi", "description": "core.asgi", "peekOfCode": "application = get_asgi_application()", "detail": "core.asgi", "documentation": {}}, {"label": "BASE_DIR", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "BASE_DIR = Path(__file__).resolve().parent.parent\n# Quick-start development settings - unsuitable for production\n# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/\n# SECURITY WARNING: keep the secret key used in production secret!\nSECRET_KEY = \"django-insecure-(7_$luw50_rao@2e%m(1_j(zu^)*panzee&iism4-qq+z4i@#q\"\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [", "detail": "core.settings", "documentation": {}}, {"label": "SECRET_KEY", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "SECRET_KEY = \"django-insecure-(7_$luw50_rao@2e%m(1_j(zu^)*panzee&iism4-qq+z4i@#q\"\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",", "detail": "core.settings", "documentation": {}}, {"label": "DEBUG", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "DEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",\n    \"django.contrib.messages\",\n    \"django.contrib.staticfiles\",", "detail": "core.settings", "documentation": {}}, {"label": "ALLOWED_HOSTS", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "ALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",\n    \"django.contrib.messages\",\n    \"django.contrib.staticfiles\",\n    \"login_app\",", "detail": "core.settings", "documentation": {}}, {"label": "INSTALLED_APPS", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "INSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",\n    \"django.contrib.messages\",\n    \"django.contrib.staticfiles\",\n    \"login_app\",\n    \"Base\",\n    \"Home\",", "detail": "core.settings", "documentation": {}}, {"label": "MIDDLEWARE", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "MIDDLEWARE = [\n    \"django.middleware.security.SecurityMiddleware\",\n    \"django.contrib.sessions.middleware.SessionMiddleware\",\n    \"django.middleware.common.CommonMiddleware\",\n    \"django.middleware.csrf.CsrfViewMiddleware\",\n    \"django.contrib.auth.middleware.AuthenticationMiddleware\",\n    \"django.contrib.messages.middleware.MessageMiddleware\",\n    \"django.middleware.clickjacking.XFrameOptionsMiddleware\",\n]\nROOT_URLCONF = \"core.urls\"", "detail": "core.settings", "documentation": {}}, {"label": "ROOT_URLCONF", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "ROOT_URLCONF = \"core.urls\"\nTEMPLATES = [\n    {\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\n        \"DIRS\": [],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"context_processors\": [\n                \"django.template.context_processors.debug\",\n                \"django.template.context_processors.request\",", "detail": "core.settings", "documentation": {}}, {"label": "TEMPLATES", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "TEMPLATES = [\n    {\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\n        \"DIRS\": [],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"context_processors\": [\n                \"django.template.context_processors.debug\",\n                \"django.template.context_processors.request\",\n                \"django.contrib.auth.context_processors.auth\",", "detail": "core.settings", "documentation": {}}, {"label": "WSGI_APPLICATION", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "WSGI_APPLICATION = \"core.wsgi.application\"\n# Database\n# https://docs.djangoproject.com/en/5.0/ref/settings/#databases\nDATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.mysql',\n        'NAME': 'controlticket',\n        'USER': 'root',\n        'PASSWORD': 'sosa',\n        'HOST': 'localhost',", "detail": "core.settings", "documentation": {}}, {"label": "DATABASES", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "DATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.mysql',\n        'NAME': 'controlticket',\n        'USER': 'root',\n        'PASSWORD': 'sosa',\n        'HOST': 'localhost',\n        'POST': '3306',\n        'OPTIONS': {\n            'sql_mode': 'traditional',", "detail": "core.settings", "documentation": {}}, {"label": "AUTH_PASSWORD_VALIDATORS", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "AUTH_PASSWORD_VALIDATORS = [\n    {\n        \"NAME\": \"django.contrib.auth.password_validation.UserAttributeSimilarityValidator\",\n    },\n    {\n        \"NAME\": \"django.contrib.auth.password_validation.MinimumLengthValidator\",\n    },\n    {\n        \"NAME\": \"django.contrib.auth.password_validation.CommonPasswordValidator\",\n    },", "detail": "core.settings", "documentation": {}}, {"label": "LANGUAGE_CODE", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "LANGUAGE_CODE = 'es-gt'\nTIME_ZONE = 'America/Guatemala'\nUSE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.0/howto/static-files/\nSTATIC_URL = \"static/\"\n# Directorios donde Django buscará archivos estáticos adicionales\nSTATICFILES_DIRS = [\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"", "detail": "core.settings", "documentation": {}}, {"label": "TIME_ZONE", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "TIME_ZONE = 'America/Guatemala'\nUSE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.0/howto/static-files/\nSTATIC_URL = \"static/\"\n# Directorios donde Django buscará archivos estáticos adicionales\nSTATICFILES_DIRS = [\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"\n]", "detail": "core.settings", "documentation": {}}, {"label": "USE_I18N", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "USE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.0/howto/static-files/\nSTATIC_URL = \"static/\"\n# Directorios donde Django buscará archivos estáticos adicionales\nSTATICFILES_DIRS = [\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"\n]\n# Ruta donde se recopilarán los archivos estáticos durante el despliegue (no necesario en desarrollo)", "detail": "core.settings", "documentation": {}}, {"label": "USE_TZ", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "USE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.0/howto/static-files/\nSTATIC_URL = \"static/\"\n# Directorios donde Django buscará archivos estáticos adicionales\nSTATICFILES_DIRS = [\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"\n]\n# Ruta donde se recopilarán los archivos estáticos durante el despliegue (no necesario en desarrollo)\nSTATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')", "detail": "core.settings", "documentation": {}}, {"label": "STATIC_URL", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "STATIC_URL = \"static/\"\n# Directorios donde Django buscará archivos estáticos adicionales\nSTATICFILES_DIRS = [\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"\n]\n# Ruta donde se recopilarán los archivos estáticos durante el despliegue (no necesario en desarrollo)\nSTATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "core.settings", "documentation": {}}, {"label": "STATICFILES_DIRS", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "STATICFILES_DIRS = [\n    os.path.join(BASE_DIR, 'Base/static'),  # Ruta a la carpeta static de la aplicación \"base\"\n]\n# Ruta donde se recopilarán los archivos estáticos durante el despliegue (no necesario en desarrollo)\nSTATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"\nLOGIN_URL = '/'\n#AUTH_USER_MODEL = 'login_app.User'", "detail": "core.settings", "documentation": {}}, {"label": "STATIC_ROOT", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"\nLOGIN_URL = '/'\n#AUTH_USER_MODEL = 'login_app.User'\nAUTH_USER_MODEL = 'user.User'", "detail": "core.settings", "documentation": {}}, {"label": "DEFAULT_AUTO_FIELD", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "DEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"\nLOGIN_URL = '/'\n#AUTH_USER_MODEL = 'login_app.User'\nAUTH_USER_MODEL = 'user.User'", "detail": "core.settings", "documentation": {}}, {"label": "LOGIN_URL", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "LOGIN_URL = '/'\n#AUTH_USER_MODEL = 'login_app.User'\nAUTH_USER_MODEL = 'user.User'", "detail": "core.settings", "documentation": {}}, {"label": "#AUTH_USER_MODEL", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "#AUTH_USER_MODEL = 'login_app.User'\nAUTH_USER_MODEL = 'user.User'", "detail": "core.settings", "documentation": {}}, {"label": "AUTH_USER_MODEL", "kind": 5, "importPath": "core.settings", "description": "core.settings", "peekOfCode": "AUTH_USER_MODEL = 'user.User'", "detail": "core.settings", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "core.urls", "description": "core.urls", "peekOfCode": "urlpatterns = [\n    path(\"admin/\", admin.site.urls),\n    path('', include('login_app.urls')),\n    path('inicio/', include('Home.urls')),\n    path('user/', include('user.urls')),\n]", "detail": "core.urls", "documentation": {}}, {"label": "application", "kind": 5, "importPath": "core.wsgi", "description": "core.wsgi", "peekOfCode": "application = get_wsgi_application()", "detail": "core.wsgi", "documentation": {}}, {"label": "HomeConfig", "kind": 6, "importPath": "Home.apps", "description": "Home.apps", "peekOfCode": "class HomeConfig(AppConfig):\n    default_auto_field = \"django.db.models.BigAutoField\"\n    name = \"Home\"", "detail": "Home.apps", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "Home.urls", "description": "Home.urls", "peekOfCode": "urlpatterns = [\n    path('', views.home_view, name='home'),\n]", "detail": "Home.urls", "documentation": {}}, {"label": "home_view", "kind": 2, "importPath": "Home.views", "description": "Home.views", "peekOfCode": "def home_view(request):\n    return render(request, 'home.html')", "detail": "Home.views", "documentation": {}}, {"label": "LoginAppConfig", "kind": 6, "importPath": "login_app.apps", "description": "login_app.apps", "peekOfCode": "class LoginAppConfig(AppConfig):\n    default_auto_field = \"django.db.models.BigAutoField\"\n    name = \"login_app\"", "detail": "login_app.apps", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "login_app.urls", "description": "login_app.urls", "peekOfCode": "urlpatterns = [\n    path('', views.login_view, name='login'),\n    path('logout/', views.logout_view, name='logout'),\n]", "detail": "login_app.urls", "documentation": {}}, {"label": "login_view", "kind": 2, "importPath": "login_app.views", "description": "login_app.views", "peekOfCode": "def login_view(request):\n    if request.method == 'POST':\n        username = request.POST['username']\n        password = request.POST['password']\n        user = authenticate(request, username=username, password=password)\n        if user is not None:\n            login(request, user)\n            return redirect('home')  # Redirige a la página de inicio después del login\n        else:\n            messages.error(request, 'Usuario o contraseña incorrectos.')", "detail": "login_app.views", "documentation": {}}, {"label": "logout_view", "kind": 2, "importPath": "login_app.views", "description": "login_app.views", "peekOfCode": "def logout_view(request):\n    logout(request)  # Cierra la sesión del usuario\n    return redirect('login')  # Redirige al usuario a la página de login", "detail": "login_app.views", "documentation": {}}, {"label": "Migration", "kind": 6, "importPath": "user.migrations.0001_initial", "description": "user.migrations.0001_initial", "peekOfCode": "class Migration(migrations.Migration):\n    initial = True\n    dependencies = [\n        (\"auth\", \"0012_alter_user_first_name_max_length\"),\n    ]\n    operations = [\n        migrations.CreateModel(\n            name=\"CargoUsuario\",\n            fields=[\n                (", "detail": "user.migrations.0001_initial", "documentation": {}}, {"label": "Migration", "kind": 6, "importPath": "user.migrations.0002_alter_user_fecha_nacimiento", "description": "user.migrations.0002_alter_user_fecha_nacimiento", "peekOfCode": "class Migration(migrations.Migration):\n    dependencies = [\n        (\"user\", \"0001_initial\"),\n    ]\n    operations = [\n        migrations.AlterField(\n            model_name=\"user\",\n            name=\"fecha_nacimiento\",\n            field=models.DateField(blank=True, null=True),\n        ),", "detail": "user.migrations.0002_alter_user_fecha_nacimiento", "documentation": {}}, {"label": "Migration", "kind": 6, "importPath": "user.migrations.0003_alter_user_genero", "description": "user.migrations.0003_alter_user_genero", "peekOfCode": "class Migration(migrations.Migration):\n    dependencies = [\n        (\"user\", \"0002_alter_user_fecha_nacimiento\"),\n    ]\n    operations = [\n        migrations.AlterField(\n            model_name=\"user\",\n            name=\"genero\",\n            field=models.IntegerField(\n                blank=True, choices=[(1, \"Hombre\"), (2, \"Mujer\")], null=True", "detail": "user.migrations.0003_alter_user_genero", "documentation": {}}, {"label": "GroupAdminForm", "kind": 6, "importPath": "user.admin", "description": "user.admin", "peekOfCode": "class GroupAdminForm(forms.ModelForm):\n    \"\"\"\n    Formulario para el admin de Group que añade el campo 'users'.\n    \"\"\"\n    users = forms.ModelMultipleChoiceField(\n        queryset=User.objects.all(),\n        required=False,\n        label=\"Usuarios del grupo\",\n        widget=admin.widgets.FilteredSelectMultiple(\"Usuarios\", is_stacked=False),\n    )", "detail": "user.admin", "documentation": {}}, {"label": "GroupAdmin", "kind": 6, "importPath": "user.admin", "description": "user.admin", "peekOfCode": "class GroupAdmin(DefaultGroupAdmin):\n    \"\"\"\n    Admin personalizado para Group, muestra y guarda usuarios.\n    \"\"\"\n    form = GroupAdminForm\n    filter_horizontal = (\"permissions\",)\n    # Añadimos users al fieldsets para que aparezca en el formulario\n    fieldsets = (\n        (None, {'fields': ('name',)}),\n        ('Permisos', {'fields': ('permissions',)}),", "detail": "user.admin", "documentation": {}}, {"label": "User", "kind": 5, "importPath": "user.admin", "description": "user.admin", "peekOfCode": "User = get_user_model()\nclass GroupAdminForm(forms.ModelForm):\n    \"\"\"\n    Formulario para el admin de Group que añade el campo 'users'.\n    \"\"\"\n    users = forms.ModelMultipleChoiceField(\n        queryset=User.objects.all(),\n        required=False,\n        label=\"Usuarios del grupo\",\n        widget=admin.widgets.FilteredSelectMultiple(\"Usuarios\", is_stacked=False),", "detail": "user.admin", "documentation": {}}, {"label": "UserConfig", "kind": 6, "importPath": "user.apps", "description": "user.apps", "peekOfCode": "class UserConfig(AppConfig):\n    default_auto_field = \"django.db.models.BigAutoField\"\n    name = \"user\"", "detail": "user.apps", "documentation": {}}, {"label": "CustomUserCreationForm", "kind": 6, "importPath": "user.forms", "description": "user.forms", "peekOfCode": "class CustomUserCreationForm(UserCreationForm):\n    \"\"\"\n    Formulario para crear nuevos usuarios con campos personalizados.\n    Extiende UserCreationForm para mantener la funcionalidad de contraseñas.\n    \"\"\"\n    class Meta(UserCreationForm.Meta):\n        model = User\n        fields = ('username', 'first_name', 'last_name', 'email', 'dpi',\n                 'fecha_nacimiento', 'genero', 'cargo', 'is_supervisor')\n        widgets = {", "detail": "user.forms", "documentation": {}}, {"label": "CustomUserChangeForm", "kind": 6, "importPath": "user.forms", "description": "user.forms", "peekOfCode": "class CustomUserChangeForm(UserChangeForm):\n    \"\"\"\n    Formulario para modificar usuarios existentes.\n    No incluye cambio de contraseña (se maneja por separado).\n    \"\"\"\n    class Meta:\n        model = User\n        fields = ('first_name', 'last_name', 'email', 'dpi', 'fecha_nacimiento',\n                 'genero', 'cargo', 'is_supervisor', 'is_active')\n        widgets = {", "detail": "user.forms", "documentation": {}}, {"label": "CargoUsuarioForm", "kind": 6, "importPath": "user.forms", "description": "user.forms", "peekOfCode": "class CargoUsuarioForm(forms.ModelForm):\n    \"\"\"Formulario para gestionar cargos de usuario.\"\"\"\n    class Meta:\n        model = CargoUsuario\n        fields = ('nombre', 'descripcion', 'is_active')\n        widgets = {\n            'descripcion': forms.Textarea(attrs={'rows': 3}),\n        }\nclass CelularUsuarioForm(forms.ModelForm):\n    \"\"\"", "detail": "user.forms", "documentation": {}}, {"label": "CelularUsuarioForm", "kind": 6, "importPath": "user.forms", "description": "user.forms", "peekOfCode": "class CelularUsuarioForm(forms.ModelForm):\n    \"\"\"\n    Formulario para números telefónicos de usuarios.\n    Incluye validación de formato de número.\n    \"\"\"\n    class Meta:\n        model = CelularUsuario\n        fields = ('numero', 'tipo', 'is_active')\n    def clean_numero(self):\n        \"\"\"Valida formato del número telefónico.\"\"\"", "detail": "user.forms", "documentation": {}}, {"label": "FamiliarForm", "kind": 6, "importPath": "user.forms", "description": "user.forms", "peekOfCode": "class FamiliarForm(forms.ModelForm):\n    \"\"\"\n    Formulario para registrar familiares de usuarios.\n    Permite agregar múltiples números de emergencia.\n    \"\"\"\n    numeros_emergencia = forms.CharField(\n        widget=forms.Textarea(attrs={'rows': 2}),\n        help_text='Ingrese los números separados por comas',\n        required=False\n    )", "detail": "user.forms", "documentation": {}}, {"label": "CelularEmergenciaForm", "kind": 6, "importPath": "user.forms", "description": "user.forms", "peekOfCode": "class CelularEmergenciaForm(forms.ModelForm):\n    \"\"\"Formulario para números de emergencia individuales.\"\"\"\n    class Meta:\n        model = CelularEmergencia\n        fields = ('numero', 'is_active')\n    def clean_numero(self):\n        \"\"\"Valida formato del número de emergencia.\"\"\"\n        numero = self.cleaned_data.get('numero')\n        if not numero.isdigit() or len(numero) != 8:\n            raise ValidationError('El número debe contener 8 dígitos numéricos.')", "detail": "user.forms", "documentation": {}}, {"label": "CargoUsuario", "kind": 6, "importPath": "user.models", "description": "user.models", "peekOfCode": "class CargoUsuario(models.Model):\n    \"\"\"\n    Catálogo de cargos o puestos dentro de la organización.\n    \"\"\"\n    nombre = models.CharField(max_length=100)\n    descripcion = models.TextField(blank=True)\n    is_active = models.BooleanField(default=True)\n    class Meta:\n        db_table = \"cargo_usuario\"\n        verbose_name = \"Cargo\"", "detail": "user.models", "documentation": {}}, {"label": "User", "kind": 6, "importPath": "user.models", "description": "user.models", "peekOfCode": "class User(AbstractUser):\n    \"\"\"\n    Usuario extendido que servirá como `AUTH_USER_MODEL`.\n    \"\"\"\n    cargo = models.ForeignKey(\n        CargoUsuario,\n        on_delete=models.SET_NULL,\n        null=True,\n        blank=True,\n        related_name=\"usuarios\",", "detail": "user.models", "documentation": {}}, {"label": "CelularUsuario", "kind": 6, "importPath": "user.models", "description": "user.models", "peekOfCode": "class CelularUsuario(models.Model):\n    \"\"\"\n    Teléfonos asociados a un usuario.\n    \"\"\"\n    TIPO_CHOICES = (\n        (\"personal\", \"Personal\"),\n        (\"trabajo\", \"Trabajo\"),\n    )\n    usuario = models.ForeignKey(\n        settings.AUTH_USER_MODEL,", "detail": "user.models", "documentation": {}}, {"label": "Parentesco", "kind": 6, "importPath": "user.models", "description": "user.models", "peekOfCode": "class Parentesco(models.Model):\n    \"\"\"\n    Catálogo de tipos de parentesco para familiares.\n    \"\"\"\n    parentesco = models.CharField(max_length=100)\n    class Meta:\n        db_table = \"parentesco\"\n        verbose_name = \"Parentesco\"\n        verbose_name_plural = \"Parentescos\"\n    def __str__(self) -> str:", "detail": "user.models", "documentation": {}}, {"label": "Familiar", "kind": 6, "importPath": "user.models", "description": "user.models", "peekOfCode": "class Familiar(models.Model):\n    \"\"\"\n    Familiares de un usuario con su tipo de parentesco.\n    \"\"\"\n    usuario = models.ForeignKey(\n        settings.AUTH_USER_MODEL,\n        on_delete=models.CASCADE,\n        related_name=\"familiares\",\n    )\n    nombre = models.CharField(max_length=100)", "detail": "user.models", "documentation": {}}, {"label": "CelularEmergencia", "kind": 6, "importPath": "user.models", "description": "user.models", "peekOfCode": "class CelularEmergencia(models.Model):\n    \"\"\"\n    Teléfonos de emergencia asociados a un familiar.\n    \"\"\"\n    familiar = models.ForeignKey(\n        Familiar,\n        on_delete=models.CASCADE,\n        related_name=\"celulares_emergencia\",\n    )\n    numero = models.CharField(max_length=20)", "detail": "user.models", "documentation": {}}, {"label": "app_name", "kind": 5, "importPath": "user.urls", "description": "user.urls", "peekOfCode": "app_name = \"user\"\nurlpatterns = [\n    # Rutas principales de usuario\n    path(\"usuarios/\", views.lista_usuarios, name=\"lista_usuarios\"),\n    path(\"usuarios/crear/\", views.crear_usuario, name=\"crear_usuario\"),\n    path(\"usuarios/<int:pk>/\", views.detalle_usuario, name=\"detalle_usuario\"),\n    path(\"usuarios/<int:pk>/editar/\", views.editar_usuario, name=\"editar_usuario\"),\n    path(\"usuarios/<int:pk>/desactivar/\",views.desactivar_usuario,name=\"desactivar_usuario\",),\n    # Rutas para gestión de teléfonos\n    path(", "detail": "user.urls", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "user.urls", "description": "user.urls", "peekOfCode": "urlpatterns = [\n    # Rutas principales de usuario\n    path(\"usuarios/\", views.lista_usuarios, name=\"lista_usuarios\"),\n    path(\"usuarios/crear/\", views.crear_usuario, name=\"crear_usuario\"),\n    path(\"usuarios/<int:pk>/\", views.detalle_usuario, name=\"detalle_usuario\"),\n    path(\"usuarios/<int:pk>/editar/\", views.editar_usuario, name=\"editar_usuario\"),\n    path(\"usuarios/<int:pk>/desactivar/\",views.desactivar_usuario,name=\"desactivar_usuario\",),\n    # Rutas para gestión de teléfonos\n    path(\n        \"usuarios/<int:usuario_id>/telefonos/\",", "detail": "user.urls", "documentation": {}}, {"label": "staff_required", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def staff_required(view_func):\n    \"\"\"\n    Decorador que verifica si el usuario es staff.\n    Redirige al login si no lo es.\n    \"\"\"\n    decorated_view_func = login_required(permission_required('user.view_user', raise_exception=True)(view_func))\n    return decorated_view_func\ndef ajax_required(view_func):\n    \"\"\"Decorador que verifica si la petición es AJAX.\"\"\"\n    def wrap(request, *args, **kwargs):", "detail": "user.views", "documentation": {}}, {"label": "ajax_required", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def ajax_required(view_func):\n    \"\"\"Decorador que verifica si la petición es AJAX.\"\"\"\n    def wrap(request, *args, **kwargs):\n        if not request.headers.get('x-requested-with') == 'XMLHttpRequest':\n            return JsonResponse({'error': 'No permitido'}, status=400)\n        return view_func(request, *args, **kwargs)\n    return wrap\n# Vistas de Usuario\n@staff_required\ndef lista_usuarios(request):", "detail": "user.views", "documentation": {}}, {"label": "lista_usuarios", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def lista_usuarios(request):\n    \"\"\"\n    Lista paginada de usuarios con búsqueda y filtros.\n    Args:\n        request: HttpRequest object\n    Returns:\n        Render del template con lista de usuarios o JSON para scroll infinito\n    \"\"\"\n    queryset = User.objects.select_related('cargo').prefetch_related(\n        'celulares',", "detail": "user.views", "documentation": {}}, {"label": "detalle_usuario", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def detalle_usuario(request, pk):\n    \"\"\"\n    Muestra información detallada de un usuario específico.\n    Args:\n        request: HttpRequest object\n        pk: Primary key del usuario\n    Returns:\n        Render del template con detalles del usuario\n    \"\"\"\n    usuario = get_object_or_404(", "detail": "user.views", "documentation": {}}, {"label": "crear_usuario", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def crear_usuario(request):\n    \"\"\"\n    Crea un nuevo usuario con su información relacionada.\n    Args:\n        request: HttpRequest object\n    Returns:\n        Redirección a lista de usuarios o render del form con errores\n    \"\"\"\n    if request.method == 'POST':\n        form = CustomUserCreationForm(request.POST)", "detail": "user.views", "documentation": {}}, {"label": "editar_usuario", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def editar_usuario(request, pk):\n    \"\"\"\n    Edita información de un usuario existente.\n    Args:\n        request: HttpRequest object\n        pk: Primary key del usuario\n    Returns:\n        Redirección a detalle de usuario o render del form con errores\n    \"\"\"\n    usuario = get_object_or_404(User, pk=pk, is_active=True)", "detail": "user.views", "documentation": {}}, {"label": "desactivar_usuario", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def desactivar_usuario(request, pk):\n    \"\"\"\n    Desactiva un usuario (soft delete).\n    Args:\n        request: HttpRequest object\n        pk: Primary key del usuario\n    Returns:\n        JsonResponse con estado de la operación\n    \"\"\"\n    if request.method == 'POST':", "detail": "user.views", "documentation": {}}, {"label": "gestionar_telefonos", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def gestionar_telefonos(request, usuario_id):\n    \"\"\"\n    Gestiona los teléfonos de un usuario.\n    Args:\n        request: HttpRequest object\n        usuario_id: ID del usuario\n    Returns:\n        Render del template con forms para teléfonos o JSON response\n    \"\"\"\n    usuario = get_object_or_404(User, pk=usuario_id, is_active=True)", "detail": "user.views", "documentation": {}}, {"label": "gestionar_familiares", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def gestionar_familiares(request, usuario_id):\n    \"\"\"\n    Gestiona los familiares y sus teléfonos de emergencia.\n    Args:\n        request: HttpRequest object\n        usuario_id: ID del usuario\n    Returns:\n        Render del template con forms para familiares o JSON response\n    \"\"\"\n    usuario = get_object_or_404(User, pk=usuario_id, is_active=True)", "detail": "user.views", "documentation": {}}, {"label": "desactivar_telefono", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def desactivar_telefono(request, usuario_id, telefono_id):\n    \"\"\"\n    Desactiva un teléfono de usuario (soft delete).\n    Args:\n        request: HttpRequest object\n        usuario_id: ID del usuario\n        telefono_id: ID del teléfono\n    \"\"\"\n    if request.method == 'POST':\n        telefono = get_object_or_404(", "detail": "user.views", "documentation": {}}, {"label": "editar_familiar", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def editar_familiar(request, usuario_id, familiar_id):\n    \"\"\"\n    Edita la información de un familiar y sus teléfonos.\n    Args:\n        request: HttpRequest object\n        usuario_id: ID del usuario\n        familiar_id: ID del familiar\n    \"\"\"\n    familiar = get_object_or_404(\n        Familiar,", "detail": "user.views", "documentation": {}}, {"label": "desactivar_familiar", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def desactivar_familiar(request, usuario_id, familiar_id):\n    \"\"\"\n    Desactiva un familiar (soft delete).\n    Args:\n        request: HttpRequest object\n        usuario_id: ID del usuario\n        familiar_id: ID del familiar\n    \"\"\"\n    if request.method == 'POST':\n        familiar = get_object_or_404(", "detail": "user.views", "documentation": {}}, {"label": "lista_cargos", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def lista_cargos(request):\n    \"\"\"\n    Lista los cargos disponibles con opción de búsqueda.\n    \"\"\"\n    cargos = CargoUsuario.objects.filter(is_active=True)\n    # Búsqueda\n    busqueda = request.GET.get('q', '')\n    if busqueda:\n        cargos = cargos.filter(nombre__icontains=busqueda)\n    context = {", "detail": "user.views", "documentation": {}}, {"label": "crear_cargo", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def crear_cargo(request):\n    \"\"\"\n    Crea un nuevo cargo.\n    \"\"\"\n    if request.method == 'POST':\n        form = CargoUsuarioForm(request.POST)\n        if form.is_valid():\n            form.save()\n            messages.success(request, 'Cargo creado exitosamente.')\n            return redirect('user:lista_cargos')", "detail": "user.views", "documentation": {}}, {"label": "editar_cargo", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def editar_cargo(request, pk):\n    \"\"\"\n    Edita un cargo existente.\n    \"\"\"\n    cargo = get_object_or_404(CargoUsuario, pk=pk, is_active=True)\n    if request.method == 'POST':\n        form = CargoUsuarioForm(request.POST, instance=cargo)\n        if form.is_valid():\n            form.save()\n            messages.success(request, 'Cargo actualizado exitosamente.')", "detail": "user.views", "documentation": {}}, {"label": "desactivar_cargo", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def desactivar_cargo(request, pk):\n    \"\"\"\n    Desactiva un cargo (soft delete).\n    \"\"\"\n    if request.method == 'POST':\n        cargo = get_object_or_404(CargoUsuario, pk=pk)\n        cargo.is_active = False\n        cargo.save()\n        messages.success(request, 'Cargo desactivado exitosamente.')\n        return JsonResponse({'success': True})", "detail": "user.views", "documentation": {}}, {"label": "buscar_usuarios", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def buscar_usuarios(request):\n    \"\"\"\n    Endpoint AJAX para búsqueda dinámica de usuarios.\n    Retorna resultados en formato JSON.\n    \"\"\"\n    query = request.GET.get('q', '')\n    if len(query) < 3:\n        return JsonResponse({'results': []})\n    usuarios = User.objects.filter(\n        Q(username__icontains=query) |", "detail": "user.views", "documentation": {}}, {"label": "cambiar_estado_usuario", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def cambiar_estado_usuario(request, pk):\n    \"\"\"\n    Endpoint AJAX para cambiar el estado activo/inactivo de un usuario.\n    \"\"\"\n    if request.method == 'POST':\n        usuario = get_object_or_404(User, pk=pk)\n        estado = request.POST.get('estado')\n        if estado in ['true', 'false']:\n            usuario.is_active = estado == 'true'\n            usuario.save()", "detail": "user.views", "documentation": {}}, {"label": "lista_cargos_json", "kind": 2, "importPath": "user.views", "description": "user.views", "peekOfCode": "def lista_cargos_json(request):\n    \"\"\"\n    Endpoint AJAX que retorna lista de cargos en formato JSON.\n    Útil para select dinámicos en formularios.\n    \"\"\"\n    cargos = CargoUsuario.objects.filter(is_active=True)\n    data = [{\n        'id': cargo.id,\n        'nombre': cargo.nombre\n    } for cargo in cargos]", "detail": "user.views", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "manage", "description": "manage", "peekOfCode": "def main():\n    \"\"\"Run administrative tasks.\"\"\"\n    os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n    try:\n        from django.core.management import execute_from_command_line\n    except ImportError as exc:\n        raise ImportError(\n            \"Couldn't import Django. Are you sure it's installed and \"\n            \"available on your PYTHONPATH environment variable? Did you \"\n            \"forget to activate a virtual environment?\"", "detail": "manage", "documentation": {}}]