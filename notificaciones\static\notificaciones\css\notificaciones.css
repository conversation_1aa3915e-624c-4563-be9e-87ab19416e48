/**
 * notificaciones.css
 * Estilos específicos para el sistema de notificaciones
 */

/* ============================================================================
   ESTILOS GENERALES DE NOTIFICACIONES
   ============================================================================ */

.notification-bell {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-bell:hover {
    transform: scale(1.1);
    color: var(--accent-orange) !important;
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

.notification-indicator.has-notifications::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background-color: #dc3545;
    border-radius: 50%;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

/* ============================================================================
   ANIMACIONES
   ============================================================================ */

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ============================================================================
   LISTA DE NOTIFICACIONES
   ============================================================================ */

.notification-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-item.notification-unread {
    background-color: #fff3cd;
    border-left-color: #ffc107;
}

.notification-item.notification-read {
    background-color: #f8f9fa;
    border-left-color: #6c757d;
}

.notification-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-message {
    color: #212529;
    font-size: 0.9rem;
    line-height: 1.4;
    font-weight: 500;
}

.notification-meta {
    font-size: 0.8rem;
    color: #495057;
    margin-top: 0.5rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* ============================================================================
   BADGES Y ESTADOS
   ============================================================================ */

.badge-nueva {
    animation: pulse 2s infinite;
}

.notification-type-info {
    border-left-color: #0dcaf0;
}

.notification-type-warning {
    border-left-color: #ffc107;
}

.notification-type-success {
    border-left-color: #198754;
}

.notification-type-error {
    border-left-color: #dc3545;
}

.notification-type-ticket {
    border-left-color: #0d6efd;
}

/* ============================================================================
   DROPDOWN DE NOTIFICACIONES
   ============================================================================ */

.notifications-dropdown {
    min-width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notifications-dropdown .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    white-space: normal;
}

.notifications-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

.notifications-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

.notification-dropdown-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.notification-dropdown-icon {
    flex-shrink: 0;
    width: 20px;
    text-align: center;
}

.notification-dropdown-content {
    flex-grow: 1;
    min-width: 0;
}

.notification-dropdown-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.notification-dropdown-message {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.notification-dropdown-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

/* ============================================================================
   FORMULARIOS DE NOTIFICACIONES
   ============================================================================ */

.notification-form-container {
    max-width: 800px;
    margin: 0 auto;
}

.notification-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.notification-preview-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.notification-preview-content {
    color: #495057;
}

.user-selection-grid {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.user-selection-item {
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;
}

.user-selection-item:hover {
    background-color: #f8f9fa;
}

.user-selection-item input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* ============================================================================
   ESTADÍSTICAS Y MÉTRICAS
   ============================================================================ */

.notification-stats-card {
    transition: transform 0.2s ease;
}

.notification-stats-card:hover {
    transform: translateY(-2px);
}

.notification-progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.notification-progress-bar {
    transition: width 0.5s ease;
}

/* ============================================================================
   TOASTS PERSONALIZADOS
   ============================================================================ */

.toast-container {
    z-index: 9999;
}

.toast {
    animation: slideInRight 0.3s ease;
}

.toast-header {
    font-weight: 600;
}

.toast-body {
    font-size: 0.9rem;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
    .notifications-dropdown {
        min-width: 300px;
        max-width: 90vw;
    }
    
    .notification-item {
        padding: 1rem 0.75rem;
    }
    
    .notification-actions {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .notification-actions .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    
    .user-selection-grid {
        max-height: 200px;
    }
}

@media (max-width: 576px) {
    .notification-stats-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .notification-stats-card h4 {
        font-size: 1.5rem;
    }
    
    .notifications-dropdown {
        min-width: 280px;
    }
    
    .notification-dropdown-item {
        gap: 0.5rem;
    }
}

/* ============================================================================
   MODO OSCURO (OPCIONAL)
   ============================================================================ */

@media (prefers-color-scheme: dark) {
    .notification-item.notification-unread {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .notification-item.notification-read {
        background-color: #1a202c;
        color: #a0aec0;
    }
    
    .notification-message {
        color: #a0aec0;
    }
    
    .notification-meta {
        color: #718096;
    }
    
    .notification-preview {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .user-selection-grid {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .user-selection-item:hover {
        background-color: #4a5568;
    }
}

/* ============================================================================
   UTILIDADES ESPECÍFICAS
   ============================================================================ */

.notification-icon-lg {
    font-size: 1.5rem;
}

.notification-icon-xl {
    font-size: 2rem;
}

.notification-border-left {
    border-left: 4px solid;
}

.notification-shadow {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-shadow-hover:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.notification-fade-in {
    animation: fadeIn 0.5s ease;
}

.notification-slide-in {
    animation: slideInRight 0.3s ease;
}

/* ============================================================================
   ESTADOS DE CARGA
   ============================================================================ */

.notification-loading {
    position: relative;
    overflow: hidden;
}

.notification-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
