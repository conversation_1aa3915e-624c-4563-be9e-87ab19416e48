"""
Base/views.py
Vistas auxiliares para el sistema base.

Incluye vistas para:
- Demo del sistema de permisos
- Testing de funcionalidades
- Páginas de ayuda
"""

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from django.http import Http404
from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta
from permissions.decorators import admin_required
from permissions.core import PermissionHelper
from Base.error_handlers import log_permission_denied
from .models import UnauthorizedAccessAttempt


@login_required
def demo_permisos(request):
    """
    Vista de demostración del sistema de permisos.

    Muestra todos los permisos del usuario actual y cómo
    se comporta el sidebar dinámico.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Render del template de demo
    """
    # El context processor ya agrega user_permissions y sidebar_permissions
    # Solo necesitamos agregar información adicional si es necesaria

    context = {
        'page_title': 'Demo del Sistema de Permisos',
        'debug_info': {
            'user_areas': list(PermissionHelper.get_user_areas(request.user).values_list('name', flat=True)),
            'user_role_groups': list(PermissionHelper.get_user_role_groups(request.user).values_list('name', flat=True)),
            'all_groups': list(request.user.groups.values_list('name', flat=True)),
        }
    }

    return render(request, 'base/demo_permisos.html', context)


@login_required
def test_permissions_info(request):
    """
    Vista que muestra información detallada de permisos para debugging.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: JSON con información de permisos
    """
    user = request.user

    permissions_info = {
        'user_info': {
            'username': user.username,
            'full_name': user.get_full_name(),
            'email': user.email,
            'is_active': user.is_active,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'cargo': user.cargo.nombre if user.cargo else None,
            'is_supervisor': user.is_supervisor,
        },
        'roles': {
            'is_admin': PermissionHelper.is_admin(user),
            'is_supervisor': PermissionHelper.is_supervisor(user),
            'is_secretaria': PermissionHelper.is_secretaria(user),
            'is_empleado': PermissionHelper.is_empleado(user),
            'role': PermissionHelper.get_user_role(user),
        },
        'groups': {
            'all_groups': list(user.groups.values_list('name', flat=True)),
            'role_groups': list(PermissionHelper.get_user_role_groups(user).values_list('name', flat=True)),
            'area_groups': list(PermissionHelper.get_user_areas(user).values_list('name', flat=True)),
        },
        'ticket_permissions': {
            'can_create_tickets': PermissionHelper.can_create_tickets(user),
            'can_assign_tickets': PermissionHelper.can_assign_tickets(user),
            'can_view_all_tickets': PermissionHelper.can_view_all_tickets(user),
            'can_change_ticket_status': PermissionHelper.can_change_ticket_status(user),
            'can_delete_tickets': PermissionHelper.can_delete_tickets(user),
        },
        'user_permissions': {
            'can_manage_users': PermissionHelper.can_manage_users(user),
        },
        'sidebar_permissions': PermissionHelper.get_sidebar_permissions(user),
    }

    return JsonResponse(permissions_info, indent=2)


def test_403_error(request):
    """
    Vista para probar el manejo de errores 403.
    """
    log_permission_denied(request, "Test de error 403 - Vista de prueba")
    raise PermissionDenied("Esta es una prueba del manejo de errores 403.")


def test_404_error(request):
    """
    Vista para probar el manejo de errores 404.
    """
    raise Http404("Esta es una prueba del manejo de errores 404.")


def test_500_error(request):
    """
    Vista para probar el manejo de errores 500.
    """
    raise Exception("Esta es una prueba del manejo de errores 500.")


@login_required
def security_stats(request):
    """
    Vista de estadísticas de seguridad para administradores.
    Muestra información sobre intentos de acceso no autorizado.
    """
    # Solo administradores pueden ver estas estadísticas
    if not PermissionHelper.is_admin(request.user):
        raise PermissionDenied("Solo los administradores pueden ver las estadísticas de seguridad.")

    # Fechas para análisis
    now = timezone.now()
    last_24h = now - timedelta(hours=24)
    last_7d = now - timedelta(days=7)
    last_30d = now - timedelta(days=30)

    # Estadísticas generales
    total_attempts = UnauthorizedAccessAttempt.objects.count()
    attempts_24h = UnauthorizedAccessAttempt.objects.filter(timestamp__gte=last_24h).count()
    attempts_7d = UnauthorizedAccessAttempt.objects.filter(timestamp__gte=last_7d).count()
    attempts_30d = UnauthorizedAccessAttempt.objects.filter(timestamp__gte=last_30d).count()

    # Usuarios con más intentos (últimos 7 días)
    top_users = UnauthorizedAccessAttempt.objects.filter(
        timestamp__gte=last_7d
    ).values(
        'user__username', 'user__first_name', 'user__last_name'
    ).annotate(
        attempt_count=Count('id')
    ).order_by('-attempt_count')[:10]

    # IPs con más intentos (últimos 7 días)
    top_ips = UnauthorizedAccessAttempt.objects.filter(
        timestamp__gte=last_7d
    ).values('ip_address').annotate(
        attempt_count=Count('id')
    ).order_by('-attempt_count')[:10]

    # URLs más intentadas (últimos 7 días)
    top_urls = UnauthorizedAccessAttempt.objects.filter(
        timestamp__gte=last_7d
    ).values('url_attempted').annotate(
        attempt_count=Count('id')
    ).order_by('-attempt_count')[:10]

    # Intentos recientes (últimas 24 horas)
    recent_attempts = UnauthorizedAccessAttempt.objects.filter(
        timestamp__gte=last_24h
    ).select_related('user').order_by('-timestamp')[:20]

    # Usuarios que podrían ser deslogueados pronto
    users_at_risk = []
    for user_data in top_users:
        username = user_data['user__username']
        recent_count = UnauthorizedAccessAttempt.objects.filter(
            user__username=username,
            timestamp__gte=now - timedelta(minutes=30)
        ).count()

        if recent_count >= 2:  # 2 o más intentos en 30 minutos
            users_at_risk.append({
                'username': username,
                'full_name': f"{user_data['user__first_name']} {user_data['user__last_name']}".strip(),
                'recent_attempts': recent_count
            })

    context = {
        'stats': {
            'total_attempts': total_attempts,
            'attempts_24h': attempts_24h,
            'attempts_7d': attempts_7d,
            'attempts_30d': attempts_30d,
        },
        'top_users': top_users,
        'top_ips': top_ips,
        'top_urls': top_urls,
        'recent_attempts': recent_attempts,
        'users_at_risk': users_at_risk,
        'last_updated': now,
    }

    return render(request, 'Base/security_stats.html', context)
