from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings
from django.utils import timezone
from datetime import timedelta

# Create your models here.

class UnauthorizedAccessAttempt(models.Model):
    """
    Modelo para registrar intentos de acceso no autorizado.
    Permite implementar sistema de advertencias progresivas.
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='unauthorized_attempts')
    ip_address = models.GenericIPAddressField()
    url_attempted = models.URLField(max_length=500)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    session_key = models.CharField(max_length=40, blank=True)

    class Meta:
        db_table = 'base_unauthorized_access_attempt'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.url_attempted} - {self.timestamp}"

    @classmethod
    def get_recent_attempts_count(cls, user, minutes=30):
        """
        Obtiene el número de intentos recientes de un usuario.

        Args:
            user: Usuario a verificar
            minutes: Ventana de tiempo en minutos (default: 30)

        Returns:
            int: Número de intentos en la ventana de tiempo
        """
        since = timezone.now() - timedelta(minutes=minutes)
        return cls.objects.filter(
            user=user,
            timestamp__gte=since
        ).count()

    @classmethod
    def should_logout_user(cls, user, max_attempts=3, minutes=30):
        """
        Determina si un usuario debe ser deslogueado por exceso de intentos.

        Args:
            user: Usuario a verificar
            max_attempts: Máximo número de intentos permitidos (default: 3)
            minutes: Ventana de tiempo en minutos (default: 30)

        Returns:
            bool: True si debe ser deslogueado, False en caso contrario
        """
        return cls.get_recent_attempts_count(user, minutes) >= max_attempts
