{% extends 'Base/base.html' %}
{% block title %}Cargos{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2>Cargos</h2>
    <a href="{% url 'user:crear_cargo' %}" class="btn btn-primary">Nuevo Cargo</a>
</div>
<form class="row g-2 mb-3" method="get">
    <div class="col-md-4">
        <input type="text" name="q" class="form-control" placeholder="Buscar cargo" value="{{ busqueda }}">
    </div>
    <div class="col-md-2">
        <button type="submit" class="btn btn-outline-secondary w-100">Filtrar</button>
    </div>
</form>
<table class="table table-hover">
    <thead>
        <tr>
            <th>Nombre</th>
            <th>Descripción</th>
            <th>Acciones</th>
        </tr>
    </thead>
    <tbody>
        {% for cargo in cargos %}
        <tr>
            <td>{{ cargo.nombre }}</td>
            <td>{{ cargo.descripcion }}</td>
            <td>
                <a href="{% url 'user:editar_cargo' cargo.id %}" class="btn btn-sm btn-outline-secondary">Editar</a>
                <button class="btn btn-sm btn-outline-danger" onclick="eliminarCargo({{ cargo.id }})">Eliminar</button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="text-center">No hay cargos registrados.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<script>
function eliminarCargo(id) {
    fetch("{% url 'user:desactivar_cargo' 0 %}".replace('0', id), {
        method: 'POST',
        headers: {'X-CSRFToken': '{{ csrf_token }}'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) location.reload();
    });
}
</script>
{% endblock %}