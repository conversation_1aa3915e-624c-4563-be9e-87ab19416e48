{% for ciudadano in ciudadanos %}
    <tr id="ciudadano-{{ ciudadano.id }}">
        <td>
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <h6 class="mb-0">{{ ciudadano.nombre_completo }}</h6>
                    {% if ciudadano.email %}
                        <small class="text-muted">{{ ciudadano.email }}</small>
                    {% endif %}
                    {% if ciudadano.direccion %}
                        <div class="mt-1">
                            <small class="text-info">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ ciudadano.direccion|truncatechars:40 }}
                            </small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </td>
        <td>
            <span class="badge bg-secondary">{{ ciudadano.dpi }}</span>
        </td>
        <td>
            {% if ciudadano.telefono %}
                <span class="badge bg-info">
                    <i class="fas fa-phone me-1"></i>{{ ciudadano.telefono }}
                </span>
            {% else %}
                <span class="text-muted">
                    <i class="fas fa-phone-slash me-1"></i>Sin teléfono
                </span>
            {% endif %}
        </td>
        <td>
            <div class="d-flex align-items-center">
                {% if ciudadano.total_tickets > 0 %}
                    <span class="badge bg-success me-1">{{ ciudadano.total_tickets }}</span>
                    {% if ciudadano.tickets_activos > 0 %}
                        <small class="text-warning">
                            <i class="fas fa-clock me-1"></i>{{ ciudadano.tickets_activos }} activo{{ ciudadano.tickets_activos|pluralize }}
                        </small>
                    {% endif %}
                {% else %}
                    <span class="text-muted">
                        <i class="fas fa-ticket-alt me-1"></i>Sin tickets
                    </span>
                {% endif %}
            </div>
        </td>
        <td>
            <small class="text-muted">
                {{ ciudadano.fecha_registro|date:"d/m/Y" }}<br>
                <span class="text-xs">{{ ciudadano.fecha_registro|time:"H:i" }}</span>
            </small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <a href="{% url 'ciudadano:detalle_ciudadano' ciudadano.id %}"
                   class="btn btn-sm btn-outline-info" title="Ver detalles">
                    <i class="fas fa-eye"></i>
                </a>

                <a href="{% url 'ciudadano:editar_ciudadano' ciudadano.id %}"
                   class="btn btn-sm btn-outline-primary" title="Editar">
                    <i class="fas fa-edit"></i>
                </a>
            </div>
        </td>
    </tr>
{% endfor %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #17a2b8, #138496);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.text-xs {
    font-size: 0.75rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-weight: 500;
}

.table td {
    vertical-align: middle;
}

.table h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.table small {
    font-size: 0.8rem;
}
</style>
