{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Editar Usuario - {{ usuario.get_full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con información del usuario -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Editar Usuario</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ usuario.username }} •
                                    <i class="fas fa-briefcase me-1"></i>{{ usuario.cargo.nombre }}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'user:detalle_usuario' usuario.pk %}" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>Ver Detalles
                            </a>
                            <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-arrow-left me-2"></i>Volver
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario de edición -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Información del Usuario
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="form-editar-usuario">
                        {% csrf_token %}

                        <!-- Información Personal -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Información Personal
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    Nombres <span class="text-danger">*</span>
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    Apellidos <span class="text-danger">*</span>
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.dpi.id_for_label }}" class="form-label">
                                    DPI <span class="text-danger">*</span>
                                </label>
                                {{ form.dpi }}
                                {% if form.dpi.errors %}
                                    <div class="text-danger small">{{ form.dpi.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    Email
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.fecha_nacimiento.id_for_label }}" class="form-label">
                                    Fecha de Nacimiento <span class="text-danger">*</span>
                                </label>
                                {{ form.fecha_nacimiento }}
                                {% if form.fecha_nacimiento.errors %}
                                    <div class="text-danger small">{{ form.fecha_nacimiento.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    Género <span class="text-danger">*</span>
                                </label>
                                <div class="mt-2">
                                    {{ form.genero }}
                                </div>
                                {% if form.genero.errors %}
                                    <div class="text-danger small">{{ form.genero.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Información Laboral -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-briefcase me-2"></i>Información Laboral
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    Nombre de Usuario <span class="text-danger">*</span>
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small">{{ form.username.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.cargo.id_for_label }}" class="form-label">
                                    Cargo <span class="text-danger">*</span>
                                </label>
                                {{ form.cargo }}
                                {% if form.cargo.errors %}
                                    <div class="text-danger small">{{ form.cargo.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Estado del Usuario -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-toggle-on me-2"></i>Estado del Usuario
                                </h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        Usuario Activo
                                    </label>
                                </div>
                                <small class="text-muted">Los usuarios inactivos no pueden acceder al sistema</small>
                            </div>
                        </div>

                        <!-- Botones de acción -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'user:lista_usuarios' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancelar
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Guardar Cambios
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones Rápidas -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Acciones Rápidas
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a href="{% url 'user:gestionar_telefonos' usuario.pk %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-phone me-2"></i>Gestionar Teléfonos
                                <small class="d-block text-muted">{{ usuario.celulares.count }} registrados</small>
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="{% url 'user:gestionar_familiares' usuario.pk %}" class="btn btn-outline-success w-100">
                                <i class="fas fa-users me-2"></i>Gestionar Familiares
                                <small class="d-block text-muted">{{ usuario.familiares.count }} registrados</small>
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button class="btn btn-outline-warning w-100" onclick="resetearPassword()">
                                <i class="fas fa-key me-2"></i>Resetear Contraseña
                                <small class="d-block text-muted">Generar nueva contraseña</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ffc107, #ff8f00);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.card {
    transition: all 0.3s ease;
}

.form-control, .form-select {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.text-primary {
    color: #007bff !important;
}

.gap-2 {
    gap: 0.5rem;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-editar-usuario');

    // Agregar clases de Bootstrap a los campos del formulario
    form.querySelectorAll('input[type="text"], input[type="email"], input[type="date"], select').forEach(field => {
        if (field.type === 'checkbox') {
            field.classList.add('form-check-input');
        } else if (field.tagName === 'SELECT') {
            field.classList.add('form-select');
        } else {
            field.classList.add('form-control');
        }
    });

    // Agregar clases a radio buttons
    form.querySelectorAll('input[type="radio"]').forEach(radio => {
        radio.classList.add('form-check-input');
        const label = radio.nextElementSibling;
        if (label) {
            label.classList.add('form-check-label', 'ms-2');
        }
    });

    // Manejar envío del formulario con confirmación
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        Swal.fire({
            title: '¿Guardar cambios?',
            text: 'Se actualizará la información del usuario',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Sí, guardar',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Mostrar loading
                Swal.fire({
                    title: 'Guardando...',
                    text: 'Actualizando información del usuario',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Enviar formulario
                form.submit();
            }
        });
    });
});

function resetearPassword() {
    Swal.fire({
        title: '¿Resetear contraseña?',
        text: 'Se generará una nueva contraseña temporal para el usuario',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, resetear',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            // Implementar reseteo de contraseña
            Swal.fire({
                icon: 'info',
                title: 'Funcionalidad en desarrollo',
                text: 'El reseteo de contraseña se implementará próximamente'
            });
        }
    });
}
</script>
{% endblock %}