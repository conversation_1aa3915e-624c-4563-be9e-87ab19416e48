"""
tickets/urls.py
URLs para la gestión de tickets del sistema.

Incluye rutas para:
- Listado de tickets
- Creación de tickets
- Detalle de tickets
- Edición de tickets
- Asignación de tickets
"""

from django.urls import path
from . import views

app_name = 'tickets'

urlpatterns = [
    # Listado y dashboard de tickets
    path('', views.lista_tickets, name='lista_tickets'),
    path('dashboard/', views.dashboard_tickets, name='dashboard'),
    
    # CRUD de tickets
    path('crear/', views.crear_ticket, name='crear_ticket'),
    path('<int:ticket_id>/', views.detalle_ticket, name='detalle_ticket'),
    path('<int:ticket_id>/editar/', views.editar_ticket, name='editar_ticket'),
    path('<int:ticket_id>/cambiar-estado/', views.cambiar_estado_ticket, name='cambiar_estado'),

    # PDF y QR
    path('<int:ticket_id>/pdf/', views.generar_pdf_ticket, name='generar_pdf'),

    # Gestión de imágenes
    path('imagen/<int:imagen_id>/eliminar/', views.eliminar_imagen_ticket, name='eliminar_imagen'),
    
   

    
    # AJAX endpoints
    path('ajax/filtrar/', views.filtrar_tickets_ajax, name='filtrar_ajax'),
    path('ajax/buscar/', views.buscar_tickets_ajax, name='buscar_ajax'),
    path('ajax/cargar-mas/', views.cargar_mas_tickets, name='cargar_mas'),
    
    # Reportes y estadísticas
    path('reportes/', views.reportes_tickets, name='reportes'),
    path('estadisticas/', views.estadisticas_tickets, name='estadisticas'),
]
