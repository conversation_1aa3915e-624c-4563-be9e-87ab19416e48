{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Lista de Tickets{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con estadísticas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Gestión de Tickets</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-list me-1"></i>{{ total_tickets }} ticket{{ total_tickets|pluralize }} total{{ total_tickets|pluralize:"es" }}
                                    <span class="ms-3">
                                        <i class="fas fa-user me-1"></i>{{ rol_usuario }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            {% if puede_crear_ticket %}
                                <a href="{% url 'tickets:crear_ticket' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Nuevo Ticket
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros y búsqueda -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="q" class="form-label">Buscar Ticket</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="q" name="q" 
                                       value="{{ filtros_aplicados.busqueda }}" 
                                       placeholder="Título, descripción, ID...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="estado" class="form-label">Estado</label>
                            <select class="form-select" id="estado" name="estado">
                                <option value="">Todos</option>
                                <option value="1" {% if filtros_aplicados.estado == "1" %}selected{% endif %}>Abierto</option>
                                <option value="2" {% if filtros_aplicados.estado == "2" %}selected{% endif %}>En Progreso</option>
                                <option value="3" {% if filtros_aplicados.estado == "3" %}selected{% endif %}>Cerrado</option>
                                <option value="4" {% if filtros_aplicados.estado == "4" %}selected{% endif %}>Pendiente</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="prioridad" class="form-label">Prioridad</label>
                            <select class="form-select" id="prioridad" name="prioridad">
                                <option value="">Todas</option>
                                <option value="baja" {% if filtros_aplicados.prioridad == "baja" %}selected{% endif %}>Baja</option>
                                <option value="media" {% if filtros_aplicados.prioridad == "media" %}selected{% endif %}>Media</option>
                                <option value="alta" {% if filtros_aplicados.prioridad == "alta" %}selected{% endif %}>Alta</option>
                                <option value="critica" {% if filtros_aplicados.prioridad == "critica" %}selected{% endif %}>Crítica</option>
                            </select>
                        </div>
                        {% if areas_disponibles %}
                        <div class="col-md-2">
                            <label for="area" class="form-label">Área</label>
                            <select class="form-select" id="area" name="area">
                                <option value="">Todas</option>
                                {% for area in areas_disponibles %}
                                    <option value="{{ area.id }}" 
                                            {% if filtros_aplicados.area == area.id|stringformat:"s" %}selected{% endif %}>
                                        {{ area.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>Filtrar
                            </button>
                            <a href="{% url 'tickets:lista_tickets' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Limpiar
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de tickets -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Lista de Tickets
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if tickets %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="80">#</th>
                                        <th>Título</th>
                                        <th width="120">Estado</th>
                                        <th width="100">Prioridad</th>
                                        <th width="150">Área</th>
                                        <th width="120">Creado</th>
                                        <th width="150">Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="tickets-container">
                                    {% include 'tickets/parciales/lista_tickets.html' %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Loading indicator -->
                        <div id="loading-indicator" class="text-center py-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2 text-muted">Cargando más tickets...</p>
                        </div>

                        <!-- Paginación -->
                        {% if tickets.has_next %}
                            <div class="card-footer bg-white border-top">
                                <div class="text-center">
                                    <button id="load-more" class="btn btn-outline-primary" data-page="{{ tickets.next_page_number }}">
                                        <i class="fas fa-chevron-down me-2"></i>Cargar más tickets
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay tickets disponibles</h5>
                            <p class="text-muted">
                                {% if puede_crear_ticket %}
                                    Crea tu primer ticket para comenzar
                                {% else %}
                                    No tienes tickets asignados en este momento
                                {% endif %}
                            </p>
                            {% if puede_crear_ticket %}
                                <a href="{% url 'tickets:crear_ticket' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Crear Primer Ticket
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.table tbody tr {
    transition: background-color 0.1s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

#loading-indicator {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.getElementById('load-more');
    const loadingIndicator = document.getElementById('loading-indicator');
    const ticketsContainer = document.getElementById('tickets-container');
    
    // Scroll infinito optimizado
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const page = this.dataset.page;
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            
            // Mostrar loading
            loadingIndicator.style.display = 'block';
            this.style.display = 'none';
            
            fetch(url.toString(), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Agregar nuevos tickets
                ticketsContainer.insertAdjacentHTML('beforeend', data.html);
                
                // Ocultar loading
                loadingIndicator.style.display = 'none';
                
                // Actualizar botón o ocultarlo
                if (data.has_next) {
                    this.dataset.page = parseInt(page) + 1;
                    this.style.display = 'block';
                } else {
                    this.parentElement.parentElement.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.style.display = 'none';
                this.style.display = 'block';
                
                alert('Error al cargar más tickets');
            });
        });
    }
});
</script>
{% endblock %}
