{% extends 'reportes/base_reportes.html' %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">General</li>
{% endblock %}

{% block page_title %}Reportes Generales{% endblock %}
{% block page_description %}Genere reportes completos del sistema con información detallada{% endblock %}

{% block report_content %}
<div class="row">
    <div class="col-12">
        <form class="report-form" action="{% url 'reportes:generar_reporte_general' %}" method="post">
            {% csrf_token %}
            
            <!-- Información del Reporte -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-chart-bar me-2"></i>Reporte General del Sistema
                </h5>
                
                <div class="alert alert-primary">
                    <h6><i class="fas fa-info-circle me-2"></i>Descripción del Reporte</h6>
                    <p class="mb-0">
                        El reporte general incluye <strong>todos los tickets del sistema</strong> organizados por estado,
                        con información completa de cada ticket incluyendo empleado asignado, área responsable y ciudadano solicitante.
                    </p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-file-pdf text-danger" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Formato PDF</h6>
                                <p class="small text-muted">
                                    Formato horizontal con tablas extendidas para mayor información por ticket.
                                    Ideal para visualización completa.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Formato Excel</h6>
                                <p class="small text-muted">
                                    Hojas separadas por estado con resumen general.
                                    Ideal para análisis de datos y filtrado.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filtros de Fecha -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-calendar-alt me-2"></i>Filtros de Fecha
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <label for="fecha_inicio" class="form-label">Fecha de Inicio</label>
                        <input type="date" name="fecha_inicio" id="fecha_inicio" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir desde el inicio del sistema.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="fecha_fin" class="form-label">Fecha de Fin</label>
                        <input type="date" name="fecha_fin" id="fecha_fin" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir hasta la fecha actual.</div>
                    </div>
                </div>
                
                <!-- Botones de fecha rápida -->
                <div class="row mt-3">
                    <div class="col-12">
                        <label class="form-label">Filtros Rápidos</label>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="setFechaRapida('hoy')">
                                <i class="fas fa-calendar-day me-1"></i>Hoy
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="setFechaRapida('semana')">
                                <i class="fas fa-calendar-week me-1"></i>Esta Semana
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="setFechaRapida('mes')">
                                <i class="fas fa-calendar-alt me-1"></i>Este Mes
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="limpiarFechas()">
                                <i class="fas fa-eraser me-1"></i>Limpiar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Formato del Reporte -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-export me-2"></i>Formato del Reporte
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_pdf" value="pdf" checked>
                            <label class="form-check-label" for="formato_pdf">
                                <i class="fas fa-file-pdf text-danger me-2"></i>PDF (Formato Horizontal)
                            </label>
                        </div>
                        <div class="form-text">
                            Formato horizontal con más columnas de información. 
                            Incluye descripción, asignado y ciudadano en cada ticket.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_excel" value="excel">
                            <label class="form-check-label" for="formato_excel">
                                <i class="fas fa-file-excel text-success me-2"></i>Excel (Hojas Separadas)
                            </label>
                        </div>
                        <div class="form-text">
                            Hoja de resumen general + una hoja por cada estado.
                            Ideal para análisis detallado y filtrado de datos.
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Botones de Acción -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-generate me-3">
                    <i class="fas fa-download me-2"></i>Generar Reporte General
                </button>
                <a href="{% url 'reportes:index' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Información detallada -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>Contenido del Reporte
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Resumen general con conteo por estados</li>
                    <li>Listado completo de tickets abiertos</li>
                    <li>Listado completo de tickets en progreso</li>
                    <li>Listado completo de tickets cerrados</li>
                    <li>Listado completo de tickets pendientes</li>
                    <li>Información completa de cada ticket</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Consideraciones
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>El reporte puede ser extenso dependiendo del rango de fechas</li>
                    <li>Se recomienda usar filtros de fecha para reportes específicos</li>
                    <li>El formato Excel permite mejor manipulación de datos</li>
                    <li>El formato PDF es mejor para presentaciones</li>
                    <li>La generación puede tomar unos minutos</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_report_js %}
<script>
function setFechaRapida(periodo) {
    const hoy = new Date();
    const fechaHoy = hoy.toISOString().split('T')[0];
    
    switch(periodo) {
        case 'hoy':
            $('#fecha_inicio').val(fechaHoy);
            $('#fecha_fin').val(fechaHoy);
            break;
            
        case 'semana':
            const inicioSemana = new Date(hoy);
            inicioSemana.setDate(hoy.getDate() - hoy.getDay());
            $('#fecha_inicio').val(inicioSemana.toISOString().split('T')[0]);
            $('#fecha_fin').val(fechaHoy);
            break;
            
        case 'mes':
            const inicioMes = new Date(hoy.getFullYear(), hoy.getMonth(), 1);
            $('#fecha_inicio').val(inicioMes.toISOString().split('T')[0]);
            $('#fecha_fin').val(fechaHoy);
            break;
    }
}

function limpiarFechas() {
    $('#fecha_inicio, #fecha_fin').val('');
}
</script>
{% endblock %}
