{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Crear Usuario - Paso 1{% endblock %}

{% block content %}
<style>
    :root {
        --primary-color: #1A237E;
        --secondary-color: #283593;
        --success-color: #28a745;
        --light-gray: #e9ecef;
        --dark-gray: #6c757d;
    }

    .progress-container {
        margin-bottom: 3rem;
        padding: 0 2rem;
    }

    .progress-bar-custom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        margin-bottom: 1rem;
    }

    .progress-line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 4px;
        background-color: var(--light-gray);
        z-index: 1;
        border-radius: 2px;
    }

    .progress-line-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
        transition: width 0.3s ease;
    }

    .step-item {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: white;
        padding: 0.5rem;
    }

    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        border: 3px solid var(--light-gray);
        background: white;
        color: var(--dark-gray);
    }

    .step-item.active .step-circle {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
    }

    .step-item.completed .step-circle {
        background: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }

    .step-label {
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
        color: var(--dark-gray);
        transition: color 0.3s ease;
    }

    .step-item.active .step-label {
        color: var(--primary-color);
        font-weight: 600;
    }

    .step-item.completed .step-label {
        color: var(--success-color);
        font-weight: 600;
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 2.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
        border: 1px solid rgba(26, 35, 126, 0.1);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .progress-container {
            padding: 0 1rem;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .step-label {
            font-size: 0.8rem;
        }

        .form-section {
            padding: 1.5rem;
        }
    }
</style>



<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Barra de progreso moderna -->
            <div class="progress-container">
                <div class="progress-bar-custom">
                    <div class="progress-line">
                        <div class="progress-line-fill" style="width: 33.33%;"></div>
                    </div>

                    <div class="step-item active">
                        <div class="step-circle">1</div>
                        <div class="step-label">Información<br>Básica</div>
                    </div>

                    <div class="step-item">
                        <div class="step-circle">2</div>
                        <div class="step-label">Teléfonos</div>
                    </div>

                    <div class="step-item">
                        <div class="step-circle">3</div>
                        <div class="step-label">Familiares</div>
                    </div>
                </div>
            </div>

            <!-- Formulario -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="form-section">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3><i class="fas fa-user-plus me-2"></i>Crear Nuevo Usuario</h3>
                            <div class="badge bg-primary">Paso {{ paso_actual }} de {{ total_pasos }}</div>
                        </div>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show">
                                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            {% crispy form %}
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación del formulario
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Validación en tiempo real del DPI
    const dpiField = document.getElementById('id_dpi');
    if (dpiField) {
        dpiField.addEventListener('input', function() {
            const value = this.value.replace(/\D/g, ''); // Solo números
            this.value = value;

            if (value.length === 13) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                if (value.length > 0) {
                    this.classList.add('is-invalid');
                }
            }
        });
    }

    // Validación del nombre de usuario
    const usernameField = document.getElementById('id_username');
    if (usernameField) {
        usernameField.addEventListener('input', function() {
            const value = this.value;
            const regex = /^[a-zA-Z0-9_.-]+$/;

            if (value.length >= 3 && regex.test(value)) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                if (value.length > 0) {
                    this.classList.add('is-invalid');
                }
            }
        });
    }

    // Validación de contraseñas
    const password1 = document.getElementById('id_password1');
    const password2 = document.getElementById('id_password2');

    if (password1 && password2) {
        function validatePasswords() {
            if (password1.value && password2.value) {
                if (password1.value === password2.value) {
                    password2.classList.remove('is-invalid');
                    password2.classList.add('is-valid');
                } else {
                    password2.classList.remove('is-valid');
                    password2.classList.add('is-invalid');
                }
            }
        }

        password1.addEventListener('input', validatePasswords);
        password2.addEventListener('input', validatePasswords);
    }
});
</script>
{% endblock %}
