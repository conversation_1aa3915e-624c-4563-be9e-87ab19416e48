"""
notificaciones/admin.py
Configuración del admin de Django para el sistema de notificaciones.

Incluye:
- Admin personalizado para Notificacion
- Admin para NotificacionUsuario con filtros
- Admin para NotificacionGrupo
- Acciones personalizadas para envío masivo
"""

from django.contrib import admin
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Q
from django.contrib import messages
from .models import Notificacion, NotificacionUsuario, NotificacionGrupo

User = get_user_model()


class NotificacionUsuarioInline(admin.TabularInline):
    """
    Inline para mostrar usuarios notificados en el admin de Notificacion.
    """
    model = NotificacionUsuario
    extra = 0
    readonly_fields = ('fecha_envio', 'fecha_lectura', 'leida')
    fields = ('usuario', 'fecha_envio', 'leida', 'fecha_lectura')

    def has_add_permission(self, request, obj=None):
        return False


class NotificacionGrupoInline(admin.TabularInline):
    """
    Inline para mostrar grupos notificados en el admin de Notificacion.
    """
    model = NotificacionGrupo
    extra = 0
    readonly_fields = ('fecha_envio',)
    fields = ('grupo', 'fecha_envio')

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(Notificacion)
class NotificacionAdmin(admin.ModelAdmin):
    """
    Admin personalizado para el modelo Notificacion.

    Incluye filtros, búsqueda, acciones personalizadas y
    funcionalidad para envío a usuarios y grupos.
    """

    list_display = (
        'id',
        'mensaje_corto',
        'tipo_badge',
        'ticket_link',
        'creado_por',
        'fecha_creacion',
        'usuarios_notificados_count',
        'grupos_notificados_count',
        'is_active'
    )

    list_filter = (
        'tipo',
        'is_active',
        'fecha_creacion',
        'creado_por',
    )

    search_fields = (
        'mensaje',
        'titulo',
        'creado_por__username',
        'creado_por__first_name',
        'creado_por__last_name',
    )

    readonly_fields = (
        'fecha_creacion',
        'usuarios_notificados_count',
        'grupos_notificados_count'
    )

    fieldsets = (
        ('Información Principal', {
            'fields': ('titulo', 'mensaje', 'tipo', 'is_active')
        }),
        ('Relaciones', {
            'fields': ('ticket', 'url_accion')
        }),
        ('Auditoría', {
            'fields': ('creado_por', 'fecha_creacion'),
            'classes': ('collapse',)
        }),
        ('Estadísticas', {
            'fields': ('usuarios_notificados_count', 'grupos_notificados_count'),
            'classes': ('collapse',)
        }),
    )

    inlines = [NotificacionUsuarioInline, NotificacionGrupoInline]

    actions = [
        'enviar_a_usuarios_seleccionados',
        'enviar_a_grupos_seleccionados',
        'marcar_como_activa',
        'marcar_como_inactiva'
    ]

    def mensaje_corto(self, obj):
        """Muestra una versión corta del mensaje."""
        return obj.mensaje[:50] + '...' if len(obj.mensaje) > 50 else obj.mensaje
    mensaje_corto.short_description = 'Mensaje'

    def tipo_badge(self, obj):
        """Muestra el tipo como badge con color."""
        color = obj.get_tipo_display_color()
        return format_html(
            '<span class="badge badge-{}">{}</span>',
            color,
            obj.get_tipo_display()
        )
    tipo_badge.short_description = 'Tipo'

    def ticket_link(self, obj):
        """Muestra enlace al ticket si existe."""
        if obj.ticket:
            url = reverse('admin:tickets_ticket_change', args=[obj.ticket.id])
            return format_html('<a href="{}">{}</a>', url, obj.ticket)
        return '-'
    ticket_link.short_description = 'Ticket'

    def usuarios_notificados_count(self, obj):
        """Cuenta de usuarios notificados."""
        return obj.usuarios_notificados.count()
    usuarios_notificados_count.short_description = 'Usuarios Notificados'

    def grupos_notificados_count(self, obj):
        """Cuenta de grupos notificados."""
        return obj.grupos_notificados.count()
    grupos_notificados_count.short_description = 'Grupos Notificados'

    def save_model(self, request, obj, form, change):
        """Asigna el usuario creador automáticamente."""
        if not change:  # Solo en creación
            obj.creado_por = request.user
        super().save_model(request, obj, form, change)

    def enviar_a_usuarios_seleccionados(self, request, queryset):
        """
        Acción para enviar notificaciones a usuarios seleccionados.
        """
        # Esta acción se implementará con un formulario personalizado
        # Por ahora, mostramos un mensaje informativo
        self.message_user(
            request,
            "Para enviar notificaciones a usuarios específicos, use el formulario de creación manual.",
            messages.INFO
        )
    enviar_a_usuarios_seleccionados.short_description = "Enviar a usuarios seleccionados"

    def enviar_a_grupos_seleccionados(self, request, queryset):
        """
        Acción para enviar notificaciones a grupos seleccionados.
        """
        # Esta acción se implementará con un formulario personalizado
        # Por ahora, mostramos un mensaje informativo
        self.message_user(
            request,
            "Para enviar notificaciones a grupos específicos, use el formulario de creación manual.",
            messages.INFO
        )
    enviar_a_grupos_seleccionados.short_description = "Enviar a grupos seleccionados"

    def marcar_como_activa(self, request, queryset):
        """Marca las notificaciones seleccionadas como activas."""
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f'{updated} notificación(es) marcada(s) como activa(s).',
            messages.SUCCESS
        )
    marcar_como_activa.short_description = "Marcar como activa"

    def marcar_como_inactiva(self, request, queryset):
        """Marca las notificaciones seleccionadas como inactivas."""
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f'{updated} notificación(es) marcada(s) como inactiva(s).',
            messages.SUCCESS
        )
    marcar_como_inactiva.short_description = "Marcar como inactiva"


@admin.register(NotificacionUsuario)
class NotificacionUsuarioAdmin(admin.ModelAdmin):
    """
    Admin para NotificacionUsuario con filtros y búsqueda optimizada.
    """

    list_display = (
        'id',
        'notificacion_mensaje',
        'usuario_nombre',
        'fecha_envio',
        'leida_badge',
        'fecha_lectura'
    )

    list_filter = (
        'leida',
        'fecha_envio',
        'fecha_lectura',
        'notificacion__tipo',
        'usuario__groups'
    )

    search_fields = (
        'notificacion__mensaje',
        'usuario__username',
        'usuario__first_name',
        'usuario__last_name'
    )

    readonly_fields = ('fecha_envio', 'fecha_lectura')

    def notificacion_mensaje(self, obj):
        """Muestra el mensaje de la notificación."""
        mensaje = obj.notificacion.mensaje
        return mensaje[:40] + '...' if len(mensaje) > 40 else mensaje
    notificacion_mensaje.short_description = 'Mensaje'

    def usuario_nombre(self, obj):
        """Muestra el nombre completo del usuario."""
        return obj.usuario.get_full_name() or obj.usuario.username
    usuario_nombre.short_description = 'Usuario'

    def leida_badge(self, obj):
        """Muestra el estado de lectura como badge."""
        if obj.leida:
            return format_html('<span class="badge badge-success">Leída</span>')
        else:
            return format_html('<span class="badge badge-warning">No leída</span>')
    leida_badge.short_description = 'Estado'

    actions = ['marcar_como_leida', 'marcar_como_no_leida']

    def marcar_como_leida(self, request, queryset):
        """Marca las notificaciones seleccionadas como leídas."""
        updated = 0
        for notif in queryset.filter(leida=False):
            notif.marcar_como_leida()
            updated += 1

        self.message_user(
            request,
            f'{updated} notificación(es) marcada(s) como leída(s).',
            messages.SUCCESS
        )
    marcar_como_leida.short_description = "Marcar como leída"

    def marcar_como_no_leida(self, request, queryset):
        """Marca las notificaciones seleccionadas como no leídas."""
        updated = queryset.update(leida=False, fecha_lectura=None)
        self.message_user(
            request,
            f'{updated} notificación(es) marcada(s) como no leída(s).',
            messages.SUCCESS
        )
    marcar_como_no_leida.short_description = "Marcar como no leída"


@admin.register(NotificacionGrupo)
class NotificacionGrupoAdmin(admin.ModelAdmin):
    """
    Admin para NotificacionGrupo con funcionalidad de expansión a usuarios.
    """

    list_display = (
        'id',
        'notificacion_mensaje',
        'grupo_nombre',
        'fecha_envio',
        'miembros_count',
        'notificaciones_creadas'
    )

    list_filter = (
        'fecha_envio',
        'grupo',
        'notificacion__tipo'
    )

    search_fields = (
        'notificacion__mensaje',
        'grupo__name'
    )

    readonly_fields = ('fecha_envio', 'miembros_count', 'notificaciones_creadas')

    def notificacion_mensaje(self, obj):
        """Muestra el mensaje de la notificación."""
        mensaje = obj.notificacion.mensaje
        return mensaje[:40] + '...' if len(mensaje) > 40 else mensaje
    notificacion_mensaje.short_description = 'Mensaje'

    def grupo_nombre(self, obj):
        """Muestra el nombre del grupo."""
        return obj.grupo.name
    grupo_nombre.short_description = 'Grupo'

    def miembros_count(self, obj):
        """Cuenta los miembros activos del grupo."""
        return obj.grupo.user_set.filter(is_active=True).count()
    miembros_count.short_description = 'Miembros Activos'

    def notificaciones_creadas(self, obj):
        """Cuenta las notificaciones individuales creadas."""
        return NotificacionUsuario.objects.filter(
            notificacion=obj.notificacion,
            usuario__groups=obj.grupo
        ).count()
    notificaciones_creadas.short_description = 'Notif. Individuales'

    actions = ['crear_notificaciones_individuales']

    def crear_notificaciones_individuales(self, request, queryset):
        """
        Crea notificaciones individuales para todos los miembros de los grupos seleccionados.
        """
        total_creadas = 0

        for notif_grupo in queryset:
            creadas = notif_grupo.crear_notificaciones_individuales()
            total_creadas += creadas

        self.message_user(
            request,
            f'{total_creadas} notificación(es) individual(es) creada(s).',
            messages.SUCCESS
        )
    crear_notificaciones_individuales.short_description = "Crear notificaciones individuales"


# Personalización adicional del admin site
admin.site.site_header = "Sistema de Tickets - Administración"
admin.site.site_title = "Admin Tickets"
admin.site.index_title = "Panel de Administración"
