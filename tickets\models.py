"""
tickets/models.py
Modelos principales para la gestión de tickets del sistema.

Incluye:
- Ticket: Modelo principal de tickets
- HistorialTicket: Auditoría y seguimiento de cambios
"""

from django.db import models
from django.contrib.auth.models import Group
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinLengthValidator, FileExtensionValidator
from django.core.files.storage import default_storage
from PIL import Image
import json
import uuid
import os


class Ticket(models.Model):
    """
    Modelo principal para los tickets del sistema.

    Incluye validaciones, índices optimizados y métodos
    para gestión completa del ciclo de vida del ticket.
    """

    ESTADO_CHOICES = (
        (1, 'Abierto'),
        (2, 'En Progreso'),
        (3, 'Cerrado'),
        (4, 'Pendiente'),
    )

    PRIORIDAD_CHOICES = (
        ('baja', 'Baja'),
        ('media', 'Media'),
        ('alta', 'Alta'),
        ('critica', 'Crítica'),
    )

    # Campos principales
    titulo = models.CharField(
        max_length=200,
        validators=[MinLengthValidator(5)],
        help_text='Título descriptivo del ticket (mínimo 5 caracteres)',
        db_index=True
    )
    descripcion = models.TextField(
        validators=[MinLengthValidator(10)],
        help_text='Descripción detallada del problema o solicitud'
    )
    estado = models.IntegerField(
        choices=ESTADO_CHOICES,
        default=1,
        help_text='Estado actual del ticket',
        db_index=True
    )
    prioridad = models.CharField(
        max_length=50,
        choices=PRIORIDAD_CHOICES,
        default='media',
        help_text='Prioridad del ticket',
        db_index=True
    )

    # Campos de fechas
    fecha_creacion = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de creación del ticket',
        db_index=True
    )
    fecha_finalizacion = models.DateTimeField(
        blank=True,
        null=True,
        help_text='Fecha y hora de finalización del ticket'
    )
    fecha_actualizacion = models.DateTimeField(
        auto_now=True,
        help_text='Última fecha de actualización'
    )

    # Relaciones
    creado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='tickets_creados',
        help_text='Usuario que creó el ticket'
    )
    grupo = models.ForeignKey(
        Group,
        on_delete=models.PROTECT,
        related_name='tickets_asignados',
        help_text='Grupo responsable del ticket',
        db_index=True
    )

    # Información adicional
    direccion = models.TextField(
        blank=True,
        help_text='Dirección específica donde se requiere el servicio'
    )

    # Campo de control
    is_active = models.BooleanField(
        default=True,
        help_text='Indica si el ticket está activo',
        db_index=True
    )

    # Token único para acceso público
    token = models.CharField(
        max_length=36,
        unique=True,
        editable=False,
        help_text='Token único para acceso público al ticket',
        db_index=True
    )

    class Meta:
        db_table = 'ticket'
        verbose_name = 'Ticket'
        verbose_name_plural = 'Tickets'
        ordering = ['-fecha_creacion']
        indexes = [
            models.Index(fields=['estado', 'prioridad'], name='idx_ticket_estado_prioridad'),
            models.Index(fields=['grupo', 'estado'], name='idx_ticket_grupo_estado'),
            models.Index(fields=['creado_por', 'fecha_creacion'], name='idx_ticket_creador_fecha'),
            models.Index(fields=['fecha_creacion'], name='idx_ticket_fecha_creacion'),
            models.Index(fields=['is_active'], name='idx_ticket_activo'),
        ]

    def save(self, *args, **kwargs):
        """
        Sobrescribe el método save para generar token automáticamente
        y sincronizar estados de asignaciones.
        """
        if not self.token:
            self.token = str(uuid.uuid4())

        # Verificar si el estado cambió
        estado_cambio = False
        if self.pk:
            try:
                ticket_anterior = Ticket.objects.get(pk=self.pk)
                if ticket_anterior.estado != self.estado:
                    estado_cambio = True
            except Ticket.DoesNotExist:
                pass

        super().save(*args, **kwargs)

        # Sincronizar estados de asignaciones si el estado del ticket cambió
        if estado_cambio:
            self._sincronizar_estados_asignaciones()

    def _sincronizar_estados_asignaciones(self):
        """
        Sincroniza los estados de las asignaciones con el estado del ticket.

        Mapeo de estados:
        - Ticket 1 (Abierto) -> Asignación 1 (Asignado)
        - Ticket 2 (En Progreso) -> Asignación 2 (En Progreso)
        - Ticket 3 (Cerrado) -> Asignación 3 (Finalizado)
        - Ticket 4 (Pendiente) -> Asignación 1 (Asignado)
        """
        # Mapeo de estados ticket -> asignación
        mapeo_estados = {
            1: 1,    # Abierto -> Asignado
            2: 2,    # En Progreso -> En Progreso
            3: 3,    # Cerrado -> Finalizado
            4: 1,    # Pendiente -> Asignado
        }

        nuevo_estado_asignacion = mapeo_estados.get(self.estado)

        if nuevo_estado_asignacion:
            # Actualizar todas las asignaciones activas del ticket
            asignaciones_actualizadas = self.asignaciones.filter(is_active=True).update(
                estado=nuevo_estado_asignacion
            )

            # Si el ticket se finaliza o cancela, actualizar fecha_finalizacion
            if nuevo_estado_asignacion in [3, 4]:  # Finalizado o Cancelado
                self.asignaciones.filter(
                    is_active=True,
                    fecha_finalizacion__isnull=True
                ).update(
                    fecha_finalizacion=timezone.now()
                )

            # Si el ticket pasa a en_progreso, actualizar fecha_inicio si no existe
            elif nuevo_estado_asignacion == 2:  # En Progreso
                self.asignaciones.filter(
                    is_active=True,
                    fecha_inicio__isnull=True
                ).update(
                    fecha_inicio=timezone.now()
                )

    def __str__(self):
        return f"#{self.id} - {self.titulo}"

    def get_estado_display_color(self):
        """
        Retorna el color CSS asociado al estado del ticket.

        Returns:
            str: Clase CSS para el color del estado
        """
        colors = {
            1: 'primary',    # Abierto - Azul
            2: 'warning',    # En Progreso - Amarillo
            3: 'success',    # Cerrado - Verde
            4: 'secondary',  # Pendiente - Gris
        }
        return colors.get(self.estado, 'secondary')

    def get_prioridad_display_color(self):
        """
        Retorna el color CSS asociado a la prioridad del ticket.

        Returns:
            str: Clase CSS para el color de la prioridad
        """
        colors = {
            'baja': 'success',
            'media': 'info',
            'alta': 'warning',
            'critica': 'danger',
        }
        return colors.get(self.prioridad, 'secondary')

    def get_tiempo_transcurrido(self):
        """
        Calcula el tiempo transcurrido desde la creación del ticket.

        Returns:
            timedelta: Tiempo transcurrido
        """
        if self.fecha_finalizacion:
            return self.fecha_finalizacion - self.fecha_creacion
        return timezone.now() - self.fecha_creacion

    def get_asignaciones_activas(self):
        """
        Obtiene las asignaciones activas del ticket.

        Returns:
            QuerySet: Asignaciones activas
        """
        return self.asignaciones.filter(is_active=True)

    def get_ciudadano(self):
        """
        Obtiene el ciudadano asociado al ticket.

        Returns:
            Ciudadano: Ciudadano asociado o None
        """
        ciudadano_ticket = self.ciudadanos.first()
        return ciudadano_ticket.ciudadano if ciudadano_ticket else None

    def puede_ser_editado_por(self, usuario):
        """
        Verifica si un usuario puede editar este ticket.

        Args:
            usuario: Usuario a verificar

        Returns:
            bool: True si puede editar, False en caso contrario
        """
        # Admin puede editar cualquier ticket
        if usuario.groups.filter(name='Admin').exists():
            return True

        # Secretaria puede editar tickets que creó
        if usuario.groups.filter(name='Secretaria').exists():
            return self.creado_por == usuario

        # Supervisor puede editar tickets de su grupo
        if usuario.is_supervisor and usuario.groups.filter(id=self.grupo.id).exists():
            return True

        # Empleado puede editar solo el estado de tickets asignados a él
        if self.asignaciones.filter(usuario=usuario, is_active=True).exists():
            return True

        return False

    def finalizar(self, usuario=None):
        """
        Finaliza el ticket estableciendo la fecha de finalización.

        Args:
            usuario: Usuario que finaliza el ticket
        """
        self.estado = 3  # Cerrado
        self.fecha_finalizacion = timezone.now()
        self.save()

        # Registrar en historial
        if usuario:
            HistorialTicket.objects.create(
                ticket=self,
                usuario=usuario,
                accion='Ticket finalizado',
                detalles={'estado_anterior': self.estado, 'fecha_finalizacion': self.fecha_finalizacion.isoformat()}
            )


class HistorialTicket(models.Model):
    """
    Modelo para el historial y auditoría de cambios en tickets.

    Registra automáticamente todos los cambios realizados en los tickets
    para mantener una trazabilidad completa del sistema.
    """

    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name='historial',
        help_text='Ticket al que pertenece este registro de historial'
    )
    usuario = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='historial_tickets',
        help_text='Usuario que realizó la acción'
    )
    accion = models.CharField(
        max_length=200,
        help_text='Descripción de la acción realizada',
        db_index=True
    )
    detalles = models.JSONField(
        default=dict,
        help_text='Detalles adicionales de la acción en formato JSON'
    )
    fecha = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de la acción',
        db_index=True
    )

    class Meta:
        db_table = 'historial_ticket'
        verbose_name = 'Historial de Ticket'
        verbose_name_plural = 'Historial de Tickets'
        ordering = ['-fecha']
        indexes = [
            models.Index(fields=['ticket', 'fecha'], name='idx_historial_ticket_fecha'),
            models.Index(fields=['usuario', 'fecha'], name='idx_historial_usuario_fecha'),
            models.Index(fields=['accion'], name='idx_historial_accion'),
        ]

    def __str__(self):
        usuario_str = self.usuario.username if self.usuario else 'Sistema'
        return f"Ticket #{self.ticket.id} - {self.accion} por {usuario_str}"

    def get_detalles_formateados(self):
        """
        Retorna los detalles en formato legible.

        Returns:
            str: Detalles formateados para mostrar
        """
        if not self.detalles:
            return "Sin detalles adicionales"

        try:
            # Formatear detalles comunes
            formatted = []
            for key, value in self.detalles.items():
                if key == 'estado_anterior':
                    estado_display = dict(Ticket.ESTADO_CHOICES).get(value, value)
                    formatted.append(f"Estado anterior: {estado_display}")
                elif key == 'estado_nuevo':
                    estado_display = dict(Ticket.ESTADO_CHOICES).get(value, value)
                    formatted.append(f"Nuevo estado: {estado_display}")
                elif key == 'usuario_asignado':
                    formatted.append(f"Usuario asignado: {value}")
                elif key == 'usuario_removido':
                    formatted.append(f"Usuario removido: {value}")
                else:
                    formatted.append(f"{key.replace('_', ' ').title()}: {value}")

            return " | ".join(formatted)
        except Exception:
            return str(self.detalles)

    @classmethod
    def registrar_cambio(cls, ticket, usuario, accion, detalles=None):
        """
        Método de clase para registrar cambios en el historial.

        Args:
            ticket: Instancia del ticket
            usuario: Usuario que realiza el cambio
            accion: Descripción de la acción
            detalles: Diccionario con detalles adicionales
        """
        cls.objects.create(
            ticket=ticket,
            usuario=usuario,
            accion=accion,
            detalles=detalles or {}
        )


def ticket_image_upload_path(instance, filename):
    """
    Genera la ruta de almacenamiento para las imágenes de tickets.

    Args:
        instance: Instancia de TicketImagen
        filename: Nombre original del archivo

    Returns:
        str: Ruta donde se almacenará el archivo
    """
    # Obtener extensión del archivo
    ext = filename.split('.')[-1].lower()
    # Generar nombre único
    filename = f"{uuid.uuid4()}.{ext}"
    # Ruta: tickets/images/YYYY/MM/DD/filename
    from datetime import datetime
    now = datetime.now()
    return f"tickets/images/{now.year}/{now.month:02d}/{now.day:02d}/{filename}"


class TicketImagen(models.Model):
    """
    Modelo para almacenar imágenes asociadas a tickets.

    Incluye compresión automática de imágenes para optimizar
    el almacenamiento y la velocidad de carga.
    """

    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name='imagenes',
        help_text='Ticket al que pertenece esta imagen'
    )
    imagen = models.ImageField(
        upload_to=ticket_image_upload_path,
        validators=[
            FileExtensionValidator(
                allowed_extensions=['jpg', 'jpeg', 'png', 'webp'],
                message='Solo se permiten archivos JPG, PNG y WebP'
            )
        ],
        help_text='Imagen del ticket (máximo 5MB, se comprimirá automáticamente)'
    )
    descripcion = models.CharField(
        max_length=200,
        blank=True,
        help_text='Descripción opcional de la imagen'
    )
    orden = models.PositiveIntegerField(
        default=1,
        help_text='Orden de visualización de la imagen'
    )

    # Campos de auditoría
    fecha_subida = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de subida de la imagen'
    )
    subida_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='imagenes_subidas',
        help_text='Usuario que subió la imagen'
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Indica si la imagen está activa'
    )

    # Metadatos de la imagen
    tamaño_original = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text='Tamaño original del archivo en bytes'
    )
    tamaño_comprimido = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text='Tamaño después de la compresión en bytes'
    )
    ancho_original = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text='Ancho original de la imagen en píxeles'
    )
    alto_original = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text='Alto original de la imagen en píxeles'
    )

    class Meta:
        db_table = 'ticket_imagen'
        verbose_name = 'Imagen de Ticket'
        verbose_name_plural = 'Imágenes de Tickets'
        ordering = ['ticket', 'orden', 'fecha_subida']
        indexes = [
            models.Index(fields=['ticket', 'is_active'], name='idx_ticket_imagen_activa'),
            models.Index(fields=['fecha_subida'], name='idx_ticket_imagen_fecha'),
        ]

    def __str__(self):
        return f"Imagen #{self.id} - Ticket #{self.ticket.id}"

    def save(self, *args, **kwargs):
        """
        Sobrescribe el método save para comprimir la imagen automáticamente.
        """
        if self.imagen and hasattr(self.imagen, 'file'):
            # Guardar metadatos originales
            try:
                with Image.open(self.imagen.file) as img:
                    self.ancho_original = img.width
                    self.alto_original = img.height
                    self.tamaño_original = self.imagen.size
            except Exception:
                pass

            # Comprimir imagen
            self._comprimir_imagen()

        super().save(*args, **kwargs)

    def _comprimir_imagen(self):
        """
        Comprime la imagen manteniendo buena calidad.
        """
        try:
            # Abrir imagen
            with Image.open(self.imagen.file) as img:
                # Convertir a RGB si es necesario
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Crear fondo blanco para transparencias
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                # Redimensionar si es muy grande (máximo 1920x1080)
                max_width, max_height = 1920, 1080
                if img.width > max_width or img.height > max_height:
                    img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

                # Guardar imagen comprimida
                from io import BytesIO
                from django.core.files.base import ContentFile

                output = BytesIO()

                # Determinar formato y calidad
                format_map = {
                    'jpg': 'JPEG',
                    'jpeg': 'JPEG',
                    'png': 'PNG',
                    'webp': 'WebP'
                }

                # Obtener extensión del archivo
                filename = self.imagen.name
                ext = filename.split('.')[-1].lower()
                format_name = format_map.get(ext, 'JPEG')

                # Configurar parámetros de compresión
                save_kwargs = {}
                if format_name == 'JPEG':
                    save_kwargs = {
                        'format': format_name,
                        'quality': 85,
                        'optimize': True,
                        'progressive': True
                    }
                elif format_name == 'PNG':
                    save_kwargs = {
                        'format': format_name,
                        'optimize': True
                    }
                elif format_name == 'WebP':
                    save_kwargs = {
                        'format': format_name,
                        'quality': 85,
                        'optimize': True
                    }

                # Guardar imagen comprimida
                img.save(output, **save_kwargs)
                output.seek(0)

                # Reemplazar archivo original
                self.imagen.save(
                    filename,
                    ContentFile(output.read()),
                    save=False
                )

                # Guardar tamaño comprimido
                self.tamaño_comprimido = len(output.getvalue())

        except Exception as e:
            # Si falla la compresión, continuar con la imagen original
            print(f"Error al comprimir imagen: {e}")
            pass

    def get_tamaño_legible(self):
        """
        Retorna el tamaño del archivo en formato legible.
        """
        if self.tamaño_comprimido:
            size = self.tamaño_comprimido
        elif self.imagen:
            size = self.imagen.size
        else:
            return "Desconocido"

        # Convertir bytes a formato legible
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def get_reduccion_tamaño(self):
        """
        Calcula el porcentaje de reducción de tamaño.
        """
        if self.tamaño_original and self.tamaño_comprimido:
            reduccion = ((self.tamaño_original - self.tamaño_comprimido) / self.tamaño_original) * 100
            return round(reduccion, 1)
        return 0
