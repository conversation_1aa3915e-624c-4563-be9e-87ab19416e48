"""
Comando de Django para crear usuarios de prueba con datos aleatorios.

Uso:
    python manage.py crear_usuarios_prueba

Este comando crea 40 usuarios de prueba con:
- Nombres y apellidos aleatorios
- DPIs únicos
- Teléfonos aleatorios
- Algunos familiares
- Algunos usuarios inactivos para probar funcionalidad
"""

import random
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from user.models import CargoUsuario, CelularUsuario, Familiar, CelularEmergencia, Parentesco

User = get_user_model()

class Command(BaseCommand):
    help = 'Crea 40 usuarios de prueba con datos aleatorios'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cantidad',
            type=int,
            default=40,
            help='Cantidad de usuarios a crear (default: 40)'
        )

    def handle(self, *args, **options):
        cantidad = options['cantidad']
        
        # Listas de nombres aleatorios
        nombres_masculinos = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON>ctor', '<PERSON>', '<PERSON>ctor', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>'
        ]
        
        no<PERSON><PERSON>_fem<PERSON> = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            'Sofía', 'Alejandra', 'Gabriela', 'Mónica', 'Andrea', 'Silvia', 'Beatriz',
            'Lucía', 'Adriana', 'Valeria', 'Natalia', 'Paola', 'Verónica', 'Cristina',
            'Elena', 'Mariana', 'Daniela', 'Carolina', 'Fernanda', 'Lorena', 'Rocío'
        ]
        
        apellidos = [
            'García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez',
            'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno',
            'Muñoz', 'Álvarez', 'Romero', 'Alonso', 'Gutiérrez', 'Navarro', 'Torres',
            'Domínguez', 'Vázquez', 'Ramos', 'Gil', 'Ramírez', 'Serrano', 'Blanco',
            'Suárez', 'Molina', 'Morales', 'Ortega', 'Delgado', 'Castro', 'Ortiz'
        ]
        
        # Obtener o crear cargos
        cargos = list(CargoUsuario.objects.all())
        if not cargos:
            self.stdout.write(
                self.style.WARNING('No hay cargos disponibles. Creando cargos básicos...')
            )
            cargos_nombres = ['Administrador', 'Supervisor', 'Empleado', 'Asistente', 'Coordinador']
            for cargo_nombre in cargos_nombres:
                cargo, created = CargoUsuario.objects.get_or_create(
                    nombre=cargo_nombre,
                    defaults={'descripcion': f'Cargo de {cargo_nombre}'}
                )
                cargos.append(cargo)

        # Obtener o crear parentescos
        parentescos_nombres = ['Padre', 'Madre', 'Hermano/a', 'Hijo/a', 'Cónyuge', 'Tío/a', 'Primo/a']
        parentescos = []
        for parentesco_nombre in parentescos_nombres:
            parentesco, created = Parentesco.objects.get_or_create(
                parentesco=parentesco_nombre
            )
            parentescos.append(parentesco)
            if created:
                self.stdout.write(f'Creado parentesco: {parentesco_nombre}')
        
        usuarios_creados = 0
        usuarios_existentes = 0
        
        self.stdout.write(f'Creando {cantidad} usuarios de prueba...')
        
        for i in range(cantidad):
            # Generar datos aleatorios
            genero_num = random.choice([1, 2])  # 1=Hombre, 2=Mujer
            if genero_num == 1:
                nombre = random.choice(nombres_masculinos)
            else:
                nombre = random.choice(nombres_femeninos)
            
            apellido1 = random.choice(apellidos)
            apellido2 = random.choice(apellidos)
            
            # Generar username único
            base_username = f"{nombre.lower()}.{apellido1.lower()}"
            username = base_username
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{base_username}{counter}"
                counter += 1
            
            # Generar DPI único
            dpi = self.generar_dpi_unico()
            
            # Fecha de nacimiento aleatoria (entre 18 y 65 años)
            today = date.today()
            start_date = today - timedelta(days=65*365)
            end_date = today - timedelta(days=18*365)
            random_date = start_date + timedelta(
                days=random.randint(0, (end_date - start_date).days)
            )
            
            # Crear usuario
            try:
                usuario = User.objects.create_user(
                    username=username,
                    first_name=nombre,
                    last_name=f"{apellido1} {apellido2}",
                    dpi=dpi,
                    fecha_nacimiento=random_date,
                    genero=genero_num,
                    cargo=random.choice(cargos),
                    is_active=random.choice([True, True, True, False])  # 75% activos, 25% inactivos
                )
                
                # Agregar teléfonos (1-3 teléfonos por usuario)
                num_telefonos = random.randint(1, 3)
                tipos_telefono = ['personal', 'trabajo', 'casa', 'emergencia']
                
                for _ in range(num_telefonos):
                    numero = self.generar_telefono()
                    tipo = random.choice(tipos_telefono)
                    
                    CelularUsuario.objects.create(
                        usuario=usuario,
                        numero=numero,
                        tipo=tipo
                    )
                
                # Agregar familiares (0-2 familiares por usuario)
                if random.choice([True, False]):  # 50% de usuarios tienen familiares
                    num_familiares = random.randint(1, 2)

                    for _ in range(num_familiares):
                        nombre_familiar = random.choice(nombres_masculinos + nombres_femeninos)
                        apellido_familiar = random.choice(apellidos)

                        familiar = Familiar.objects.create(
                            usuario=usuario,
                            nombre=f"{nombre_familiar} {apellido_familiar}",
                            parentesco=random.choice(parentescos)
                        )
                        
                        # Agregar teléfono de emergencia al familiar (50% de probabilidad)
                        if random.choice([True, False]):
                            CelularEmergencia.objects.create(
                                familiar=familiar,
                                numero=self.generar_telefono()
                            )
                
                usuarios_creados += 1
                
                if usuarios_creados % 10 == 0:
                    self.stdout.write(f'Creados {usuarios_creados} usuarios...')
                    
            except Exception as e:
                usuarios_existentes += 1
                self.stdout.write(
                    self.style.WARNING(f'Usuario {username} ya existe o error: {str(e)}')
                )
        
        # Resumen
        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Proceso completado:\n'
                f'   - Usuarios creados: {usuarios_creados}\n'
                f'   - Usuarios existentes: {usuarios_existentes}\n'
                f'   - Total procesados: {usuarios_creados + usuarios_existentes}'
            )
        )
        
        # Estadísticas
        total_usuarios = User.objects.count()
        usuarios_activos = User.objects.filter(is_active=True).count()
        usuarios_inactivos = User.objects.filter(is_active=False).count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Estadísticas del sistema:\n'
                f'   - Total usuarios: {total_usuarios}\n'
                f'   - Usuarios activos: {usuarios_activos}\n'
                f'   - Usuarios inactivos: {usuarios_inactivos}'
            )
        )

    def generar_dpi_unico(self):
        """Genera un DPI único de 13 dígitos"""
        while True:
            # Generar DPI de 13 dígitos
            dpi = ''.join([str(random.randint(0, 9)) for _ in range(13)])
            
            # Verificar que no exista
            if not User.objects.filter(dpi=dpi).exists():
                return dpi

    def generar_telefono(self):
        """Genera un número de teléfono de 8 dígitos"""
        # Primer dígito entre 2-9, resto aleatorio
        primer_digito = random.randint(2, 9)
        resto = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        return f"{primer_digito}{resto}"
