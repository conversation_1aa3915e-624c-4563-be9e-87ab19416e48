#!/usr/bin/env python
"""
Script de instalación para el sistema de reportes
Municipalidad de Estanzuela

Este script garantiza una instalación limpia del sistema de reportes
sin conflictos con migraciones existentes.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line
from django.db import connection
from django.conf import settings

def setup_django():
    """Configura Django para el script."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
    django.setup()

def check_database_connection():
    """Verifica la conexión a la base de datos."""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Conexión a la base de datos exitosa")
        return True
    except Exception as e:
        print(f"❌ Error de conexión a la base de datos: {e}")
        return False

def check_table_exists(table_name):
    """Verifica si una tabla existe en la base de datos."""
    try:
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            result = cursor.fetchone()
            return result is not None
    except Exception as e:
        print(f"Error verificando tabla {table_name}: {e}")
        return False

def install_reportes():
    """Instala el sistema de reportes de forma segura."""
    print("🚀 Iniciando instalación del Sistema de Reportes")
    print("=" * 60)
    
    # 1. Verificar conexión a BD
    if not check_database_connection():
        print("❌ No se puede continuar sin conexión a la base de datos")
        return False
    
    # 2. Verificar si la tabla de reportes ya existe
    if check_table_exists('reporte_generado'):
        print("⚠️  La tabla 'reporte_generado' ya existe")
        print("   Saltando creación de migraciones...")
    else:
        print("📝 Creando migraciones para reportes...")
        try:
            execute_from_command_line(['manage.py', 'makemigrations', 'reportes'])
            print("✅ Migraciones creadas exitosamente")
        except Exception as e:
            print(f"❌ Error creando migraciones: {e}")
            return False
    
    # 3. Aplicar migraciones
    print("📦 Aplicando migraciones...")
    try:
        execute_from_command_line(['manage.py', 'migrate', 'reportes'])
        print("✅ Migraciones aplicadas exitosamente")
    except Exception as e:
        print(f"⚠️  Advertencia en migraciones: {e}")
        print("   Intentando aplicar con --fake-initial...")
        try:
            execute_from_command_line(['manage.py', 'migrate', 'reportes', '--fake-initial'])
            print("✅ Migraciones aplicadas con --fake-initial")
        except Exception as e2:
            print(f"❌ Error aplicando migraciones: {e2}")
            return False
    
    # 4. Verificar instalación
    print("🔍 Verificando instalación...")
    if check_table_exists('reporte_generado'):
        print("✅ Tabla 'reporte_generado' creada correctamente")
    else:
        print("❌ Error: Tabla 'reporte_generado' no encontrada")
        return False
    
    # 5. Verificar permisos (importar después de setup)
    try:
        from permissions.core import PermissionHelper
        print("✅ Sistema de permisos integrado correctamente")
    except ImportError as e:
        print(f"❌ Error importando sistema de permisos: {e}")
        return False
    
    # 6. Verificar dependencias
    try:
        import openpyxl
        import reportlab
        print("✅ Dependencias (openpyxl, reportlab) disponibles")
    except ImportError as e:
        print(f"❌ Error: Dependencia faltante: {e}")
        print("   Ejecute: pip install openpyxl reportlab")
        return False
    
    print("=" * 60)
    print("🎉 ¡Sistema de Reportes instalado exitosamente!")
    print()
    print("📋 Próximos pasos:")
    print("   1. Reinicie el servidor Django")
    print("   2. Acceda como administrador o superadministrador")
    print("   3. Navegue a 'Reportes' en el sidebar")
    print("   4. Genere su primer reporte")
    print()
    print("⚠️  IMPORTANTE: Solo administradores y superadministradores")
    print("   pueden acceder al sistema de reportes.")
    print()
    print("📚 Documentación: SISTEMA_REPORTES_README.md")
    
    return True

def main():
    """Función principal del script."""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("Script de instalación del Sistema de Reportes")
        print("Uso: python install_reportes.py")
        print()
        print("Este script:")
        print("- Verifica la conexión a la base de datos")
        print("- Crea y aplica migraciones de forma segura")
        print("- Verifica dependencias")
        print("- Confirma la instalación exitosa")
        return
    
    try:
        setup_django()
        success = install_reportes()
        
        if success:
            print("\n✅ Instalación completada exitosamente")
            sys.exit(0)
        else:
            print("\n❌ Instalación falló")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        print("   Verifique la configuración de Django y la base de datos")
        sys.exit(1)

if __name__ == '__main__':
    main()
