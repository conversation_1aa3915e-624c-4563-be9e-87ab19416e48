"""
reportes/urls.py
URLs para el sistema de reportes.
"""

from django.urls import path
from . import views

app_name = 'reportes'

urlpatterns = [
    # Vista principal de reportes
    path('', views.ReportesIndexView.as_view(), name='index'),
    
    # Reportes por empleado
    path('empleado/', views.ReporteEmpleadoView.as_view(), name='reporte_empleado'),
    path('empleado/generar/', views.GenerarReporteEmpleadoView.as_view(), name='generar_reporte_empleado'),
    
    # Reportes por área
    path('area/', views.ReporteAreaView.as_view(), name='reporte_area'),
    path('area/generar/', views.GenerarReporteAreaView.as_view(), name='generar_reporte_area'),
    
    # Reportes por ciudadano
    path('ciudadano/', views.ReporteCiudadanoView.as_view(), name='reporte_ciudadano'),
    path('ciudadano/generar/', views.GenerarReporteCiudadanoView.as_view(), name='generar_reporte_ciudadano'),
    
    # Reportes generales
    path('general/', views.ReporteGeneralView.as_view(), name='reporte_general'),
    path('general/generar/', views.GenerarReporteGeneralView.as_view(), name='generar_reporte_general'),
    
    # APIs para búsquedas dinámicas
    path('api/buscar-empleados/', views.BuscarEmpleadosAPIView.as_view(), name='api_buscar_empleados'),
    path('api/buscar-areas/', views.BuscarAreasAPIView.as_view(), name='api_buscar_areas'),
    path('api/buscar-ciudadanos/', views.BuscarCiudadanosAPIView.as_view(), name='api_buscar_ciudadanos'),
    
    # Historial de reportes
    path('historial/', views.HistorialReportesView.as_view(), name='historial'),
]
