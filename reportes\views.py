"""
reportes/views.py
Vistas para el sistema de reportes.

Incluye vistas para generar reportes por empleado, área, ciudadano
y reportes generales, tanto en PDF como en Excel.
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.views.generic import TemplateView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Q
from datetime import datetime, date
import json

from permissions.decorators import admin_required, supervisor_or_admin_required
from permissions.core import PermissionHelper
from ciudadano.models import Ciudadano
from .models import ReporteGenerado
from .utils import ReporteDataHelper, ReporteFormatHelper
from .generators.pdf_generator import PDFReportGenerator
from .generators.excel_generator import ExcelReportGenerator

User = get_user_model()


class ReportesBaseView(LoginRequiredMixin, TemplateView):
    """Vista base para todas las vistas de reportes."""
    
    def dispatch(self, request, *args, **kwargs):
        # Verificar que el usuario sea admin o superadministrador únicamente
        if not PermissionHelper.is_admin(request.user):
            from django.core.exceptions import PermissionDenied
            raise PermissionDenied("Solo administradores y superadministradores pueden acceder al sistema de reportes.")
        return super().dispatch(request, *args, **kwargs)


class ReportesIndexView(ReportesBaseView):
    """Vista principal del sistema de reportes."""
    template_name = 'reportes/index.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'title': 'Sistema de Reportes',
            'breadcrumb': 'Reportes',
            'can_generate_reports': True,
        })
        return context


class ReporteEmpleadoView(ReportesBaseView):
    """Vista para generar reportes por empleado."""
    template_name = 'reportes/empleado.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'title': 'Reportes por Empleado',
            'breadcrumb': 'Reportes > Por Empleado',
            'empleados': ReporteDataHelper.get_empleados_activos(),
        })
        return context


class ReporteAreaView(ReportesBaseView):
    """Vista para generar reportes por área."""
    template_name = 'reportes/area.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'title': 'Reportes por Área',
            'breadcrumb': 'Reportes > Por Área',
            'areas': ReporteDataHelper.get_areas_activas(),
        })
        return context


class ReporteCiudadanoView(ReportesBaseView):
    """Vista para generar reportes por ciudadano."""
    template_name = 'reportes/ciudadano.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'title': 'Reportes por Ciudadano',
            'breadcrumb': 'Reportes > Por Ciudadano',
            'ciudadanos': ReporteDataHelper.get_ciudadanos_activos()[:100],  # Limitar para performance
        })
        return context


class ReporteGeneralView(ReportesBaseView):
    """Vista para generar reportes generales."""
    template_name = 'reportes/general.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'title': 'Reportes Generales',
            'breadcrumb': 'Reportes > General',
        })
        return context


class GenerarReporteBaseView(ReportesBaseView, View):
    """Vista base para generar reportes."""
    
    def post(self, request, *args, **kwargs):
        try:
            # Obtener parámetros comunes
            formato = request.POST.get('formato', 'pdf')
            fecha_inicio_str = request.POST.get('fecha_inicio')
            fecha_fin_str = request.POST.get('fecha_fin')
            
            # Convertir fechas
            fecha_inicio = None
            fecha_fin = None
            
            if fecha_inicio_str:
                fecha_inicio = datetime.strptime(fecha_inicio_str, '%Y-%m-%d').date()
            if fecha_fin_str:
                fecha_fin = datetime.strptime(fecha_fin_str, '%Y-%m-%d').date()
            
            # Generar reporte según el tipo
            response = self.generar_reporte(request, formato, fecha_inicio, fecha_fin)
            
            # Registrar el reporte generado
            self.registrar_reporte(request, formato, fecha_inicio, fecha_fin)
            
            return response
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Error al generar el reporte: {str(e)}'
            }, status=500)
    
    def generar_reporte(self, request, formato, fecha_inicio, fecha_fin):
        """Método a implementar en las clases hijas."""
        raise NotImplementedError
    
    def registrar_reporte(self, request, formato, fecha_inicio, fecha_fin):
        """Registra el reporte generado en la base de datos."""
        ReporteGenerado.objects.create(
            tipo_reporte=self.get_tipo_reporte(),
            formato=formato,
            fecha_inicio=fecha_inicio,
            fecha_fin=fecha_fin,
            entidades_incluidas=self.get_entidades_incluidas(request),
            generado_por=request.user,
            nombre_archivo=self.get_nombre_archivo(request, formato),
        )
    
    def get_tipo_reporte(self):
        """Retorna el tipo de reporte."""
        raise NotImplementedError
    
    def get_entidades_incluidas(self, request):
        """Retorna las entidades incluidas en el reporte."""
        return {}
    
    def get_nombre_archivo(self, request, formato):
        """Genera el nombre del archivo."""
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        return f"reporte_{self.get_tipo_reporte()}_{timestamp}.{formato}"


class GenerarReporteEmpleadoView(GenerarReporteBaseView):
    """Vista para generar reportes por empleado."""
    
    def generar_reporte(self, request, formato, fecha_inicio, fecha_fin):
        empleados_ids = request.POST.getlist('empleados')
        
        if not empleados_ids:
            return JsonResponse({
                'success': False,
                'error': 'Debe seleccionar al menos un empleado'
            }, status=400)
        
        # Obtener datos para el reporte
        datos_reporte = []
        for empleado_id in empleados_ids:
            empleado = get_object_or_404(User, id=empleado_id, is_active=True)
            tickets = ReporteDataHelper.get_tickets_por_empleado(empleado_id, fecha_inicio, fecha_fin)
            resumen = ReporteDataHelper.get_resumen_por_estado(tickets)
            
            datos_reporte.append({
                'empleado': empleado,
                'tickets': tickets,
                'resumen': resumen,
                'tickets_por_estado': {
                    1: ReporteDataHelper.get_tickets_por_estado(tickets, 1),
                    2: ReporteDataHelper.get_tickets_por_estado(tickets, 2),
                    3: ReporteDataHelper.get_tickets_por_estado(tickets, 3),
                    4: ReporteDataHelper.get_tickets_por_estado(tickets, 4),
                }
            })
        
        # Generar reporte según formato
        if formato == 'pdf':
            generator = PDFReportGenerator()
            return generator.generar_reporte_empleado(datos_reporte, fecha_inicio, fecha_fin)
        else:
            generator = ExcelReportGenerator()
            return generator.generar_reporte_empleado(datos_reporte, fecha_inicio, fecha_fin)
    
    def get_tipo_reporte(self):
        return 'empleado'
    
    def get_entidades_incluidas(self, request):
        return {'empleados': request.POST.getlist('empleados')}


class GenerarReporteAreaView(GenerarReporteBaseView):
    """Vista para generar reportes por área."""
    
    def generar_reporte(self, request, formato, fecha_inicio, fecha_fin):
        areas_ids = request.POST.getlist('areas')
        
        if not areas_ids:
            return JsonResponse({
                'success': False,
                'error': 'Debe seleccionar al menos un área'
            }, status=400)
        
        # Obtener datos para el reporte
        datos_reporte = []
        for area_id in areas_ids:
            area = get_object_or_404(Group, id=area_id)
            tickets = ReporteDataHelper.get_tickets_por_area(area_id, fecha_inicio, fecha_fin)
            resumen = ReporteDataHelper.get_resumen_por_estado(tickets)
            
            datos_reporte.append({
                'area': area,
                'tickets': tickets,
                'resumen': resumen,
                'tickets_por_estado': {
                    1: ReporteDataHelper.get_tickets_por_estado(tickets, 1),
                    2: ReporteDataHelper.get_tickets_por_estado(tickets, 2),
                    3: ReporteDataHelper.get_tickets_por_estado(tickets, 3),
                    4: ReporteDataHelper.get_tickets_por_estado(tickets, 4),
                }
            })
        
        # Generar reporte según formato
        if formato == 'pdf':
            generator = PDFReportGenerator()
            return generator.generar_reporte_area(datos_reporte, fecha_inicio, fecha_fin)
        else:
            generator = ExcelReportGenerator()
            return generator.generar_reporte_area(datos_reporte, fecha_inicio, fecha_fin)
    
    def get_tipo_reporte(self):
        return 'area'
    
    def get_entidades_incluidas(self, request):
        return {'areas': request.POST.getlist('areas')}


class GenerarReporteCiudadanoView(GenerarReporteBaseView):
    """Vista para generar reportes por ciudadano."""
    
    def generar_reporte(self, request, formato, fecha_inicio, fecha_fin):
        ciudadanos_ids = request.POST.getlist('ciudadanos')
        
        if not ciudadanos_ids:
            return JsonResponse({
                'success': False,
                'error': 'Debe seleccionar al menos un ciudadano'
            }, status=400)
        
        # Obtener datos para el reporte
        datos_reporte = []
        for ciudadano_id in ciudadanos_ids:
            ciudadano = get_object_or_404(Ciudadano, id=ciudadano_id, is_active=True)
            tickets = ReporteDataHelper.get_tickets_por_ciudadano(ciudadano_id, fecha_inicio, fecha_fin)
            resumen = ReporteDataHelper.get_resumen_por_estado(tickets)
            
            datos_reporte.append({
                'ciudadano': ciudadano,
                'tickets': tickets,
                'resumen': resumen,
                'tickets_por_estado': {
                    1: ReporteDataHelper.get_tickets_por_estado(tickets, 1),
                    2: ReporteDataHelper.get_tickets_por_estado(tickets, 2),
                    3: ReporteDataHelper.get_tickets_por_estado(tickets, 3),
                    4: ReporteDataHelper.get_tickets_por_estado(tickets, 4),
                }
            })
        
        # Generar reporte según formato
        if formato == 'pdf':
            generator = PDFReportGenerator()
            return generator.generar_reporte_ciudadano(datos_reporte, fecha_inicio, fecha_fin)
        else:
            generator = ExcelReportGenerator()
            return generator.generar_reporte_ciudadano(datos_reporte, fecha_inicio, fecha_fin)
    
    def get_tipo_reporte(self):
        return 'ciudadano'
    
    def get_entidades_incluidas(self, request):
        return {'ciudadanos': request.POST.getlist('ciudadanos')}


class GenerarReporteGeneralView(GenerarReporteBaseView):
    """Vista para generar reportes generales."""

    def generar_reporte(self, request, formato, fecha_inicio, fecha_fin):
        # Obtener todos los tickets para el reporte general
        tickets = ReporteDataHelper.get_tickets_generales(fecha_inicio, fecha_fin)
        resumen_general = ReporteDataHelper.get_resumen_por_estado(tickets)

        datos_reporte = {
            'tickets': tickets,
            'resumen': resumen_general,
            'tickets_por_estado': {
                1: ReporteDataHelper.get_tickets_por_estado(tickets, 1),
                2: ReporteDataHelper.get_tickets_por_estado(tickets, 2),
                3: ReporteDataHelper.get_tickets_por_estado(tickets, 3),
                4: ReporteDataHelper.get_tickets_por_estado(tickets, 4),
            }
        }

        # Generar reporte según formato
        if formato == 'pdf':
            generator = PDFReportGenerator()
            return generator.generar_reporte_general(datos_reporte, fecha_inicio, fecha_fin)
        else:
            generator = ExcelReportGenerator()
            return generator.generar_reporte_general(datos_reporte, fecha_inicio, fecha_fin)

    def get_tipo_reporte(self):
        return 'general'


# APIs para búsquedas dinámicas
class BuscarEmpleadosAPIView(ReportesBaseView, View):
    """API para buscar empleados dinámicamente."""

    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()

        empleados = ReporteDataHelper.get_empleados_activos()

        if query:
            empleados = empleados.filter(
                Q(first_name__icontains=query) |
                Q(last_name__icontains=query) |
                Q(username__icontains=query)
            )

        empleados = empleados[:20]  # Limitar resultados

        data = []
        for empleado in empleados:
            data.append({
                'id': empleado.id,
                'text': f"{empleado.get_full_name() or empleado.username}",
                'username': empleado.username,
                'cargo': empleado.cargo.nombre if empleado.cargo else 'Sin cargo',
                'areas': ', '.join([g.name for g in empleado.groups.all() if g.name in PermissionHelper.AREA_GROUPS])
            })

        return JsonResponse({'results': data})


class BuscarAreasAPIView(ReportesBaseView, View):
    """API para buscar áreas dinámicamente."""

    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()

        areas = ReporteDataHelper.get_areas_activas()

        if query:
            areas = areas.filter(name__icontains=query)

        data = []
        for area in areas:
            # Contar tickets del área
            tickets_count = ReporteDataHelper.get_tickets_por_area(area.id).count()

            data.append({
                'id': area.id,
                'text': area.name,
                'tickets_count': tickets_count
            })

        return JsonResponse({'results': data})


class BuscarCiudadanosAPIView(ReportesBaseView, View):
    """API para buscar ciudadanos dinámicamente."""

    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()

        ciudadanos = ReporteDataHelper.get_ciudadanos_activos()

        if query:
            ciudadanos = ciudadanos.filter(
                Q(nombre_completo__icontains=query) |
                Q(dpi__icontains=query) |
                Q(telefono__icontains=query)
            )

        ciudadanos = ciudadanos[:20]  # Limitar resultados

        data = []
        for ciudadano in ciudadanos:
            # Contar tickets del ciudadano
            tickets_count = ReporteDataHelper.get_tickets_por_ciudadano(ciudadano.id).count()

            data.append({
                'id': ciudadano.id,
                'text': ciudadano.nombre_completo,
                'dpi': ciudadano.dpi,
                'telefono': ciudadano.telefono,
                'tickets_count': tickets_count
            })

        return JsonResponse({'results': data})


class HistorialReportesView(ReportesBaseView):
    """Vista para mostrar el historial de reportes generados."""
    template_name = 'reportes/historial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Obtener reportes del usuario actual (o todos si es admin)
        if PermissionHelper.is_admin(self.request.user):
            reportes = ReporteGenerado.objects.all()
        else:
            reportes = ReporteGenerado.objects.filter(generado_por=self.request.user)

        reportes = reportes.order_by('-fecha_generacion')[:50]  # Últimos 50

        context.update({
            'title': 'Historial de Reportes',
            'breadcrumb': 'Reportes > Historial',
            'reportes': reportes,
        })
        return context
