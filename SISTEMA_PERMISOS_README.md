# Sistema de Permisos Dinámico - Documentación

## Resumen de Implementación

Se ha implementado un sistema completo de permisos dinámico para el sistema de tickets municipal, que incluye:

### 1. **Templates de Error Personalizados** ✅
- `templates/403.html` - Acceso denegado con logging de seguridad
- `templates/404.html` - Página no encontrada con búsqueda
- `templates/400.html` - Solicitud incorrecta
- `templates/500.html` - Error del servidor con auto-retry

### 2. **Sistema de Permisos Centralizado** ✅
- **Archivo:** `Base/permissions.py`
- **Clase principal:** `PermissionHelper`
- **Funcionalidades:**
  - Verificación de roles basada en cargo e `is_supervisor`
  - Separación entre grupos de roles y grupos de áreas
  - Métodos específicos para permisos de tickets
  - Context processor mejorado para templates

### 3. **Sidebar Dinámico** ✅
- **Template:** `Base/Templates/Base/sidebar_dinamico.html`
- **Template base:** `Base/Templates/Base/base_dinamico.html`
- **CSS:** `Base/static/css/sidebar_mejorado.css`
- Se adapta automáticamente según el rol del usuario

### 4. **Decoradores Actualizados** ✅
- **Archivo:** `Base/decorators.py`
- Uso del sistema centralizado de permisos
- Logging de intentos de acceso no autorizados
- Manejo correcto de errores 403

### 5. **Logging de Seguridad** ✅
- **Archivo:** `Base/error_handlers.py`
- Registro de intentos de acceso no autorizados
- Configuración de loggers específicos en `settings.py`

## Estructura de Roles

### **Admin/Superadmin**
- **Identificación:** `cargo='Administrador'` O `groups=['Admin']` O `is_superuser=True`
- **Permisos:**
  - Acceso completo al sistema
  - CRUD completo excepto HistorialTicket
  - Ve todo el sidebar

### **Secretaria**
- **Identificación:** `cargo='Secretaria'` O `groups=['Secretaria']`
- **Permisos:**
  - Crear tickets
  - Asignar tickets a grupos (no usuarios individuales)
  - Ver tickets que creó
  - Sidebar limitado: Dashboard, Crear Tickets, Ciudadanos

### **Supervisor**
- **Identificación:** `is_supervisor=True` (prioridad) O `cargo='Supervisor'`
- **Permisos:**
  - Ver tickets de su área/grupo
  - Asignar/desasignar usuarios de su grupo
  - Cambiar estado de tickets
  - Sidebar: Dashboard, Tickets de su grupo, Asignaciones

### **Empleado**
- **Identificación:** `cargo='Empleado'` O `groups=['Empleado']` O por exclusión
- **Permisos:**
  - Ver solo tickets asignados a él
  - Cambiar estado de tickets asignados
  - Sidebar mínimo: Dashboard, Mis Tickets

## Archivos Principales Modificados/Creados

### **Nuevos Archivos:**
```
templates/403.html
templates/404.html
templates/400.html
templates/500.html
Base/error_handlers.py
Base/Templates/Base/sidebar_dinamico.html
Base/Templates/Base/base_dinamico.html
Base/Templates/Base/demo_permisos.html
Base/views.py
Base/urls.py
SISTEMA_PERMISOS_README.md
```

### **Archivos Modificados:**
```
Base/permissions.py - Sistema centralizado mejorado
Base/decorators.py - Decoradores actualizados
Base/static/css/sidebar_mejorado.css - Estilos mejorados
core/settings.py - Templates y logging
core/urls.py - Handlers de error y URLs de Base
Home/templates/home.html - Uso del nuevo template base
```

## Cómo Usar el Sistema

### **1. En Templates:**
```html
{% extends 'base/base_dinamico.html' %}

<!-- El sidebar se adapta automáticamente -->
<!-- Variables disponibles: -->
{{ user_permissions.role }}
{{ user_permissions.is_admin }}
{{ sidebar_permissions.show_tickets }}
```

### **2. En Vistas:**
```python
from Base.decorators import admin_required, can_create_tickets_required
from Base.permissions import PermissionHelper

@admin_required
def vista_admin(request):
    pass

@can_create_tickets_required
def crear_ticket(request):
    pass

# Verificación manual
if PermissionHelper.can_create_tickets(request.user):
    # Lógica para crear ticket
```

### **3. Context Processor:**
Automáticamente disponible en todos los templates:
- `user_permissions` - Todos los permisos del usuario
- `sidebar_permissions` - Permisos específicos del sidebar

## URLs de Testing

Para probar el sistema:
- `/base/demo-permisos/` - Demo completo del sistema
- `/base/test/permissions-info/` - JSON con información de permisos
- `/base/test/403/` - Prueba error 403
- `/base/test/404/` - Prueba error 404
- `/base/test/500/` - Prueba error 500

## Configuración de Logging

Los logs se guardan en:
- `logs/django.log` - Logs generales
- `logs/security.log` - Logs de seguridad

## Próximos Pasos

1. **Probar con diferentes usuarios y roles**
2. **Actualizar templates existentes para usar `base_dinamico.html`**
3. **Aplicar decoradores actualizados en vistas existentes**
4. **Configurar usuarios de prueba con diferentes roles**
5. **Verificar que todas las URLs del sidebar funcionen correctamente**

## Notas Importantes

- El campo `is_supervisor=True` tiene **prioridad** sobre los grupos
- Los grupos se usan tanto para roles como para áreas/departamentos
- El sistema es **backward compatible** con el código existente
- Todos los intentos de acceso no autorizados se registran automáticamente
- Los templates de error se muestran automáticamente en caso de errores 403/404/500
