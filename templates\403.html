{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Acceso Denegado - Sistema de Tickets{% endblock %}

{% block extra_css %}
<style>
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }

        .error-page-container {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .error-icon {
            font-size: 6rem;
            color: var(--danger-color);
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .error-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .error-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn-outline-secondary {
            border: 2px solid #6c757d;
            color: #6c757d;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-2px);
        }

        .security-info {
            background: #f8f9fa;
            border-left: 4px solid var(--warning-color);
            padding: 1rem;
            margin-top: 2rem;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }

        .security-info h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .security-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        /* Estilos para mensajes de advertencia */
        .warning-messages {
            margin-bottom: 2rem;
        }

        .warning-messages .alert {
            border-radius: 15px;
            border: none;
            font-weight: 500;
            animation: slideInDown 0.5s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="error-page-container">
    <div class="error-container">
        <!-- Mostrar mensajes de advertencia del middleware -->
        {% if messages %}
        <div class="warning-messages">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="error-icon">
            <i class="fas fa-shield-alt"></i>
        </div>

        <div class="error-code">403</div>
        <h1 class="error-title">Acceso Denegado</h1>

        <p class="error-description">
            No tienes los permisos necesarios para acceder a esta página o realizar esta acción.
            Tu intento de acceso ha sido registrado por motivos de seguridad.
        </p>

        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver Atrás
            </a>
            <a href="/inicio/" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Ir al Dashboard
            </a>
        </div>

        <div class="security-info">
            <h6><i class="fas fa-info-circle me-2"></i>Información de Seguridad</h6>
            <p>
                Si crees que deberías tener acceso a esta página, contacta al administrador del sistema.
                Todos los intentos de acceso no autorizados son monitoreados y registrados.
            </p>
        </div>
    </div>
</div>
{% endblock %}
