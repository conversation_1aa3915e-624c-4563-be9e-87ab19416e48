{% extends 'Base/base.html' %}
{% load static %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    .report-card {
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .report-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .report-icon {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 15px;
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .section-title {
        color: var(--primary-color);
        font-weight: bold;
        margin-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 5px;
    }
    
    .btn-generate {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        padding: 12px 30px;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
    }
    
    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(26, 35, 126, 0.3);
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .select2-container--bootstrap-5 .select2-selection {
        min-height: 38px;
    }
    
    .breadcrumb-custom {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .breadcrumb-custom a {
        color: white;
        text-decoration: none;
    }
    
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="breadcrumb-custom">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{% url 'reportes:index' %}">Reportes</a></li>
                {% block breadcrumb_items %}{% endblock %}
            </ol>
        </nav>
    </div>
    
    <!-- Título de la página -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-primary">
                <i class="fas fa-chart-bar me-2"></i>
                {% block page_title %}Sistema de Reportes{% endblock %}
            </h2>
            <p class="text-muted">{% block page_description %}Genere reportes detallados del sistema de tickets{% endblock %}</p>
        </div>
    </div>
    
    {% block report_content %}{% endblock %}
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h5>Generando reporte...</h5>
        <p class="text-muted">Por favor espere mientras se procesa su solicitud</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Inicializar Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'Seleccione una opción...',
        allowClear: true
    });
    
    // Configurar fechas
    const today = new Date().toISOString().split('T')[0];
    $('#fecha_fin').attr('max', today);
    
    $('#fecha_inicio').on('change', function() {
        const fechaInicio = $(this).val();
        if (fechaInicio) {
            $('#fecha_fin').attr('min', fechaInicio);
        }
    });
    
    $('#fecha_fin').on('change', function() {
        const fechaFin = $(this).val();
        if (fechaFin) {
            $('#fecha_inicio').attr('max', fechaFin);
        }
    });
    
    // Función para mostrar loading
    function showLoading() {
        $('#loadingOverlay').css('display', 'flex');
    }
    
    // Función para ocultar loading
    function hideLoading() {
        $('#loadingOverlay').hide();
    }
    
    // Manejar envío de formularios de reportes
    $('.report-form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const formData = new FormData(form[0]);
        
        // Validar que se haya seleccionado al menos una entidad (si aplica)
        const entidades = form.find('select[multiple]');
        if (entidades.length > 0 && entidades.val() && entidades.val().length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Selección requerida',
                text: 'Debe seleccionar al menos una opción para generar el reporte.',
                confirmButtonColor: '#1A237E'
            });
            return;
        }
        
        showLoading();
        
        // Enviar formulario
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhrFields: {
                responseType: 'blob'
            },
            success: function(data, status, xhr) {
                hideLoading();
                
                // Obtener nombre del archivo del header
                const disposition = xhr.getResponseHeader('Content-Disposition');
                let filename = 'reporte.pdf';
                if (disposition) {
                    const matches = /filename="([^"]*)"/.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1];
                    }
                }
                
                // Crear enlace de descarga
                const blob = new Blob([data]);
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // Mostrar mensaje de éxito
                Swal.fire({
                    icon: 'success',
                    title: 'Reporte generado',
                    text: 'El reporte se ha generado y descargado correctamente.',
                    confirmButtonColor: '#1A237E'
                });
            },
            error: function(xhr) {
                hideLoading();
                
                let errorMessage = 'Error al generar el reporte. Por favor intente nuevamente.';
                
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: errorMessage,
                    confirmButtonColor: '#1A237E'
                });
            }
        });
    });
});
</script>

{% block extra_report_js %}{% endblock %}
{% endblock %}
