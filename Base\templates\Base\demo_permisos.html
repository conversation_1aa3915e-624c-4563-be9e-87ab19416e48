{% extends 'base/base_dinamico.html' %}
{% load static %}

{% block title %}Demo del Sistema de Permisos{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Demo del Sistema de Permisos Dinámico
                </h4>
            </div>
            <div class="card-body">
                
                <!-- Información del Usuario -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-user me-2"></i>Información del Usuario</h5>
                                <p><strong>Usuario:</strong> {{ user.username }}</p>
                                <p><strong>Nombre:</strong> {{ user.get_full_name|default:"No definido" }}</p>
                                <p><strong>Rol Principal:</strong> {{ user_permissions.role }}</p>
                                <p><strong>Cargo:</strong> {{ user.cargo.nombre|default:"No definido" }}</p>
                                <p><strong>Es Supervisor:</strong> 
                                    {% if user.is_supervisor %}
                                        <span class="badge bg-success">Sí</span>
                                    {% else %}
                                        <span class="badge bg-secondary">No</span>
                                    {% endif %}
                                </p>
                                {% if user_permissions.areas %}
                                <p><strong>Áreas:</strong> {{ user_permissions.areas|join:", " }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5><i class="fas fa-key me-2"></i>Roles del Usuario</h5>
                                <div class="d-flex flex-wrap gap-2">
                                    {% if user_permissions.is_admin %}
                                        <span class="badge bg-danger fs-6">
                                            <i class="fas fa-crown me-1"></i>Admin
                                        </span>
                                    {% endif %}
                                    {% if user_permissions.is_supervisor %}
                                        <span class="badge bg-warning fs-6">
                                            <i class="fas fa-users me-1"></i>Supervisor
                                        </span>
                                    {% endif %}
                                    {% if user_permissions.is_secretaria %}
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-user-tie me-1"></i>Secretaria
                                        </span>
                                    {% endif %}
                                    {% if user_permissions.is_empleado %}
                                        <span class="badge bg-secondary fs-6">
                                            <i class="fas fa-user me-1"></i>Empleado
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permisos de Tickets -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-ticket-alt me-2"></i>Permisos de Tickets</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_create_tickets %}
                                                <i class="fas fa-plus-circle text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Crear Tickets</p>
                                            {% else %}
                                                <i class="fas fa-times-circle text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Crear Tickets</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_assign_tickets %}
                                                <i class="fas fa-user-plus text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Asignar Tickets</p>
                                            {% else %}
                                                <i class="fas fa-user-times text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Asignar Tickets</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_view_all_tickets %}
                                                <i class="fas fa-eye text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Ver Todos</p>
                                            {% else %}
                                                <i class="fas fa-eye-slash text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Ver Todos</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_delete_tickets %}
                                                <i class="fas fa-trash text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Eliminar Tickets</p>
                                            {% else %}
                                                <i class="fas fa-ban text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Eliminar Tickets</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permisos del Sidebar -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bars me-2"></i>Elementos Visibles en el Sidebar</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for permission, value in sidebar_permissions.items %}
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            {% if value %}
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span class="text-success">{{ permission|title }}</span>
                                            {% else %}
                                                <i class="fas fa-times-circle text-danger me-2"></i>
                                                <span class="text-muted">{{ permission|title }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Otros Permisos -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs me-2"></i>Otros Permisos del Sistema</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_manage_users %}
                                                <i class="fas fa-users-cog text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Gestionar Usuarios</p>
                                            {% else %}
                                                <i class="fas fa-users-slash text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Gestionar Usuarios</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_create_notifications %}
                                                <i class="fas fa-bell text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Crear Notificaciones</p>
                                            {% else %}
                                                <i class="fas fa-bell-slash text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Crear Notificaciones</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3 border rounded">
                                            {% if user_permissions.can_change_ticket_status %}
                                                <i class="fas fa-edit text-success fs-2"></i>
                                                <p class="mt-2 mb-0 text-success">Cambiar Estado</p>
                                            {% else %}
                                                <i class="fas fa-lock text-danger fs-2"></i>
                                                <p class="mt-2 mb-0 text-danger">Cambiar Estado</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instrucciones -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle me-2"></i>Instrucciones</h6>
                    <p class="mb-0">
                        Esta página muestra cómo funciona el nuevo sistema de permisos dinámico. 
                        El sidebar se adapta automáticamente según tu rol, y los permisos se verifican 
                        tanto en el frontend como en el backend. Prueba cambiar de usuario con diferentes 
                        roles para ver cómo cambia la interfaz.
                    </p>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
