"""
user/models.py
Modelos principales para la gestión de usuarios extendidos y su información
relacionada (cargos, teléfonos, familiares, etc.).

Todas las tablas incluyen el campo `is_active` para desactivar registros
sin eliminarlos físicamente.
"""
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import models


class CargoUsuario(models.Model):
    """
    Catálogo de cargos o puestos dentro de la organización.
    """
    nombre = models.CharField(max_length=100)
    descripcion = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "cargo_usuario"
        verbose_name = "Cargo"
        verbose_name_plural = "Cargos"

    def __str__(self) -> str:
        return self.nombre


class User(AbstractUser):
    """
    Usuario extendido que servirá como `AUTH_USER_MODEL`.
    """
    cargo = models.ForeignKey(
        CargoUsuario,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="usuarios",
    )
    is_supervisor = models.BooleanField(default=False)
    dpi = models.CharField(max_length=20, unique=True)
    fecha_nacimiento = models.DateField(blank=True, null=True)

    GENERO_CHOICES = (
        (1, "Hombre"),
        (2, "Mujer"),
    )
    genero = models.IntegerField(choices=GENERO_CHOICES, blank=True, null=True)
    # `is_active` ya existe en AbstractUser; se mantiene su semántica.

    class Meta:
        db_table = "user_user"
        verbose_name = "Usuario"
        verbose_name_plural = "Usuarios"

    def __str__(self) -> str:
        return f"{self.username} ({self.get_full_name() or 'Sin nombre'})"


class CelularUsuario(models.Model):
    """
    Teléfonos asociados a un usuario.
    """
    TIPO_CHOICES = (
        ("personal", "Personal"),
        ("trabajo", "Trabajo"),
        ("casa", "Casa"),
        ("emergencia", "Emergencia"),
    )

    usuario = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="celulares",
    )
    numero = models.CharField(max_length=20)
    tipo = models.CharField(max_length=20, choices=TIPO_CHOICES, default="personal")
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "celular_usuario"
        verbose_name = "Celular"
        verbose_name_plural = "Celulares"

    def __str__(self) -> str:
        return f"{self.numero} ({self.get_tipo_display()})"


class Parentesco(models.Model):
    """
    Catálogo de tipos de parentesco para familiares.
    """
    parentesco = models.CharField(max_length=100)

    class Meta:
        db_table = "parentesco"
        verbose_name = "Parentesco"
        verbose_name_plural = "Parentescos"

    def __str__(self) -> str:
        return self.parentesco


class Familiar(models.Model):
    """
    Familiares de un usuario con su tipo de parentesco.
    """
    usuario = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="familiares",
    )
    nombre = models.CharField(max_length=100)
    parentesco = models.ForeignKey(Parentesco, on_delete=models.PROTECT)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "familiar"
        verbose_name = "Familiar"
        verbose_name_plural = "Familiares"

    def __str__(self) -> str:
        return f"{self.nombre} ({self.parentesco})"

    @property
    def telefonos_count(self):
        """Retorna el número de teléfonos de emergencia activos."""
        return self.celulares_emergencia.filter(is_active=True).count()


class CelularEmergencia(models.Model):
    """
    Teléfonos de emergencia asociados a un familiar.
    """
    familiar = models.ForeignKey(
        Familiar,
        on_delete=models.CASCADE,
        related_name="celulares_emergencia",
    )
    numero = models.CharField(max_length=20)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "celular_emergencia"
        verbose_name = "Celular de Emergencia"
        verbose_name_plural = "Celulares de Emergencia"

    def __str__(self) -> str:
        return self.numero