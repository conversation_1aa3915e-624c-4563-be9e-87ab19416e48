"""
Base/admin.py
Configuración del panel de administración para la app Base.

Incluye:
- Administración de intentos de acceso no autorizado
- Filtros y búsquedas para facilitar el monitoreo
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import UnauthorizedAccessAttempt


@admin.register(UnauthorizedAccessAttempt)
class UnauthorizedAccessAttemptAdmin(admin.ModelAdmin):
    """
    Administración de intentos de acceso no autorizado.
    Permite monitorear y analizar patrones de acceso sospechoso.
    """

    list_display = [
        'user_link',
        'ip_address',
        'url_attempted_short',
        'timestamp',
        'user_agent_short',
        'is_recent'
    ]

    list_filter = [
        'timestamp',
        ('user', admin.RelatedOnlyFieldListFilter),
        'ip_address',
    ]

    search_fields = [
        'user__username',
        'user__first_name',
        'user__last_name',
        'ip_address',
        'url_attempted',
        'user_agent'
    ]

    readonly_fields = [
        'user',
        'ip_address',
        'url_attempted',
        'user_agent',
        'timestamp',
        'session_key'
    ]

    # date_hierarchy = 'timestamp'  # Comentado para evitar errores de timezone con MySQL

    ordering = ['-timestamp']

    list_per_page = 50

    def user_link(self, obj):
        """
        Muestra el nombre de usuario sin enlace para evitar errores de URL.
        """
        if obj.user:
            return format_html('<strong>{}</strong> ({})', obj.user.username, obj.user.get_full_name() or 'Sin nombre')
        return '-'
    user_link.short_description = 'Usuario'
    user_link.admin_order_field = 'user__username'

    def url_attempted_short(self, obj):
        """
        Muestra una versión corta de la URL intentada.
        """
        if len(obj.url_attempted) > 50:
            return obj.url_attempted[:47] + '...'
        return obj.url_attempted
    url_attempted_short.short_description = 'URL Intentada'
    url_attempted_short.admin_order_field = 'url_attempted'

    def user_agent_short(self, obj):
        """
        Muestra una versión corta del User-Agent.
        """
        if len(obj.user_agent) > 30:
            return obj.user_agent[:27] + '...'
        return obj.user_agent
    user_agent_short.short_description = 'User Agent'

    def is_recent(self, obj):
        """
        Indica si el intento es reciente (últimas 24 horas).
        """
        recent_threshold = timezone.now() - timedelta(hours=24)
        if obj.timestamp >= recent_threshold:
            return format_html('<span style="color: red;">●</span> Reciente')
        return format_html('<span style="color: gray;">○</span> Antiguo')
    is_recent.short_description = 'Estado'

    def has_add_permission(self, request):
        """
        No permitir agregar intentos manualmente.
        """
        return False

    def has_change_permission(self, request, obj=None):
        """
        No permitir modificar intentos.
        """
        return False

    def get_queryset(self, request):
        """
        Optimizar consultas incluyendo datos relacionados.
        """
        return super().get_queryset(request).select_related('user')


# Configuración adicional del admin
admin.site.site_header = "Administración - Sistema Municipal de Tickets"
admin.site.site_title = "Admin Municipal"
admin.site.index_title = "Panel de Administración"
