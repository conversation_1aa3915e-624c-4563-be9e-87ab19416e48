{% extends 'Base/base.html' %}
{% block title %}Familiares de {{ usuario.get_full_name }}{% endblock %}
{% block content %}
<div class="card">
    <div class="card-header">
        <h5>Familiares de {{ usuario.get_full_name }}</h5>
    </div>
    <div class="card-body">
        <form method="post" id="form-familiar">
            {% csrf_token %}
            {{ form.as_p }}
            <button type="submit" class="btn btn-primary">Agregar</button>
        </form>
        <hr>
        <h6>Familiares registrados</h6>
        <ul>
            {% for fam in familiares %}
                <li>
                    {{ fam.nombre }} ({{ fam.parentesco }})
                    <ul>
                        {% for cel in fam.celulares_emergencia.all %}
                            <li>{{ cel.numero }}</li>
                        {% empty %}
                            <li class="text-muted">Sin teléfonos de emergencia</li>
                        {% endfor %}
                    </ul>
                    <a href="{% url 'user:editar_familiar' usuario.id fam.id %}" class="btn btn-sm btn-secondary">Editar</a>
                    <button class="btn btn-sm btn-danger" onclick="eliminarFamiliar({{ fam.id }})">Eliminar</button>
                </li>
            {% empty %}
                <li class="text-muted">Sin familiares registrados</li>
            {% endfor %}
        </ul>
    </div>
</div>
<script>
function eliminarFamiliar(id) {
    fetch("{% url 'user:desactivar_familiar' usuario.id 0 %}".replace('0', id), {
        method: 'POST',
        headers: {'X-CSRFToken': '{{ csrf_token }}'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) location.reload();
    });
}
</script>
{% endblock %}