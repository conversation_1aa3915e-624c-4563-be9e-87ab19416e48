{% extends 'Base/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block title %}Crear Usuario - Paso 3 - Familiares{% endblock %}

{% block content %}
<style>
    :root {
        --primary-color: #1A237E;
        --secondary-color: #283593;
        --success-color: #28a745;
        --light-gray: #e9ecef;
        --dark-gray: #6c757d;
    }

    .progress-container {
        margin-bottom: 3rem;
        padding: 0 2rem;
    }

    .progress-bar-custom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        margin-bottom: 1rem;
    }

    .progress-line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 4px;
        background-color: var(--light-gray);
        z-index: 1;
        border-radius: 2px;
    }

    .progress-line-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
        transition: width 0.3s ease;
    }

    .step-item {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: white;
        padding: 0.5rem;
    }

    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        border: 3px solid var(--light-gray);
        background: white;
        color: var(--dark-gray);
    }

    .step-item.active .step-circle {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
    }

    .step-item.completed .step-circle {
        background: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }

    .step-label {
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
        color: var(--dark-gray);
        transition: color 0.3s ease;
    }

    .step-item.active .step-label {
        color: var(--primary-color);
        font-weight: 600;
    }

    .step-item.completed .step-label {
        color: var(--success-color);
        font-weight: 600;
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 2.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
        border: 1px solid rgba(26, 35, 126, 0.1);
    }

    .familiar-form-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .familiar-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--dark-gray);
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px dashed #dee2e6;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
        color: var(--primary-color);
    }

    .action-buttons .btn {
        margin-right: 0.25rem;
    }

    .modal-header {
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        color: white;
    }

    .modal-header .btn-close {
        filter: invert(1);
    }

    .telefono-emergencia-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .telefono-emergencia-item:hover {
        background: #e9ecef;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .familiar-table tbody tr {
        animation: slideIn 0.3s ease;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .progress-container {
            padding: 0 1rem;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .step-label {
            font-size: 0.8rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .action-buttons .btn {
            margin-bottom: 0.25rem;
        }
    }

    
    .familiar-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .familiar-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .familiar-info {
        flex-grow: 1;
    }
    
    .familiar-actions {
        margin-left: 1rem;
    }
    
    .telefonos-emergencia {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
    
    .telefono-emergencia-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .add-telefono-form {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 1rem;
        margin-top: 0.5rem;
        display: none;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Barra de progreso moderna -->
            <div class="progress-container">
                <div class="progress-bar-custom">
                    <div class="progress-line">
                        <div class="progress-line-fill" style="width: 100%;"></div>
                    </div>

                    <div class="step-item completed">
                        <div class="step-circle"><i class="fas fa-check"></i></div>
                        <div class="step-label">Información<br>Básica</div>
                    </div>

                    <div class="step-item completed">
                        <div class="step-circle"><i class="fas fa-check"></i></div>
                        <div class="step-label">Teléfonos</div>
                    </div>

                    <div class="step-item active">
                        <div class="step-circle">3</div>
                        <div class="step-label">Familiares</div>
                    </div>
                </div>
            </div>

            <!-- Información del usuario -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="alert alert-info">
                        <i class="fas fa-user me-2"></i>
                        <strong>Usuario:</strong> {{ usuario.get_full_name|default:usuario.username }}
                        <span class="ms-3"><strong>DPI:</strong> {{ usuario.dpi }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Formulario para agregar familiares -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="form-section">
                        <h4><i class="fas fa-users me-2"></i>Agregar Familiares y Contactos de Emergencia</h4>
                        <p class="text-muted">Agrega familiares del usuario. Cada familiar debe tener al menos un número de emergencia.</p>

                        <!-- Formulario para agregar familiar -->
                        <div class="familiar-form-card">
                            <h6><i class="fas fa-user-plus me-2"></i>Nuevo Familiar</h6>
                            <form id="familiar-form" method="post">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="nombre" class="form-label">Nombre Completo <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="nombre" name="nombre"
                                               placeholder="Nombre completo del familiar"
                                               pattern="[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]{2,50}" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="parentesco" class="form-label">Parentesco <span class="text-danger">*</span></label>
                                        <select class="form-select" id="parentesco" name="parentesco" required>
                                            <option value="">Selecciona...</option>
                                            <option value="padre">Padre</option>
                                            <option value="madre">Madre</option>
                                            <option value="hermano">Hermano/a</option>
                                            <option value="hijo">Hijo/a</option>
                                            <option value="esposo">Esposo/a</option>
                                            <option value="abuelo">Abuelo/a</option>
                                            <option value="tio">Tío/a</option>
                                            <option value="primo">Primo/a</option>
                                            <option value="otro">Otro</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="telefono_principal" class="form-label">Teléfono Principal <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="telefono_principal" name="telefono_principal"
                                               placeholder="12345678"
                                               pattern="[0-9\+\s\(\)\-]{8,15}" required>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-plus me-1"></i>Agregar
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Tabla de familiares agregados -->
                        <div class="mt-4">
                            <h5><i class="fas fa-list me-2"></i>Familiares Agregados</h5>
                            <div class="familiar-table">
                                {% if familiares %}
                                    <table class="table table-hover mb-0" id="familiares-tabla">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Nombre</th>
                                                <th>Parentesco</th>
                                                <th>Teléfonos</th>
                                                <th width="200">Acciones</th>
                                            </tr>
                                        </thead>
                                        <tbody id="familiares-tbody">
                                            {% for familiar in familiares %}
                                                <tr data-familiar-id="{{ familiar.id }}">
                                                    <td><strong>{{ familiar.nombre }}</strong></td>
                                                    <td>
                                                        <span class="badge bg-info">{{ familiar.parentesco.parentesco }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success" id="count-telefonos-{{ familiar.id }}">
                                                            {{ familiar.telefonos_count }} número(s)
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <button type="button" class="btn btn-sm btn-outline-info ver-detalle"
                                                                    data-familiar-id="{{ familiar.id }}"
                                                                    data-familiar-nombre="{{ familiar.nombre }}"
                                                                    title="Ver detalles">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-primary gestionar-telefonos"
                                                                    data-familiar-id="{{ familiar.id }}"
                                                                    data-familiar-nombre="{{ familiar.nombre }}"
                                                                    title="Gestionar teléfonos">
                                                                <i class="fas fa-phone"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger eliminar-familiar"
                                                                    data-familiar-id="{{ familiar.id }}"
                                                                    data-familiar-nombre="{{ familiar.nombre }}"
                                                                    title="Eliminar familiar">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                {% else %}
                                    <div class="empty-state" id="empty-familiares">
                                        <i class="fas fa-users-slash"></i>
                                        <p><strong>No hay familiares agregados aún</strong></p>
                                        <small>Agrega familiares usando el formulario de arriba.</small>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Botones de navegación -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'user:crear_usuario_paso2' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Anterior
                            </a>
                            
                            <div>
                                <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-secondary me-2">
                                    Cancelar
                                </a>
                                <form method="post" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" name="finalizar" class="btn btn-success">
                                        <i class="fas fa-check me-2"></i>Finalizar
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('familiar-form');
    const familiariesLista = document.querySelector('.familiar-table');
    const emptyState = document.getElementById('empty-familiares');

    console.log('Formulario encontrado:', form);

    // Manejar envío del formulario de familiares
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Formulario de familiar enviado');

            const formData = new FormData(form);
            console.log('Datos del formulario:', Object.fromEntries(formData));

            fetch('', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => {
                console.log('Respuesta recibida:', response);
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);
                if (data.success) {
                    // Agregar familiar a la lista
                    agregarFamiliarALista(data.familiar);

                    // Limpiar formulario
                    form.reset();

                    // Mostrar mensaje de éxito con SweetAlert
                    Swal.fire({
                        icon: 'success',
                        title: '¡Éxito!',
                        text: data.message || 'Familiar agregado correctamente',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    // Mostrar mensaje de error con SweetAlert
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Error al agregar familiar'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        });
    } else {
        console.error('Formulario no encontrado');
    }

    // Manejar botón "Ver detalles"
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('ver-detalle') || e.target.closest('.ver-detalle')) {
            const button = e.target.classList.contains('ver-detalle') ? e.target : e.target.closest('.ver-detalle');
            const familiarId = button.dataset.familiarId;
            const familiarNombre = button.dataset.familiarNombre;

            mostrarDetallesFamiliar(familiarId, familiarNombre);
        }
    });

    // Manejar botón "Gestionar teléfonos"
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('gestionar-telefonos') || e.target.closest('.gestionar-telefonos')) {
            const button = e.target.classList.contains('gestionar-telefonos') ? e.target : e.target.closest('.gestionar-telefonos');
            const familiarId = button.dataset.familiarId;
            const familiarNombre = button.dataset.familiarNombre;

            mostrarGestionarTelefonos(familiarId, familiarNombre);
        }
    });

    // Manejar botón "Eliminar familiar"
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('eliminar-familiar') || e.target.closest('.eliminar-familiar')) {
            const button = e.target.classList.contains('eliminar-familiar') ? e.target : e.target.closest('.eliminar-familiar');
            const familiarId = button.dataset.familiarId;
            const familiarNombre = button.dataset.familiarNombre;

            // Usar SweetAlert para confirmación
            Swal.fire({
                title: '¿Estás seguro?',
                text: `Esta acción eliminará a "${familiarNombre}" y todos sus teléfonos de emergencia`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    eliminarFamiliar(familiarId);
                }
            });
        }
    });
    
    // Event delegation para botones dinámicos
    document.addEventListener('click', function(e) {
        // Eliminar familiar - funcionalidad movida al event listener principal
        
        // Mostrar formulario para agregar teléfono de emergencia
        if (e.target.classList.contains('agregar-telefono-emergencia') || e.target.closest('.agregar-telefono-emergencia')) {
            const button = e.target.classList.contains('agregar-telefono-emergencia') ? e.target : e.target.closest('.agregar-telefono-emergencia');
            const familiarId = button.dataset.familiarId;
            const form = document.querySelector(`.add-telefono-form[data-familiar-id="${familiarId}"]`);
            
            if (form) {
                form.style.display = form.style.display === 'none' ? 'block' : 'none';
            }
        }
        
        // Cancelar agregar teléfono
        if (e.target.classList.contains('cancelar-telefono-emergencia')) {
            const form = e.target.closest('.add-telefono-form');
            if (form) {
                form.style.display = 'none';
                form.querySelector('input').value = '';
            }
        }
        
        // Guardar teléfono de emergencia
        if (e.target.classList.contains('guardar-telefono-emergencia')) {
            const familiarId = e.target.dataset.familiarId;
            const form = e.target.closest('.add-telefono-form');
            const input = form.querySelector('input');
            const numero = input.value.trim();
            
            if (numero) {
                agregarTelefonoEmergencia(familiarId, numero);
            }
        }
        
        // Eliminar teléfono de emergencia
        if (e.target.classList.contains('eliminar-telefono-emergencia') || e.target.closest('.eliminar-telefono-emergencia')) {
            const button = e.target.classList.contains('eliminar-telefono-emergencia') ? e.target : e.target.closest('.eliminar-telefono-emergencia');
            const telefonoId = button.dataset.telefonoId;
            
            if (confirm('¿Eliminar este teléfono de emergencia?')) {
                eliminarTelefonoEmergencia(telefonoId);
            }
        }
    });
    
    function agregarFamiliarALista(familiar) {
        // Si no existe la tabla, crearla
        let tabla = document.getElementById('familiares-tabla');
        if (!tabla) {
            // Ocultar estado vacío
            const emptyState = document.getElementById('empty-familiares');
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            // Crear tabla
            const familiarTable = document.querySelector('.familiar-table');
            familiarTable.innerHTML = `
                <table class="table table-hover mb-0" id="familiares-tabla">
                    <thead class="table-light">
                        <tr>
                            <th>Nombre</th>
                            <th>Parentesco</th>
                            <th>Teléfonos</th>
                            <th width="200">Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="familiares-tbody">
                    </tbody>
                </table>
            `;
        }

        // Agregar fila a la tabla
        const tbody = document.getElementById('familiares-tbody');
        const row = document.createElement('tr');
        row.dataset.familiarId = familiar.id;

        row.innerHTML = `
            <td><strong>${familiar.nombre}</strong></td>
            <td>
                <span class="badge bg-info">${familiar.parentesco}</span>
            </td>
            <td>
                <span class="badge bg-success" id="count-telefonos-${familiar.id}">
                    ${familiar.telefonos_count || 1} número(s)
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button type="button" class="btn btn-sm btn-outline-info ver-detalle"
                            data-familiar-id="${familiar.id}"
                            data-familiar-nombre="${familiar.nombre}"
                            title="Ver detalles">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary gestionar-telefonos"
                            data-familiar-id="${familiar.id}"
                            data-familiar-nombre="${familiar.nombre}"
                            title="Gestionar teléfonos">
                        <i class="fas fa-phone"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger eliminar-familiar"
                            data-familiar-id="${familiar.id}"
                            data-familiar-nombre="${familiar.nombre}"
                            title="Eliminar familiar">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    }

    // Función para mostrar detalles del familiar
    function mostrarDetallesFamiliar(familiarId, familiarNombre) {
        console.log('Intentando obtener detalles del familiar:', familiarId);

        // Obtener detalles del familiar via AJAX
        fetch(`/usuarios/familiar/${familiarId}/detalles/`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            if (data.success) {
                let telefonosHtml = '';
                if (data.telefonos && data.telefonos.length > 0) {
                    telefonosHtml = data.telefonos.map(tel =>
                        `<div class="d-flex justify-content-between align-items-center mb-2">
                            <span><i class="fas fa-phone me-2"></i>${tel.numero}</span>
                            <span class="badge bg-${tel.is_active ? 'success' : 'secondary'}">${tel.is_active ? 'Activo' : 'Inactivo'}</span>
                        </div>`
                    ).join('');
                } else {
                    telefonosHtml = '<p class="text-muted">No hay teléfonos de emergencia registrados.</p>';
                }

                Swal.fire({
                    title: `<i class="fas fa-user me-2"></i>Detalles de ${familiarNombre}`,
                    html: `
                        <div class="text-start">
                            <p><strong>Nombre:</strong> ${data.familiar.nombre}</p>
                            <p><strong>Parentesco:</strong> ${data.familiar.parentesco}</p>
                            <p><strong>Estado:</strong>
                                <span class="badge bg-${data.familiar.is_active ? 'success' : 'secondary'}">
                                    ${data.familiar.is_active ? 'Activo' : 'Inactivo'}
                                </span>
                            </p>
                            <hr>
                            <h6><i class="fas fa-phone me-2"></i>Teléfonos de Emergencia:</h6>
                            ${telefonosHtml}
                        </div>
                    `,
                    width: '500px',
                    confirmButtonText: 'Cerrar',
                    confirmButtonColor: '#1A237E'
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'No se pudieron cargar los detalles del familiar'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor'
            });
        });
    }

    // Función para gestionar teléfonos del familiar
    function mostrarGestionarTelefonos(familiarId, familiarNombre) {
        Swal.fire({
            title: `<i class="fas fa-phone me-2"></i>Gestionar Teléfonos - ${familiarNombre}`,
            html: `
                <div id="telefonos-container">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>Cargando teléfonos...</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-8">
                        <input type="text" id="nuevo-telefono" class="form-control"
                               placeholder="Nuevo número de teléfono"
                               pattern="[0-9\\+\\s\\(\\)\\-]{8,15}">
                    </div>
                    <div class="col-4">
                        <button type="button" id="agregar-telefono-btn" class="btn btn-success w-100">
                            <i class="fas fa-plus me-1"></i>Agregar
                        </button>
                    </div>
                </div>
            `,
            width: '600px',
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: 'Cerrar',
            cancelButtonColor: '#6c757d',
            didOpen: () => {
                cargarTelefonosFamiliar(familiarId);

                // Event listener para agregar teléfono
                document.getElementById('agregar-telefono-btn').addEventListener('click', function() {
                    const nuevoTelefono = document.getElementById('nuevo-telefono').value.trim();
                    if (nuevoTelefono) {
                        agregarTelefonoFamiliar(familiarId, nuevoTelefono);
                    } else {
                        Swal.showValidationMessage('Por favor ingresa un número de teléfono');
                    }
                });
            }
        });
    }

    // Función para cargar teléfonos del familiar
    function cargarTelefonosFamiliar(familiarId) {
        fetch(`/usuarios/familiar/${familiarId}/telefonos/`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('telefonos-container');
            if (data.success && data.telefonos.length > 0) {
                let telefonosHtml = data.telefonos.map(tel => `
                    <div class="telefono-emergencia-item" data-telefono-id="${tel.id}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-phone me-2"></i>${tel.numero}</span>
                            <div>
                                <span class="badge bg-${tel.is_active ? 'success' : 'secondary'} me-2">
                                    ${tel.is_active ? 'Activo' : 'Inactivo'}
                                </span>
                                <button type="button" class="btn btn-sm btn-outline-danger eliminar-telefono-emergencia"
                                        data-telefono-id="${tel.id}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');

                container.innerHTML = telefonosHtml;

                // Agregar event listeners para eliminar teléfonos
                container.querySelectorAll('.eliminar-telefono-emergencia').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const telefonoId = this.dataset.telefonoId;
                        eliminarTelefonoEmergencia(telefonoId, familiarId);
                    });
                });
            } else {
                container.innerHTML = '<p class="text-muted text-center">No hay teléfonos de emergencia registrados.</p>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('telefonos-container').innerHTML =
                '<p class="text-danger text-center">Error al cargar teléfonos</p>';
        });
    }

    // Función para agregar teléfono de emergencia
    function agregarTelefonoFamiliar(familiarId, numero) {
        const formData = new FormData();
        formData.append('numero', numero);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(`/usuarios/familiar/${familiarId}/telefono/agregar/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('nuevo-telefono').value = '';
                cargarTelefonosFamiliar(familiarId);

                // Actualizar contador en la tabla principal
                const contador = document.getElementById(`count-telefonos-${familiarId}`);
                if (contador) {
                    const currentCount = parseInt(contador.textContent.match(/\d+/)[0]) + 1;
                    contador.textContent = `${currentCount} número(s)`;
                }

                Swal.showValidationMessage('');
            } else {
                Swal.showValidationMessage(data.message || 'Error al agregar teléfono');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.showValidationMessage('Error de conexión');
        });
    }

    // Función para eliminar teléfono de emergencia
    function eliminarTelefonoEmergencia(telefonoId, familiarId) {
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(`/usuarios/telefono/${telefonoId}/eliminar/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                cargarTelefonosFamiliar(familiarId);

                // Actualizar contador en la tabla principal
                const contador = document.getElementById(`count-telefonos-${familiarId}`);
                if (contador) {
                    const currentCount = Math.max(0, parseInt(contador.textContent.match(/\d+/)[0]) - 1);
                    contador.textContent = `${currentCount} número(s)`;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // Función para eliminar familiar
    function eliminarFamiliar(familiarId) {
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(`/usuarios/familiar/${familiarId}/eliminar/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remover fila de la tabla
                const familiarRow = document.querySelector(`tr[data-familiar-id="${familiarId}"]`);
                if (familiarRow) {
                    familiarRow.remove();
                }

                // Verificar si quedan familiares
                const tbody = document.getElementById('familiares-tbody');
                if (tbody && tbody.children.length === 0) {
                    // Mostrar estado vacío
                    const familiarTable = document.querySelector('.familiar-table');
                    familiarTable.innerHTML = `
                        <div class="empty-state" id="empty-familiares">
                            <i class="fas fa-users-slash"></i>
                            <p><strong>No hay familiares agregados aún</strong></p>
                            <small>Agrega familiares usando el formulario de arriba.</small>
                        </div>
                    `;
                }

                Swal.fire({
                    icon: 'success',
                    title: '¡Eliminado!',
                    text: data.message || 'Familiar eliminado correctamente',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: data.message || 'Error al eliminar familiar'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor'
            });
        });
    }
    
    function eliminarFamiliar(familiarId) {
        fetch(`{% url 'user:eliminar_familiar_ajax' 0 %}`.replace('0', familiarId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const familiarItem = document.querySelector(`[data-familiar-id="${familiarId}"]`);
                if (familiarItem) {
                    familiarItem.remove();
                }
                
                // Mostrar estado vacío si no hay más familiares
                const familiariesRestantes = document.querySelectorAll('.familiar-item');
                if (familiariesRestantes.length === 0 && emptyState) {
                    emptyState.style.display = 'block';
                }
                
                // Mensaje ya mostrado en la función eliminarFamiliar
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Error al eliminar familiar'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error de conexión',
                text: 'No se pudo conectar con el servidor'
            });
        });
    }
    
    function agregarTelefonoEmergencia(familiarId, numero) {
        const formData = new FormData();
        formData.append('numero', numero);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        
        fetch(`{% url 'user:agregar_telefono_emergencia_ajax' 0 %}`.replace('0', familiarId), {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Agregar teléfono a la lista
                const lista = document.querySelector(`.telefonos-emergencia-lista[data-familiar-id="${familiarId}"]`);
                
                // Remover mensaje de "sin teléfonos"
                const sinTelefonos = lista.querySelector('.text-muted');
                if (sinTelefonos && sinTelefonos.textContent.includes('Sin teléfonos')) {
                    sinTelefonos.remove();
                }
                
                const telefonoItem = document.createElement('div');
                telefonoItem.className = 'telefono-emergencia-item';
                telefonoItem.dataset.telefonoId = data.celular.id;
                telefonoItem.innerHTML = `
                    <span>${data.celular.numero}</span>
                    <button type="button" class="btn btn-sm btn-outline-danger eliminar-telefono-emergencia" 
                            data-telefono-id="${data.celular.id}">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                lista.appendChild(telefonoItem);
                
                // Ocultar formulario y limpiar
                const form = document.querySelector(`.add-telefono-form[data-familiar-id="${familiarId}"]`);
                form.style.display = 'none';
                form.querySelector('input').value = '';
                
                // Mensaje ya manejado en la función agregarTelefonoFamiliar
            } else {
                console.error('Error al agregar teléfono');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
    
    function eliminarTelefonoEmergencia(telefonoId) {
        fetch(`{% url 'user:eliminar_telefono_emergencia_ajax' 0 %}`.replace('0', telefonoId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const telefonoItem = document.querySelector(`[data-telefono-id="${telefonoId}"]`);
                if (telefonoItem) {
                    telefonoItem.remove();
                }
                // Mensaje ya manejado en la función eliminarTelefonoEmergencia
            } else {
                console.error('Error al eliminar teléfono');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
    
    // Función mostrarMensaje eliminada - ahora usamos SweetAlert
});
</script>
{% endblock %}
