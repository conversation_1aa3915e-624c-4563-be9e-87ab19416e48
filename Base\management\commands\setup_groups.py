"""
Base/management/commands/setup_groups.py
Comando de gestión para configurar los grupos y permisos iniciales del sistema.

Crea los grupos:
- Admin: Acceso completo al sistema
- Secretaria: Creación y asignación de tickets
- Empleado: Acceso limitado a tickets asignados

Uso: python manage.py setup_groups
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from user.models import User, CargoUsuario, Parentesco


class Command(BaseCommand):
    help = 'Configura los grupos y permisos iniciales del sistema'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Elimina y recrea todos los grupos y permisos',
        )

    def handle(self, *args, **options):
        """
        Ejecuta la configuración de grupos y permisos.
        """
        self.stdout.write(
            self.style.SUCCESS('Iniciando configuración de grupos y permisos...')
        )

        if options['reset']:
            self.reset_groups()

        # Crear grupos principales
        self.create_groups()
        
        # Configurar permisos
        self.setup_permissions()
        
        # Crear datos iniciales
        self.create_initial_data()

        self.stdout.write(
            self.style.SUCCESS('Configuración completada exitosamente!')
        )

    def reset_groups(self):
        """
        Elimina todos los grupos existentes.
        """
        self.stdout.write('Eliminando grupos existentes...')
        Group.objects.all().delete()

    def create_groups(self):
        """
        Crea los grupos principales del sistema.
        """
        groups_data = [
            {
                'name': 'Admin',
                'description': 'Administradores del sistema con acceso completo'
            },
            {
                'name': 'Secretaria',
                'description': 'Personal de secretaría que crea y asigna tickets'
            },
            {
                'name': 'Empleado',
                'description': 'Empleados que trabajan en tickets asignados'
            },
        ]

        for group_data in groups_data:
            group, created = Group.objects.get_or_create(
                name=group_data['name']
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Grupo "{group.name}" creado')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Grupo "{group.name}" ya existe')
                )

    def setup_permissions(self):
        """
        Configura los permisos para cada grupo.
        """
        # Obtener grupos
        admin_group = Group.objects.get(name='Admin')
        secretaria_group = Group.objects.get(name='Secretaria')
        empleado_group = Group.objects.get(name='Empleado')

        # Configurar permisos para Admin (acceso completo)
        self.setup_admin_permissions(admin_group)
        
        # Configurar permisos para Secretaria
        self.setup_secretaria_permissions(secretaria_group)
        
        # Configurar permisos para Empleado
        self.setup_empleado_permissions(empleado_group)

    def setup_admin_permissions(self, group):
        """
        Configura permisos para el grupo Admin.
        """
        self.stdout.write('Configurando permisos para Admin...')
        
        # Admin tiene todos los permisos
        all_permissions = Permission.objects.all()
        group.permissions.set(all_permissions)
        
        self.stdout.write(
            self.style.SUCCESS(f'Admin: {all_permissions.count()} permisos asignados')
        )

    def setup_secretaria_permissions(self, group):
        """
        Configura permisos para el grupo Secretaria.
        """
        self.stdout.write('Configurando permisos para Secretaria...')
        
        # Permisos específicos para Secretaria
        permission_codenames = [
            # Tickets
            'add_ticket',
            'view_ticket',
            'change_ticket',

            # Ciudadanos
            'add_ciudadano',
            'view_ciudadano',
            'change_ciudadano',
            'add_ciudadanoticket',
            'view_ciudadanoticket',

            # Asignaciones (solo crear)
            'add_asignacionticket',
            'view_asignacionticket',

            # Notificaciones (crear y ver)
            'add_notificacion',
            'view_notificacion',
            'change_notificacion',
            'add_notificacionusuario',
            'view_notificacionusuario',
            'add_notificaciongrupo',
            'view_notificaciongrupo',
        ]
        
        permissions = Permission.objects.filter(codename__in=permission_codenames)
        group.permissions.set(permissions)
        
        self.stdout.write(
            self.style.SUCCESS(f'Secretaria: {permissions.count()} permisos asignados')
        )

    def setup_empleado_permissions(self, group):
        """
        Configura permisos para el grupo Empleado.
        """
        self.stdout.write('Configurando permisos para Empleado...')
        
        # Permisos limitados para Empleado
        permission_codenames = [
            # Tickets (solo ver y cambiar estado)
            'view_ticket',
            'change_ticket',  # Solo para cambiar estado
            
            # Asignaciones (ver y cambiar las propias)
            'view_asignacionticket',
            'change_asignacionticket',
            
            # Ciudadanos (solo ver)
            'view_ciudadano',
            'view_ciudadanoticket',
            
            # Notificaciones (ver)
            'view_notificacion',
            'view_notificacionusuario',
        ]
        
        permissions = Permission.objects.filter(codename__in=permission_codenames)
        group.permissions.set(permissions)
        
        self.stdout.write(
            self.style.SUCCESS(f'Empleado: {permissions.count()} permisos asignados')
        )

    def create_initial_data(self):
        """
        Crea datos iniciales necesarios para el sistema.
        """
        self.stdout.write('Creando datos iniciales...')
        
        # Crear cargos iniciales
        cargos_iniciales = [
            {'nombre': 'Administrador', 'descripcion': 'Administrador del sistema'},
            {'nombre': 'Secretaria', 'descripcion': 'Personal de secretaría'},
            {'nombre': 'Supervisor', 'descripcion': 'Supervisor de área'},
            {'nombre': 'Empleado', 'descripcion': 'Empleado operativo'},
        ]
        
        for cargo_data in cargos_iniciales:
            cargo, created = CargoUsuario.objects.get_or_create(
                nombre=cargo_data['nombre'],
                defaults={'descripcion': cargo_data['descripcion']}
            )
            if created:
                self.stdout.write(f'Cargo "{cargo.nombre}" creado')

        # Crear parentescos iniciales
        parentescos_iniciales = [
            'Padre', 'Madre', 'Hijo/a', 'Hermano/a', 'Esposo/a',
            'Abuelo/a', 'Tío/a', 'Primo/a', 'Cuñado/a', 'Otro'
        ]
        
        for parentesco_nombre in parentescos_iniciales:
            parentesco, created = Parentesco.objects.get_or_create(
                parentesco=parentesco_nombre
            )
            if created:
                self.stdout.write(f'Parentesco "{parentesco.parentesco}" creado')

        self.stdout.write(
            self.style.SUCCESS('Datos iniciales creados correctamente')
        )
