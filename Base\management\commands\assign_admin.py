"""
Base/management/commands/assign_admin.py
Comando para asignar usuarios al grupo Admin y configurar datos adicionales.

Uso: python manage.py assign_admin --username admin
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from user.models import CargoUsuario

User = get_user_model()


class Command(BaseCommand):
    help = 'Asigna un usuario al grupo Admin y configura datos adicionales'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            required=True,
            help='Nombre de usuario a asignar al grupo Admin',
        )
        parser.add_argument(
            '--dpi',
            type=str,
            default='1234567890123',
            help='DPI del usuario (por defecto: 1234567890123)',
        )

    def handle(self, *args, **options):
        username = options['username']
        dpi = options['dpi']
        
        try:
            # Obtener el usuario
            user = User.objects.get(username=username)
            self.stdout.write(f'Usuario encontrado: {user.username}')
            
            # Obtener el grupo Admin
            admin_group = Group.objects.get(name='Admin')
            
            # Asignar al grupo Admin
            user.groups.add(admin_group)
            
            # Configurar datos adicionales si no existen
            if not user.dpi:
                user.dpi = dpi
            
            # Asignar cargo de Administrador
            try:
                cargo_admin = CargoUsuario.objects.get(nombre='Administrador')
                user.cargo = cargo_admin
            except CargoUsuario.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING('Cargo "Administrador" no encontrado')
                )
            
            # Marcar como staff para acceso al admin
            user.is_staff = True
            user.is_superuser = True
            
            user.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Usuario "{username}" asignado exitosamente al grupo Admin'
                )
            )
            
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Usuario "{username}" no encontrado')
            )
        except Group.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Grupo "Admin" no encontrado. Ejecuta: python manage.py setup_groups')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error: {str(e)}')
            )
