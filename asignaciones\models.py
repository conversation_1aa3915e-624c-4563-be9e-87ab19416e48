"""
asignaciones/models.py
Modelos para la gestión de asignaciones de tickets a usuarios.

Incluye:
- AsignacionTicket: Asignación de tickets a usuarios específicos
"""

from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinLengthValidator


class AsignacionTicket(models.Model):
    """
    Modelo para gestionar las asignaciones de tickets a usuarios específicos.

    Permite el seguimiento detallado de quién está trabajando en qué ticket,
    con fechas de inicio, finalización y estados específicos.
    """

    ESTADO_CHOICES = (
        (1, 'Asignado'),
        (2, 'En Progreso'),
        (3, 'Finalizado'),
        (4, 'Cancelado'),
    )

    # Relaciones principales
    ticket = models.ForeignKey(
        'tickets.Ticket',
        on_delete=models.CASCADE,
        related_name='asignaciones',
        help_text='Ticket asignado'
    )
    usuario = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='asignaciones_recibidas',
        help_text='Usuario asignado al ticket'
    )

    # Fechas de seguimiento
    fecha_asignacion = models.DateTimeField(
        auto_now_add=True,
        help_text='Fecha y hora de la asignación',
        db_index=True
    )
    fecha_inicio = models.DateTimeField(
        blank=True,
        null=True,
        help_text='Fecha y hora de inicio del trabajo'
    )
    fecha_finalizacion = models.DateTimeField(
        blank=True,
        null=True,
        help_text='Fecha y hora de finalización del trabajo'
    )

    # Estado y control
    estado = models.IntegerField(
        choices=ESTADO_CHOICES,
        default=1,
        help_text='Estado actual de la asignación',
        db_index=True
    )
    is_active = models.BooleanField(
        default=True,
        help_text='Indica si la asignación está activa',
        db_index=True
    )

    # Información adicional
    nota = models.TextField(
        blank=True,
        help_text='Observaciones sobre la asignación'
    )

    # Usuario que realizó la asignación
    asignado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='asignaciones_realizadas',
        help_text='Usuario que realizó la asignación'
    )

    class Meta:
        db_table = 'asignacion_ticket'
        verbose_name = 'Asignación de Ticket'
        verbose_name_plural = 'Asignaciones de Tickets'
        ordering = ['-fecha_asignacion']
        indexes = [
            models.Index(fields=['ticket', 'usuario'], name='idx_asignacion_ticket_usuario'),
            models.Index(fields=['usuario', 'estado'], name='idx_asignacion_usuario_estado'),
            models.Index(fields=['fecha_asignacion'], name='idx_asignacion_fecha'),
            models.Index(fields=['is_active'], name='idx_asignacion_activa'),
        ]
        # Evitar asignaciones duplicadas activas
        unique_together = ['ticket', 'usuario', 'is_active']

    def save(self, *args, **kwargs):
        """
        Sobrescribe el método save para sincronizar con el estado del ticket
        cuando se crea una nueva asignación.
        """
        # Si es una nueva asignación, sincronizar con el estado actual del ticket
        if not self.pk:
            self._sincronizar_con_estado_ticket()

        super().save(*args, **kwargs)

    def _sincronizar_con_estado_ticket(self):
        """
        Sincroniza el estado de la asignación con el estado actual del ticket.
        """
        # Mapeo de estados ticket -> asignación
        mapeo_estados = {
            'abierto': 1,        # Asignado
            'en_progreso': 2,    # En Progreso
            'resuelto': 3,       # Finalizado
            'cerrado': 3,        # Finalizado
            'cancelado': 4,      # Cancelado
        }

        estado_ticket = self.ticket.estado
        nuevo_estado = mapeo_estados.get(estado_ticket, 1)  # Default: Asignado

        self.estado = nuevo_estado

        # Si el ticket ya está en progreso o finalizado, ajustar fechas
        if nuevo_estado == 2 and not self.fecha_inicio:  # En Progreso
            self.fecha_inicio = timezone.now()
        elif nuevo_estado in [3, 4] and not self.fecha_finalizacion:  # Finalizado/Cancelado
            if not self.fecha_inicio:
                self.fecha_inicio = timezone.now()
            self.fecha_finalizacion = timezone.now()

    def __str__(self):
        return f"Ticket #{self.ticket.id} → {self.usuario.get_full_name() or self.usuario.username}"

    def get_estado_display_color(self):
        """
        Retorna el color CSS asociado al estado de la asignación.

        Returns:
            str: Clase CSS para el color del estado
        """
        colors = {
            1: 'info',       # Asignado - Azul claro
            2: 'warning',    # En Progreso - Amarillo
            3: 'success',    # Finalizado - Verde
            4: 'danger',     # Cancelado - Rojo
        }
        return colors.get(self.estado, 'secondary')

    def get_tiempo_asignado(self):
        """
        Calcula el tiempo total que ha estado asignado el ticket.

        Returns:
            timedelta: Tiempo transcurrido desde la asignación
        """
        fin = self.fecha_finalizacion or timezone.now()
        return fin - self.fecha_asignacion

    def get_fecha_para_timesince(self):
        """
        Devuelve la fecha de asignación para usar con el filtro timesince.

        Returns:
            datetime: Fecha de asignación
        """
        return self.fecha_asignacion

    def get_tiempo_trabajado(self):
        """
        Calcula el tiempo efectivo de trabajo (desde inicio hasta fin).

        Returns:
            timedelta: Tiempo trabajado o None si no ha iniciado
        """
        if not self.fecha_inicio:
            return None

        fin = self.fecha_finalizacion or timezone.now()
        return fin - self.fecha_inicio

    def iniciar_trabajo(self, usuario=None):
        """
        Marca el inicio del trabajo en la asignación.

        Args:
            usuario: Usuario que inicia el trabajo
        """
        if not self.fecha_inicio:
            self.fecha_inicio = timezone.now()
            self.estado = 2  # En Progreso
            self.save()

            # Registrar en historial del ticket
            from tickets.models import HistorialTicket
            HistorialTicket.registrar_cambio(
                ticket=self.ticket,
                usuario=usuario or self.usuario,
                accion='Trabajo iniciado en asignación',
                detalles={
                    'usuario_asignado': self.usuario.username,
                    'fecha_inicio': self.fecha_inicio.isoformat()
                }
            )

    def finalizar_trabajo(self, usuario=None, nota_finalizacion=None):
        """
        Marca la finalización del trabajo en la asignación.

        Args:
            usuario: Usuario que finaliza el trabajo
            nota_finalizacion: Nota adicional sobre la finalización
        """
        self.fecha_finalizacion = timezone.now()
        self.estado = 3  # Finalizado

        if nota_finalizacion:
            self.nota = f"{self.nota}\n\nFinalización: {nota_finalizacion}".strip()

        self.save()

        # Registrar en historial del ticket
        from tickets.models import HistorialTicket
        HistorialTicket.registrar_cambio(
            ticket=self.ticket,
            usuario=usuario or self.usuario,
            accion='Trabajo finalizado en asignación',
            detalles={
                'usuario_asignado': self.usuario.username,
                'fecha_finalizacion': self.fecha_finalizacion.isoformat(),
                'tiempo_trabajado': str(self.get_tiempo_trabajado()) if self.get_tiempo_trabajado() else None
            }
        )

    def cancelar_asignacion(self, usuario=None, motivo=None):
        """
        Cancela la asignación.

        Args:
            usuario: Usuario que cancela la asignación
            motivo: Motivo de la cancelación
        """
        self.estado = 4  # Cancelado
        self.is_active = False

        if motivo:
            self.nota = f"{self.nota}\n\nCancelación: {motivo}".strip()

        self.save()

        # Registrar en historial del ticket
        from tickets.models import HistorialTicket
        HistorialTicket.registrar_cambio(
            ticket=self.ticket,
            usuario=usuario or self.asignado_por,
            accion='Asignación cancelada',
            detalles={
                'usuario_asignado': self.usuario.username,
                'motivo': motivo or 'Sin motivo especificado'
            }
        )

    def puede_ser_gestionada_por(self, usuario):
        """
        Verifica si un usuario puede gestionar esta asignación.

        Args:
            usuario: Usuario a verificar

        Returns:
            bool: True si puede gestionar, False en caso contrario
        """
        # Admin puede gestionar cualquier asignación
        if usuario.groups.filter(name='Admin').exists():
            return True

        # Supervisor puede gestionar asignaciones de su grupo
        if usuario.is_supervisor:
            return usuario.groups.filter(id=self.ticket.grupo.id).exists()

        # El usuario asignado puede gestionar su propia asignación
        return self.usuario == usuario
