<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error del Servidor - Sistema de Tickets</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .error-icon {
            font-size: 6rem;
            color: var(--danger-color);
            margin-bottom: 1rem;
            animation: shake 1s infinite;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: var(--danger-color);
            margin-bottom: 0.5rem;
        }

        .error-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .error-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn-outline-secondary {
            border: 2px solid #6c757d;
            color: #6c757d;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: var(--danger-color);
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }

        .technical-info {
            background: #f8f9fa;
            border-left: 4px solid var(--danger-color);
            padding: 1rem;
            margin-top: 2rem;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }

        .technical-info h6 {
            color: var(--danger-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .technical-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #fff3cd;
            color: #856404;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            margin-top: 1rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--warning-color);
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .retry-btn {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <div class="error-code">500</div>
        <h1 class="error-title">Error Interno del Servidor</h1>
        
        <p class="error-description">
            Oops! Algo salió mal en nuestros servidores. Nuestro equipo técnico ha sido notificado
            automáticamente y está trabajando para resolver el problema.
        </p>

        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>Equipo técnico notificado - Trabajando en la solución</span>
        </div>

        <div class="d-flex gap-3 justify-content-center flex-wrap mt-4">
            <button onclick="location.reload()" class="btn btn-danger retry-btn">
                <i class="fas fa-redo me-2"></i>Intentar de Nuevo
            </button>
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Volver Atrás
            </a>
            <a href="/inicio/" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Ir al Dashboard
            </a>
        </div>

        <div class="technical-info">
            <h6><i class="fas fa-tools me-2"></i>Información Técnica</h6>
            <p>
                <strong>Código de Error:</strong> 500 - Internal Server Error<br>
                <strong>Timestamp:</strong> <span id="timestamp"></span><br>
                <strong>Acción recomendada:</strong> Espera unos minutos e intenta nuevamente. Si el problema persiste, contacta al administrador.
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Mostrar timestamp actual
        document.getElementById('timestamp').textContent = new Date().toLocaleString('es-ES');

        // Auto-retry después de 30 segundos
        let retryCount = 0;
        const maxRetries = 3;
        
        function autoRetry() {
            if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(() => {
                    console.log(`Auto-retry attempt ${retryCount}/${maxRetries}`);
                    location.reload();
                }, 30000);
            }
        }

        // Iniciar auto-retry
        autoRetry();

        // Mostrar contador de retry
        let countdown = 30;
        const retryBtn = document.querySelector('.retry-btn');
        const originalText = retryBtn.innerHTML;

        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                retryBtn.innerHTML = `<i class="fas fa-redo me-2"></i>Reintentando en ${countdown}s`;
            } else {
                retryBtn.innerHTML = originalText;
                clearInterval(countdownInterval);
            }
        }, 1000);
    </script>
</body>
</html>
