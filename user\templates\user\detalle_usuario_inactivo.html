{% extends 'Base/base.html' %}
{% block title %}Detalle de Usuario Inactivo{% endblock %}

{% block content %}
<div class="card mb-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">{{ usuario.get_full_name }}</h4>
            <span class="badge bg-danger">
                <i class="fas fa-user-slash me-1"></i>Usuario Inactivo
            </span>
        </div>
        <div>
            <a href="{% url 'user:lista_usuarios_inactivos' %}" class="btn btn-outline-secondary btn-sm me-2">
                <i class="fas fa-arrow-left me-1"></i>Volver a Inactivos
            </a>
            <button type="button" class="btn btn-success btn-sm" 
                    onclick="activarUsuario({{ usuario.id }}, '{{ usuario.get_full_name }}')">
                <i class="fas fa-user-check me-1"></i>Activar Usuario
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- Alerta de estado inactivo -->
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Usuario Inactivo:</strong> Este usuario no puede acceder al sistema. 
            Para permitir el acceso, debe activarlo usando el botón "Activar Usuario".
        </div>

        <dl class="row">
            <dt class="col-sm-3">DPI</dt>
            <dd class="col-sm-9">{{ usuario.dpi }}</dd>
            <dt class="col-sm-3">Usuario</dt>
            <dd class="col-sm-9">{{ usuario.username }}</dd>
            <dt class="col-sm-3">Cargo</dt>
            <dd class="col-sm-9">{{ usuario.cargo.nombre }}</dd>
            <dt class="col-sm-3">Estado</dt>
            <dd class="col-sm-9">
                <span class="badge bg-danger">
                    <i class="fas fa-user-slash me-1"></i>Inactivo
                </span>
                <small class="text-muted ms-2">No puede acceder al sistema</small>
            </dd>
            <dt class="col-sm-3">Fecha de nacimiento</dt>
            <dd class="col-sm-9">{{ usuario.fecha_nacimiento }}</dd>
            <dt class="col-sm-3">Género</dt>
            <dd class="col-sm-9">{{ usuario.get_genero_display }}</dd>
        </dl>
    </div>
</div>

<!-- Teléfonos -->
<div class="card mb-3">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-phone me-2"></i>Teléfonos
        </h5>
    </div>
    <div class="card-body">
        {% if usuario.celulares.all %}
            <div class="row">
                {% for celular in usuario.celulares.all %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-phone text-info me-2"></i>
                            <span class="me-2">{{ celular.numero }}</span>
                            <span class="badge bg-info">{{ celular.get_tipo_display }}</span>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-muted mb-0">
                <i class="fas fa-phone-slash me-2"></i>No hay teléfonos registrados
            </p>
        {% endif %}
    </div>
</div>

<!-- Familiares -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>Familiares
        </h5>
    </div>
    <div class="card-body">
        {% if usuario.familiares.all %}
            <div class="row">
                {% for familiar in usuario.familiares.all %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-light">
                            <div class="card-body p-3">
                                <h6 class="card-title mb-2">
                                    <i class="fas fa-user me-2"></i>{{ familiar.nombre }}
                                </h6>
                                <p class="card-text mb-2">
                                    <small class="text-muted">{{ familiar.parentesco }}</small>
                                </p>
                                {% if familiar.celulares_emergencia.all %}
                                    <div class="mt-2">
                                        <small class="text-muted d-block mb-1">Teléfonos de emergencia:</small>
                                        {% for telefono in familiar.celulares_emergencia.all %}
                                            <span class="badge bg-warning text-dark me-1">
                                                <i class="fas fa-phone me-1"></i>{{ telefono.numero }}
                                            </span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-muted mb-0">
                <i class="fas fa-user-slash me-2"></i>No hay familiares registrados
            </p>
        {% endif %}
    </div>
</div>

<style>
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.alert {
    border-left: 4px solid #ffc107;
}

.card-title {
    color: #495057;
}

.border-light {
    border-color: #e9ecef !important;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Función para activar usuario (reutilizada del template de lista)
function activarUsuario(userId, nombre) {
    Swal.fire({
        title: '¿Activar usuario?',
        text: `Se activará a ${nombre} y podrá acceder al sistema`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, activar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/usuarios/usuarios/${userId}/activar/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Usuario activado',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        // Redirigir a la lista de usuarios inactivos
                        window.location.href = "{% url 'user:lista_usuarios_inactivos' %}";
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}
</script>

{% csrf_token %}
{% endblock %}
