{% load asignaciones_tags %}
{% for item in items %}
    {% if item.tipo == 'ticket_sin_asignar' %}
        <!-- Ticket sin asignar -->
        {% with ticket=item.ticket %}
            <div class="border-bottom p-3 ticket-sin-asignar">
                <div class="d-flex align-items-start justify-content-between">
                    <div class="d-flex align-items-start flex-grow-1">
                        <div class="avatar-sm me-3 bg-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">
                                    <a href="{% url 'tickets:detalle_ticket' ticket.id %}" class="text-decoration-none">
                                        Ticket #{{ ticket.id }} - {{ ticket.titulo|truncatechars:50 }}
                                    </a>
                                </h6>
                                <span class="badge bg-warning text-dark">SIN ASIGNAR</span>
                                <span class="badge bg-{{ ticket.get_prioridad_display_color }} ms-1">
                                    {{ ticket.get_prioridad_display|title }}
                                </span>
                            </div>
                            <p class="text-muted mb-2">{{ ticket.descripcion|truncatechars:100 }}</p>
                            <div class="d-flex align-items-center text-sm">
                                <span class="me-3">
                                    <i class="fas fa-building me-1"></i>{{ ticket.grupo.name }}
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-user me-1"></i>{{ ticket.creado_por.get_full_name|default:ticket.creado_por.username }}
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ ticket.fecha_creacion|timesince }} atrás
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="ms-3">
                        <div class="btn-group" role="group">
                            <!-- Botón Ver Detalle -->
                            <a href="{% url 'tickets:detalle_ticket' ticket.id %}"
                               class="btn btn-sm btn-outline-primary" title="Ver detalle del ticket">
                                <i class="fas fa-eye me-1"></i>Ver Detalle
                            </a>

                            <!-- Botón Asignar -->
                            {% if user|can_assign_ticket:ticket %}
                                <button type="button" class="btn btn-sm btn-warning"
                                        onclick="mostrarModalAsignar({{ ticket.id }}, '{{ ticket.titulo|escapejs }}')">
                                    <i class="fas fa-user-plus me-1"></i>Asignar
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endwith %}
    {% elif item.tipo == 'ticket_con_asignaciones' %}
        <!-- Ticket con múltiples asignaciones agrupadas -->
        {% with ticket=item.ticket asignaciones=item.asignaciones ultima_asignacion=item.ultima_asignacion %}
            <div class="border-bottom p-3 ticket-con-asignaciones">
                <div class="d-flex align-items-start justify-content-between">
                    <div class="d-flex align-items-start flex-grow-1">
                        <div class="avatar-sm me-3 bg-{{ ultima_asignacion.get_estado_display_color }}">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">
                                    <a href="{% url 'tickets:detalle_ticket' ticket.id %}" class="text-decoration-none">
                                        Ticket #{{ ticket.id }} - {{ ticket.titulo|truncatechars:50 }}
                                    </a>
                                </h6>
                                <span class="badge bg-{{ ultima_asignacion.get_estado_display_color }}">
                                    {{ ultima_asignacion.get_estado_display }}
                                </span>
                                <span class="badge bg-{{ ticket.get_prioridad_display_color }} ms-1">
                                    {{ ticket.get_prioridad_display|title }}
                                </span>
                                <span class="badge bg-info ms-1">
                                    {{ item.total_asignaciones }} asignación{{ item.total_asignaciones|pluralize:"es" }}
                                </span>
                            </div>

                            <p class="text-muted mb-2">{{ ticket.descripcion|truncatechars:100 }}</p>

                            <!-- Lista de usuarios asignados -->
                            <div class="mb-2">
                                <small class="text-muted d-block mb-1">
                                    <i class="fas fa-users me-1"></i>Usuarios asignados:
                                </small>
                                <div class="d-flex flex-wrap gap-1">
                                    {% for asignacion in asignaciones %}
                                        <span class="badge bg-{{ asignacion.get_estado_display_color }} me-1">
                                            <i class="fas fa-user me-1"></i>
                                            {{ asignacion.usuario.get_full_name|default:asignacion.usuario.username }}
                                            {% if asignacion.estado == 2 %}
                                                <i class="fas fa-play ms-1" title="En progreso"></i>
                                            {% elif asignacion.estado == 3 %}
                                                <i class="fas fa-check ms-1" title="Finalizado"></i>
                                            {% endif %}
                                        </span>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="d-flex align-items-center text-sm">
                                <span class="me-3">
                                    <i class="fas fa-building me-1"></i>{{ ticket.grupo.name }}
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-user me-1"></i>{{ ticket.creado_por.get_full_name|default:ticket.creado_por.username }}
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ ultima_asignacion.fecha_asignacion|timesince }} atrás
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Acciones para tickets con asignaciones -->
                    <div class="ms-3">
                        <div class="btn-group" role="group">
                            <!-- Botón Ver Detalle -->
                            <a href="{% url 'tickets:detalle_ticket' ticket.id %}"
                               class="btn btn-sm btn-outline-primary" title="Ver detalle del ticket">
                                <i class="fas fa-eye me-1"></i>Ver Detalle
                            </a>

                            <!-- Dropdown de Acciones -->
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-cog me-1"></i>Acciones
                                </button>
                                <ul class="dropdown-menu">
                                    {% if user|can_assign_ticket:ticket %}
                                        <li>
                                            <button class="dropdown-item" type="button"
                                                    onclick="mostrarModalAsignar({{ ticket.id }}, '{{ ticket.titulo|escapejs }}')">
                                                <i class="fas fa-user-plus me-2"></i>Asignar más usuarios
                                            </button>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                    {% endif %}

                                    <!-- Acciones por asignación -->
                                    {% for asignacion in asignaciones %}
                                        {% if asignacion.estado == 1 and asignacion.usuario == user %}
                                            <li>
                                                <button class="dropdown-item text-success" type="button"
                                                        onclick="iniciarTrabajo({{ asignacion.id }})">
                                                    <i class="fas fa-play me-2"></i>Iniciar mi trabajo
                                                </button>
                                            </li>
                                        {% elif asignacion.estado == 2 and asignacion.usuario == user %}
                                            <li>
                                                <button class="dropdown-item text-primary" type="button"
                                                        onclick="finalizarTrabajo({{ asignacion.id }})">
                                                    <i class="fas fa-check me-2"></i>Finalizar mi trabajo
                                                </button>
                                            </li>
                                        {% endif %}

                                        {% if user|can_assign_ticket:ticket %}
                                            <li>
                                                <button class="dropdown-item text-danger" type="button"
                                                        onclick="cancelarAsignacion({{ asignacion.id }}, '{{ asignacion.usuario.get_full_name|default:asignacion.usuario.username|escapejs }}')">
                                                    <i class="fas fa-user-times me-2"></i>Desasignar {{ asignacion.usuario.get_full_name|default:asignacion.usuario.username }}
                                                </button>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endwith %}
    {% else %}
        <!-- Asignación individual (legacy) -->
        {% with asignacion=item.asignacion %}
            <div class="border-bottom p-3 asignacion-item">
                <div class="d-flex align-items-start justify-content-between">
                    <div class="d-flex align-items-start flex-grow-1">
                        <div class="avatar-sm me-3 bg-{{ asignacion.get_estado_display_color }}">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 me-2">
                                    <a href="{% url 'tickets:detalle_ticket' asignacion.ticket.id %}" class="text-decoration-none">
                                        Ticket #{{ asignacion.ticket.id }} - {{ asignacion.ticket.titulo|truncatechars:50 }}
                                    </a>
                                </h6>
                                <span class="badge bg-{{ asignacion.get_estado_display_color }}">
                                    {{ asignacion.get_estado_display }}
                                </span>
                                <span class="badge bg-{{ asignacion.ticket.get_prioridad_display_color }} ms-1">
                                    {{ asignacion.ticket.get_prioridad_display|title }}
                                </span>
                            </div>
                            
                            <div class="d-flex align-items-center mb-2">
                                <div class="avatar-xs me-2">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <span class="fw-bold text-primary">
                                    {{ asignacion.usuario.get_full_name|default:asignacion.usuario.username }}
                                </span>
                                {% if asignacion.asignado_por %}
                                    <small class="text-muted ms-2">
                                        (asignado por {{ asignacion.asignado_por.get_full_name|default:asignacion.asignado_por.username }})
                                    </small>
                                {% endif %}
                            </div>
                            
                            <p class="text-muted mb-2">{{ asignacion.ticket.descripcion|truncatechars:100 }}</p>
                            
                            <div class="d-flex align-items-center text-sm">
                                <span class="me-3">
                                    <i class="fas fa-building me-1"></i>{{ asignacion.ticket.grupo.name }}
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-calendar me-1"></i>{{ asignacion.fecha_asignacion|date:"d/m/Y H:i" }}
                                </span>
                                {% if asignacion.fecha_inicio %}
                                    <span class="me-3 text-info">
                                        <i class="fas fa-play me-1"></i>Iniciado: {{ asignacion.fecha_inicio|date:"d/m H:i" }}
                                    </span>
                                {% endif %}
                                {% if asignacion.fecha_finalizacion %}
                                    <span class="text-success">
                                        <i class="fas fa-check me-1"></i>Finalizado: {{ asignacion.fecha_finalizacion|date:"d/m H:i" }}
                                    </span>
                                {% endif %}
                            </div>
                            
                            {% if asignacion.nota %}
                                <div class="mt-2">
                                    <small class="text-info">
                                        <i class="fas fa-sticky-note me-1"></i>{{ asignacion.nota|truncatechars:80 }}
                                    </small>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Acciones -->
                    <div class="ms-3">
                        <div class="btn-group" role="group">
                            <a href="{% url 'tickets:detalle_ticket' asignacion.ticket.id %}" 
                               class="btn btn-sm btn-outline-info" title="Ver ticket">
                                <i class="fas fa-eye"></i>
                            </a>
                            
                            {% if asignacion.usuario == user %}
                                {% if asignacion.estado == 1 %}
                                    <!-- Puede iniciar trabajo -->
                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                            onclick="iniciarTrabajo({{ asignacion.id }})" title="Iniciar trabajo">
                                        <i class="fas fa-play"></i>
                                    </button>
                                {% elif asignacion.estado == 2 %}
                                    <!-- Puede finalizar trabajo -->
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="finalizarTrabajo({{ asignacion.id }})" title="Finalizar trabajo">
                                        <i class="fas fa-check"></i>
                                    </button>
                                {% endif %}
                            {% endif %}
                            
                            {% if user|can_manage_assignment:asignacion %}
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="cancelarAsignacion({{ asignacion.id }}, '{{ asignacion.usuario.get_full_name|default:asignacion.usuario.username|escapejs }}')" 
                                        title="Cancelar asignación">
                                    <i class="fas fa-times"></i>
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endwith %}
    {% endif %}
{% endfor %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.avatar-xs {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(45deg, #17a2b8, #138496);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.bg-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800) !important;
}

.bg-info {
    background: linear-gradient(45deg, #17a2b8, #138496) !important;
}

.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
}

.bg-danger {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

.ticket-sin-asignar {
    background-color: rgba(255, 193, 7, 0.05);
    border-left: 4px solid #ffc107;
}

.asignacion-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.text-sm {
    font-size: 0.875rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>

<script>
// Funciones para gestión de asignaciones
function iniciarTrabajo(asignacionId) {
    Swal.fire({
        title: '¿Iniciar trabajo?',
        text: '¿Confirma que desea iniciar el trabajo en esta asignación?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, iniciar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            // Mostrar loading
            Swal.fire({
                title: 'Iniciando trabajo...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/asignaciones/${asignacionId}/iniciar/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '¡Trabajo iniciado!',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}

function finalizarTrabajo(asignacionId) {
    Swal.fire({
        title: '¿Finalizar trabajo?',
        text: 'Ingrese una nota sobre la finalización (opcional):',
        icon: 'question',
        input: 'textarea',
        inputPlaceholder: 'Descripción del trabajo realizado, observaciones, etc...',
        showCancelButton: true,
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Finalizar trabajo',
        cancelButtonText: 'Cancelar',
        inputValidator: (value) => {
            // La nota es opcional, no validamos
            return null;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const nota = result.value || '';
            const formData = new FormData();
            formData.append('nota', nota);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            // Mostrar loading
            Swal.fire({
                title: 'Finalizando trabajo...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/asignaciones/${asignacionId}/finalizar/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '¡Trabajo finalizado!',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}

function cancelarAsignacion(asignacionId, usuarioNombre) {
    Swal.fire({
        title: '¿Cancelar asignación?',
        text: `Se cancelará la asignación de ${usuarioNombre}`,
        icon: 'warning',
        input: 'textarea',
        inputLabel: 'Motivo de la cancelación',
        inputPlaceholder: 'Explique por qué se cancela esta asignación...',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sí, cancelar asignación',
        cancelButtonText: 'No cancelar',
        inputValidator: (value) => {
            if (!value || value.trim() === '') {
                return 'Debe proporcionar un motivo para cancelar la asignación';
            }
            return null;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const motivo = result.value.trim();
            const formData = new FormData();
            formData.append('motivo', motivo);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            // Mostrar loading
            Swal.fire({
                title: 'Cancelando asignación...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/asignaciones/${asignacionId}/cancelar/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Asignación cancelada',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor'
                });
            });
        }
    });
}
</script>
