{% extends 'Base/base.html' %}
{% load static %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #1A237E;
        --secondary-color: #283593;
        --success-color: #28a745;
        --light-gray: #e9ecef;
        --dark-gray: #6c757d;
    }
    
    .progress-container {
        margin-bottom: 3rem;
        padding: 0 2rem;
    }
    
    .progress-bar-custom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        margin-bottom: 1rem;
    }
    
    .progress-line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 4px;
        background-color: var(--light-gray);
        z-index: 1;
        border-radius: 2px;
    }
    
    .progress-line-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
        transition: width 0.3s ease;
    }
    
    .step-item {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: white;
        padding: 0.5rem;
    }
    
    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        border: 3px solid var(--light-gray);
        background: white;
        color: var(--dark-gray);
    }
    
    .step-item.active .step-circle {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
    }
    
    .step-item.completed .step-circle {
        background: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }
    
    .step-label {
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
        color: var(--dark-gray);
        transition: color 0.3s ease;
    }
    
    .step-item.active .step-label {
        color: var(--primary-color);
        font-weight: 600;
    }
    
    .step-item.completed .step-label {
        color: var(--success-color);
        font-weight: 600;
    }
    
    .form-section {
        background: white;
        border-radius: 15px;
        padding: 2.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
        border: 1px solid rgba(26, 35, 126, 0.1);
    }
    
    .section-title {
        color: var(--primary-color);
        border-bottom: 3px solid var(--light-gray);
        padding-bottom: 0.75rem;
        margin-bottom: 2rem;
        position: relative;
    }
    
    .section-title::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
    }
    
    /* Estilos para elementos dinámicos */
    .telefono-item, .familiar-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }
    
    .telefono-item:hover, .familiar-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-color: var(--primary-color);
    }
    
    .telefono-info, .familiar-info {
        flex-grow: 1;
    }
    
    .telefono-actions, .familiar-actions {
        margin-left: 1rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--dark-gray);
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px dashed #dee2e6;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
        color: var(--primary-color);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .progress-container {
            padding: 0 1rem;
        }
        
        .step-circle {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .step-label {
            font-size: 0.8rem;
        }
        
        .form-section {
            padding: 1.5rem;
        }
        
        .progress-bar-custom {
            margin-bottom: 0.5rem;
        }
    }
    
    @media (max-width: 576px) {
        .step-label {
            font-size: 0.7rem;
            line-height: 1.2;
        }
        
        .step-circle {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
        
        .form-section {
            padding: 1rem;
            margin: 0 0.5rem 2rem 0.5rem;
        }
        
        .telefono-item, .familiar-item {
            padding: 0.75rem;
        }
    }
    
    /* Mejoras para radio buttons */
    .form-check-input[type="radio"] {
        margin-right: 0.5rem;
    }
    
    .form-check {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        border-radius: 8px;
        transition: background-color 0.2s ease;
    }
    
    .form-check:hover {
        background-color: rgba(26, 35, 126, 0.05);
    }
    
    .form-check-input:checked + .form-check-label {
        color: var(--primary-color);
        font-weight: 600;
    }
    
    /* Animaciones para elementos dinámicos */
    .telefono-item, .familiar-item {
        animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Estilos para botones de acción */
    .btn-outline-danger:hover {
        transform: scale(1.05);
    }
    
    .btn-outline-success:hover {
        transform: scale(1.05);
    }
    
    .btn-outline-info:hover {
        transform: scale(1.05);
    }
</style>
{% block extra_step_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Barra de progreso moderna -->
            <div class="progress-container">
                <div class="progress-bar-custom">
                    <div class="progress-line">
                        <div class="progress-line-fill" style="width: {% block progress_width %}33.33%{% endblock %};"></div>
                    </div>
                    
                    <div class="step-item {% if paso_actual == 1 %}active{% elif paso_actual > 1 %}completed{% endif %}">
                        <div class="step-circle">{% if paso_actual > 1 %}<i class="fas fa-check"></i>{% else %}1{% endif %}</div>
                        <div class="step-label">Información<br>Básica</div>
                    </div>
                    
                    <div class="step-item {% if paso_actual == 2 %}active{% elif paso_actual > 2 %}completed{% endif %}">
                        <div class="step-circle">{% if paso_actual > 2 %}<i class="fas fa-check"></i>{% else %}2{% endif %}</div>
                        <div class="step-label">Teléfonos</div>
                    </div>
                    
                    <div class="step-item {% if paso_actual == 3 %}active{% endif %}">
                        <div class="step-circle">3</div>
                        <div class="step-label">Familiares</div>
                    </div>
                </div>
            </div>
            
            {% block step_content %}{% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% block step_js %}{% endblock %}
{% endblock %}
