{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Dashboard - Inicio{% endblock %}

<!-- Token CSRF para peticiones AJAX -->
{% csrf_token %}

{% block extra_js %}
<!-- JavaScript de notificaciones -->
<script src="{% static 'notificaciones/js/notificaciones.js' %}"></script>
{% endblock %}

{% block extra_css %}
<style>
.dashboard-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    border-radius: 12px;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.notification-item {
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
    border-left-color: #007bff;
}

.notification-item.tipo-ticket {
    border-left-color: #007bff;
}

.notification-item.tipo-success {
    border-left-color: #28a745;
}

.notification-item.tipo-warning {
    border-left-color: #ffc107;
}

.notification-item.tipo-error {
    border-left-color: #dc3545;
}

.notification-item.tipo-info {
    border-left-color: #17a2b8;
}

/* Estilos para botones de acción rápida */
a[style*="background-color"]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    transition: all 0.2s ease !important;
}
</style>
{% endblock %}

{% block content %}
<!-- Header de bienvenida -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-1">¡Bienvenido, {{ user.get_full_name|default:user.username }}!</h2>
            </div>
            <div class="text-end">
                <span class="badge bg-primary fs-6">
                    {% if permisos.es_administrador %}
                        <i class="fas fa-crown me-1"></i>Administrador
                    {% elif permisos.es_supervisor %}
                        <i class="fas fa-users me-1"></i>Supervisor
                    {% elif permisos.es_secretaria %}
                        <i class="fas fa-user-tie me-1"></i>Secretaria
                    {% else %}
                        <i class="fas fa-user me-1"></i>Empleado
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Columna izquierda: Estadísticas y accesos rápidos -->
    <div class="col-lg-8">


        <!-- Accesos rápidos -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Accesos Rápidos
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Accesos rápidos por cargo -->

                            <!-- Solo Admin, Supervisor y Secretaria pueden ver tickets -->
                            {% if not user_permissions.is_empleado %}
                            <div class="col-md-3 mb-3">
                                <a href="{% url 'tickets:lista_tickets' %}"
                                   style="background-color: #007bff; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                    <i class="fas fa-list" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                    <strong style="color: white;">Ver Tickets</strong>
                                </a>
                            </div>
                            {% endif %}

                            <!-- Solo Admin y Secretaria pueden crear tickets -->
                            {% if user_permissions.can_create_tickets %}
                            <div class="col-md-3 mb-3">
                                <a href="{% url 'tickets:crear_ticket' %}"
                                   style="background-color: #28a745; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                    <i class="fas fa-plus" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                    <strong style="color: white;">Crear Ticket</strong>
                                </a>
                            </div>
                            {% endif %}

                            <!-- Todos pueden ver sus asignaciones -->
                            <div class="col-md-3 mb-3">
                                <a href="{% url 'asignaciones:mis_asignaciones' %}"
                                   style="background-color: #17a2b8; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                    <i class="fas fa-tasks" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                    <strong style="color: white;">Mis Asignaciones</strong>
                                </a>
                            </div>

                            <!-- Solo Administradores y Supervisores pueden gestionar asignaciones -->
                            {% if permisos.puede_gestionar_asignaciones %}
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'asignaciones:lista_asignaciones' %}"
                                       style="background-color: #ffc107; color: #212529; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                        <i class="fas fa-users" style="font-size: 2rem; display: block; margin-bottom: 10px; color: #212529;"></i>
                                        <strong style="color: #212529;">Gestionar Asignaciones</strong>
                                    </a>
                                </div>
                            {% endif %}

                            <!-- Solo Administradores y Supervisores pueden ver reportes -->
                            {% if permisos.puede_ver_reportes %}
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'tickets:reportes' %}"
                                       style="background-color: #17a2b8; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                        <i class="fas fa-chart-bar" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                        <strong style="color: white;">Ver Reportes</strong>
                                    </a>
                                </div>
                            {% endif %}

                            <!-- Solo Administradores, Supervisores y Secretarias pueden gestionar ciudadanos -->
                            {% if permisos.puede_gestionar_ciudadanos %}
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'ciudadano:lista_ciudadanos' %}"
                                       style="background-color: #6c757d; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                        <i class="fas fa-address-book" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                        <strong style="color: white;">Gestionar Ciudadanos</strong>
                                    </a>
                                </div>
                            {% endif %}

                            <!-- Acceso a mis notificaciones (todos los usuarios) -->
                            <div class="col-md-3 mb-3">
                                <a href="{% url 'notificaciones:mis_notificaciones' %}"
                                   style="background-color: #dc3545; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center; position: relative;">
                                    <i class="fas fa-bell" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                    <strong style="color: white;">Mis Notificaciones</strong>
                                    <span class="notification-count badge bg-warning position-absolute top-0 end-0 m-2 d-none">0</span>
                                </a>
                            </div>

                            <!-- Debug de permisos (temporal) -->
                            <div class="col-md-3 mb-3">
                                <a href="{% url 'notificaciones:debug_permisos' %}"
                                   style="background-color: #17a2b8; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                    <i class="fas fa-bug" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                    <strong style="color: white;">Debug Permisos</strong>
                                </a>
                            </div>

                            <!-- Solo usuarios con permisos pueden crear notificaciones -->
                            {% if user_permissions.can_create_notifications %}
                                <div class="col-md-3 mb-3">
                                    <div class="dropdown">
                                        <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown"
                                           style="background-color: #fd7e14; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                            <i class="fas fa-paper-plane" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                            <strong style="color: white;">Crear Notificación</strong>
                                        </a>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="{% url 'notificaciones:crear_notificacion_usuario' %}">
                                                    <i class="fas fa-user me-2"></i> Para Usuarios Específicos
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{% url 'notificaciones:crear_notificacion_grupo' %}">
                                                    <i class="fas fa-users me-2"></i> Para Grupos
                                                </a>
                                            </li>
                                            {% if user_permissions.can_send_mass_notifications %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-warning" href="{% url 'notificaciones:crear_notificacion_masiva' %}">
                                                    <i class="fas fa-broadcast-tower me-2"></i> Notificación Masiva
                                                </a>
                                            </li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>

                                <!-- Gestionar Notificaciones (solo para usuarios con permisos) -->
                                <div class="col-md-3 mb-3">
                                    <a href="{% url 'notificaciones:lista_notificaciones' %}"
                                       style="background-color: #6f42c1; color: white; padding: 20px; border-radius: 10px; text-decoration: none; display: block; text-align: center;">
                                        <i class="fas fa-cogs" style="font-size: 2rem; display: block; margin-bottom: 10px; color: white;"></i>
                                        <strong style="color: white;">Gestionar Notificaciones</strong>
                                    </a>
                                </div>
                            {% endif %}

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Columna derecha: Notificaciones -->
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Mis Notificaciones
                    </h5>
                    <div class="d-flex align-items-center gap-2">
                        <span class="notification-count badge bg-warning d-none">0</span>
                        <a href="{% url 'notificaciones:mis_notificaciones' %}"
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> Ver Todas
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-0" style="max-height: 500px; overflow-y: auto;" id="dashboard-notifications">
                <!-- Las notificaciones se cargarán aquí via AJAX -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando notificaciones...</span>
                    </div>
                    <p class="text-muted mt-2">Cargando notificaciones...</p>
                </div>
            </div>
            <div class="card-footer bg-light text-center">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        Actualizado automáticamente
                    </small>
                    <button class="btn btn-sm btn-outline-secondary" onclick="cargarNotificacionesDashboard()">
                        <i class="fas fa-sync-alt"></i> Actualizar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Función para cargar notificaciones en el dashboard
function cargarNotificacionesDashboard() {
    const container = document.getElementById('dashboard-notifications');

    // Mostrar loading
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Cargando notificaciones...</span>
            </div>
            <p class="text-muted mt-2">Cargando notificaciones...</p>
        </div>
    `;

    // Cargar notificaciones via AJAX
    fetch('/notificaciones/ajax/obtener-recientes/?limit=5')
        .then(response => response.json())
        .then(data => {
            if (data.notificaciones.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No hay notificaciones</h6>
                        <p class="text-muted small">Las notificaciones aparecerán aquí cuando las reciba</p>
                    </div>
                `;
                return;
            }

            let html = '';
            data.notificaciones.forEach(notif => {
                const isUnread = !notif.leida ? 'notification-unread' : 'notification-read';
                const badge = !notif.leida ? '<span class="badge bg-warning badge-nueva">NUEVA</span>' : '';

                html += `
                    <div class="notification-item ${isUnread} p-3 border-bottom" data-notificacion-id="${notif.id}">
                        <div class="d-flex align-items-start">
                            <div class="me-3">
                                <i class="${notif.icono} text-${notif.tipo_color}"></i>
                            </div>
                            <div class="flex-grow-1">
                                ${notif.titulo ? `<h6 class="mb-1">${notif.titulo}</h6>` : ''}
                                <p class="mb-1 small">${notif.mensaje}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>${notif.fecha_envio}
                                    </small>
                                    ${badge}
                                </div>
                                <div class="mt-2">
                                    ${notif.url_accion ? `
                                        <a href="${notif.url_accion}" class="btn btn-sm btn-outline-primary me-2" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> Ver Acción
                                        </a>
                                    ` : ''}

                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        })
        .catch(error => {
            console.error('Error al cargar notificaciones:', error);
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6 class="text-warning">Error al cargar</h6>
                    <p class="text-muted small">No se pudieron cargar las notificaciones</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="cargarNotificacionesDashboard()">
                        <i class="fas fa-retry"></i> Reintentar
                    </button>
                </div>
            `;
        });
}

// Cargar notificaciones al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    cargarNotificacionesDashboard();

    // Actualizar cada 60 segundos
    setInterval(cargarNotificacionesDashboard, 60000);

    // Inicializar manager de notificaciones para botones AJAX
    if (typeof NotificacionesManager !== 'undefined') {
        window.notificacionesManager = new NotificacionesManager();
    }
});
</script>

<!-- JavaScript de notificaciones -->
<script src="{% static 'notificaciones/js/notificaciones.js' %}"></script>
{% endblock %}