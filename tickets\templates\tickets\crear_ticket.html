{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Crear Nuevo Ticket{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Crear Nuevo Ticket</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-user me-1"></i>{{ rol_usuario }}
                                    <span class="ms-3">
                                        <i class="fas fa-info-circle me-1"></i>Complete todos los campos requeridos
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'tickets:lista_tickets' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Volver a Lista
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Información del Ticket
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="form-crear-ticket" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Título -->
                        <div class="mb-3">
                            <label for="titulo" class="form-label">
                                Título del Ticket <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="titulo" name="titulo" 
                                   required minlength="5" maxlength="200"
                                   placeholder="Descripción breve del problema o solicitud">
                            <div class="form-text">Mínimo 5 caracteres, máximo 200</div>
                        </div>

                        <!-- Descripción -->
                        <div class="mb-3">
                            <label for="descripcion" class="form-label">
                                Descripción Detallada <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="descripcion" name="descripcion" 
                                      rows="4" required minlength="10"
                                      placeholder="Describa detalladamente el problema, solicitud o situación..."></textarea>
                            <div class="form-text">Mínimo 10 caracteres. Sea específico para una mejor atención</div>
                        </div>

                        <div class="row">
                            <!-- Área Responsable -->
                            <div class="col-md-6 mb-3">
                                <label for="area" class="form-label">
                                    Área Responsable <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="area" name="area" required>
                                    <option value="">Seleccione un área</option>
                                    {% for area in areas_disponibles %}
                                        <option value="{{ area.id }}">{{ area.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Área que se encargará de resolver el ticket</div>
                            </div>

                            <!-- Prioridad -->
                            <div class="col-md-6 mb-3">
                                <label for="prioridad" class="form-label">
                                    Prioridad <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="prioridad" name="prioridad" required>
                                    {% for valor, display in prioridades_disponibles %}
                                        <option value="{{ valor }}" {% if valor == 'media' %}selected{% endif %}>
                                            {{ display }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Nivel de urgencia del ticket</div>
                            </div>
                        </div>

                        <!-- Dirección -->
                        <div class="mb-3">
                            <label for="direccion" class="form-label">
                                Dirección Específica <span class="text-muted">(Opcional)</span>
                            </label>
                            <textarea class="form-control" id="direccion" name="direccion"
                                      rows="2" placeholder="Dirección donde se requiere el servicio (si aplica)"></textarea>
                            <div class="form-text">Proporcione la dirección exacta si el servicio requiere visita física</div>
                        </div>

                        <!-- Imágenes -->
                        <div class="mb-4">
                            <label for="imagenes" class="form-label">
                                Imágenes <span class="text-muted">(Opcional)</span>
                            </label>
                            <input type="file" class="form-control" id="imagenes" name="imagenes"
                                   multiple accept="image/jpeg,image/jpg,image/png,image/webp">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Máximo 3 imágenes, 5MB cada una. Formatos: JPG, PNG, WebP
                            </div>

                            <!-- Preview de imágenes -->
                            <div id="preview-imagenes" class="mt-3" style="display: none;">
                                <div class="row g-2" id="imagenes-container"></div>
                            </div>
                        </div>

                        <!-- Botones -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'tickets:lista_tickets' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary" id="btn-crear">
                                <i class="fas fa-save me-2"></i>Crear Ticket
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panel lateral -->
        <div class="col-lg-4">
            <!-- Selección de ciudadano -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>Ciudadano Solicitante
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Ciudadano seleccionado -->
                    <div id="ciudadano-seleccionado" style="display: none;">
                        <div class="alert alert-success">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="alert-heading mb-1">
                                        <i class="fas fa-user-check me-2"></i>Ciudadano Seleccionado
                                    </h6>
                                    <p class="mb-0" id="info-ciudadano-seleccionado"></p>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerCiudadano()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Botones de acción -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalBuscarCiudadano">
                            <i class="fas fa-search me-2"></i>Buscar Ciudadano
                        </button>
                        <a href="{% url 'ciudadano:crear_ciudadano' %}?crear_ticket=true" class="btn btn-outline-success">
                            <i class="fas fa-user-plus me-2"></i>Registrar Nuevo Ciudadano
                        </a>
                    </div>

                    <!-- Ciudadanos recientes -->
                    {% if ciudadanos_recientes %}
                        <hr>
                        <h6 class="mb-2">
                            <i class="fas fa-clock me-2"></i>Recientes
                        </h6>
                        <div class="list-group list-group-flush">
                            {% for ciudadano in ciudadanos_recientes|slice:":3" %}
                                <div class="list-group-item px-0 py-2 border-0">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-0 fs-6">{{ ciudadano.nombre_completo|truncatechars:20 }}</h6>
                                            <small class="text-muted">{{ ciudadano.dpi }}</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="seleccionarCiudadano({{ ciudadano.id }}, '{{ ciudadano.nombre_completo }}', '{{ ciudadano.dpi }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Información de ayuda -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Consejos
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Sea específico en el título y descripción</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Seleccione el área correcta para mejor atención</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Use prioridad alta solo para casos urgentes</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Incluya dirección si requiere visita física</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Buscar Ciudadano -->
<div class="modal fade" id="modalBuscarCiudadano" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>Buscar Ciudadano
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Buscador -->
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="buscar-ciudadano-input"
                               placeholder="Buscar por nombre, DPI o teléfono..." autocomplete="off">
                    </div>
                    <div class="form-text">Ingrese al menos 2 caracteres para buscar</div>
                </div>

                <!-- Resultados -->
                <div id="resultados-busqueda">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>Ingrese un término de búsqueda</p>
                    </div>
                </div>

                <!-- Loading -->
                <div id="loading-busqueda" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Buscando...</span>
                    </div>
                    <p class="mt-2 text-muted">Buscando ciudadanos...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <a href="{% url 'ciudadano:crear_ciudadano' %}?crear_ticket=true" class="btn btn-success">
                    <i class="fas fa-user-plus me-2"></i>Registrar Nuevo
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Campo oculto para ciudadano seleccionado -->
<input type="hidden" id="ciudadano_id" name="ciudadano_id" form="form-crear-ticket">

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.avatar-sm {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.form-control:focus, .form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.list-group-item {
    transition: background-color 0.1s ease;
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.fs-6 {
    font-size: 0.875rem;
    color: #212529 !important; /* Asegurar texto negro */
}

.list-group-item h6 {
    color: #212529 !important; /* Texto negro para nombres */
}

.list-group-item .text-muted {
    color: #6c757d !important; /* Gris para información secundaria */
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-crear-ticket');
    const btnCrear = document.getElementById('btn-crear');
    
    // Validación en tiempo real
    const titulo = document.getElementById('titulo');
    const descripcion = document.getElementById('descripcion');
    
    titulo.addEventListener('input', function() {
        if (this.value.length < 5) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    descripcion.addEventListener('input', function() {
        if (this.value.length < 10) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
    
    // Envío del formulario
    form.addEventListener('submit', function(e) {
        btnCrear.disabled = true;
        btnCrear.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creando...';
    });

    // Búsqueda de ciudadanos
    const buscarInput = document.getElementById('buscar-ciudadano-input');
    const resultadosBusqueda = document.getElementById('resultados-busqueda');
    const loadingBusqueda = document.getElementById('loading-busqueda');
    let timeoutBusqueda;

    if (buscarInput) {
        buscarInput.addEventListener('input', function() {
            const termino = this.value.trim();

            // Limpiar timeout anterior
            clearTimeout(timeoutBusqueda);

            if (termino.length < 2) {
                resultadosBusqueda.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>Ingrese al menos 2 caracteres para buscar</p>
                    </div>
                `;
                return;
            }

            // Debounce de 300ms
            timeoutBusqueda = setTimeout(() => {
                buscarCiudadanos(termino);
            }, 300);
        });
    }

    // Verificar si hay ciudadano preseleccionado por URL
    const urlParams = new URLSearchParams(window.location.search);
    const ciudadanoId = urlParams.get('ciudadano_id');
    if (ciudadanoId) {
        // Buscar información del ciudadano preseleccionado
        fetch(`/ciudadanos/buscar/?q=${ciudadanoId}`)
            .then(response => response.json())
            .then(data => {
                const ciudadano = data.ciudadanos.find(c => c.id == ciudadanoId);
                if (ciudadano) {
                    seleccionarCiudadano(ciudadano.id, ciudadano.nombre_completo, ciudadano.dpi);
                }
            })
            .catch(error => console.error('Error:', error));
    }
});

function buscarCiudadanos(termino) {
    const resultadosBusqueda = document.getElementById('resultados-busqueda');
    const loadingBusqueda = document.getElementById('loading-busqueda');

    // Mostrar loading
    resultadosBusqueda.style.display = 'none';
    loadingBusqueda.style.display = 'block';

    fetch(`/ciudadanos/buscar/?q=${encodeURIComponent(termino)}`)
        .then(response => response.json())
        .then(data => {
            loadingBusqueda.style.display = 'none';
            resultadosBusqueda.style.display = 'block';

            if (data.ciudadanos.length === 0) {
                resultadosBusqueda.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-user-slash fa-2x mb-2"></i>
                        <p>No se encontraron ciudadanos con "${data.busqueda}"</p>
                        <a href="/ciudadanos/crear/?crear_ticket=true" class="btn btn-sm btn-success">
                            <i class="fas fa-user-plus me-2"></i>Registrar Nuevo Ciudadano
                        </a>
                    </div>
                `;
            } else {
                let html = '<div class="list-group">';
                data.ciudadanos.forEach(ciudadano => {
                    html += `
                        <div class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${ciudadano.nombre_completo}</h6>
                                    <p class="mb-1">
                                        <small class="text-muted">
                                            <i class="fas fa-id-card me-1"></i>DPI: ${ciudadano.dpi}
                                        </small>
                                        ${ciudadano.telefono !== 'Sin teléfono' ?
                                            `<span class="ms-3"><i class="fas fa-phone me-1"></i>${ciudadano.telefono}</span>` :
                                            ''
                                        }
                                    </p>
                                    <small class="text-info">
                                        <i class="fas fa-ticket-alt me-1"></i>${ciudadano.total_tickets} ticket(s)
                                        ${ciudadano.edad ? ` • ${ciudadano.edad} años` : ''}
                                    </small>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary"
                                        onclick="seleccionarCiudadanoModal(${ciudadano.id}, '${ciudadano.nombre_completo}', '${ciudadano.dpi}')">
                                    <i class="fas fa-check me-1"></i>Seleccionar
                                </button>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                resultadosBusqueda.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            loadingBusqueda.style.display = 'none';
            resultadosBusqueda.style.display = 'block';
            resultadosBusqueda.innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>Error al buscar ciudadanos</p>
                </div>
            `;
        });
}

function seleccionarCiudadano(id, nombre, dpi) {
    document.getElementById('ciudadano_id').value = id;

    // Mostrar información del ciudadano seleccionado
    const ciudadanoSeleccionado = document.getElementById('ciudadano-seleccionado');
    const infoCiudadano = document.getElementById('info-ciudadano-seleccionado');

    infoCiudadano.innerHTML = `
        <strong>${nombre}</strong><br>
        <small class="text-muted">DPI: ${dpi}</small>
    `;

    ciudadanoSeleccionado.style.display = 'block';
}

function seleccionarCiudadanoModal(id, nombre, dpi) {
    seleccionarCiudadano(id, nombre, dpi);

    // Cerrar modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalBuscarCiudadano'));
    modal.hide();

    // Limpiar búsqueda
    document.getElementById('buscar-ciudadano-input').value = '';
    document.getElementById('resultados-busqueda').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>Ingrese un término de búsqueda</p>
        </div>
    `;
}

function removerCiudadano() {
    document.getElementById('ciudadano_id').value = '';
    document.getElementById('ciudadano-seleccionado').style.display = 'none';
}

// Manejo de preview de imágenes
document.getElementById('imagenes').addEventListener('change', function(e) {
    const files = e.target.files;
    const previewContainer = document.getElementById('preview-imagenes');
    const imagenesContainer = document.getElementById('imagenes-container');

    // Limpiar preview anterior
    imagenesContainer.innerHTML = '';

    if (files.length > 0) {
        // Validar número máximo de imágenes
        if (files.length > 3) {
            alert('Solo se pueden seleccionar máximo 3 imágenes.');
            e.target.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        previewContainer.style.display = 'block';

        // Crear preview para cada imagen
        Array.from(files).forEach((file, index) => {
            // Validar tamaño
            if (file.size > 5 * 1024 * 1024) {
                alert(`La imagen ${file.name} es muy grande (máximo 5MB).`);
                return;
            }

            // Validar tipo
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert(`La imagen ${file.name} no tiene un formato válido.`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-md-4';

                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                        <div class="card-body p-2">
                            <small class="text-muted">${file.name}</small><br>
                            <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                        </div>
                    </div>
                `;

                imagenesContainer.appendChild(col);
            };
            reader.readAsDataURL(file);
        });
    } else {
        previewContainer.style.display = 'none';
    }
});
</script>
{% endblock %}
