{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Registrar Nuevo Ciudadano{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Registrar Nuevo Ciudadano</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-info-circle me-1"></i>Complete la información del ciudadano
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Volver a Lista
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Información del Ciudadano
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="form-crear-ciudadano">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- DPI -->
                            <div class="col-md-6 mb-3">
                                <label for="dpi" class="form-label">
                                    DPI <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="dpi" name="dpi" 
                                       required maxlength="13" pattern="[0-9]{13}"
                                       placeholder="1234567890123">
                                <div class="form-text">13 dígitos numéricos</div>
                            </div>

                            <!-- Nombre Completo -->
                            <div class="col-md-6 mb-3">
                                <label for="nombre_completo" class="form-label">
                                    Nombre Completo <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="nombre_completo" name="nombre_completo" 
                                       required minlength="3" maxlength="200"
                                       placeholder="Nombres y apellidos completos">
                                <div class="form-text">Mínimo 3 caracteres</div>
                            </div>
                        </div>

                        <!-- Dirección -->
                        <div class="mb-3">
                            <label for="direccion" class="form-label">
                                Dirección <span class="text-muted">(Opcional)</span>
                            </label>
                            <textarea class="form-control" id="direccion" name="direccion" 
                                      rows="2" placeholder="Dirección completa del ciudadano"></textarea>
                        </div>

                        <div class="row">
                            <!-- Teléfono -->
                            <div class="col-md-6 mb-3">
                                <label for="telefono" class="form-label">
                                    Teléfono <span class="text-muted">(Opcional)</span>
                                </label>
                                <input type="tel" class="form-control" id="telefono" name="telefono" 
                                       maxlength="15" placeholder="12345678">
                                <div class="form-text">Número de contacto</div>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    Email <span class="text-muted">(Opcional)</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       maxlength="100" placeholder="<EMAIL>">
                                <div class="form-text">Correo electrónico</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Fecha de Nacimiento -->
                            <div class="col-md-6 mb-3">
                                <label for="fecha_nacimiento" class="form-label">
                                    Fecha de Nacimiento <span class="text-muted">(Opcional)</span>
                                </label>
                                <input type="date" class="form-control" id="fecha_nacimiento" name="fecha_nacimiento">
                                <div class="form-text">Para calcular la edad</div>
                            </div>

                            <!-- Género -->
                            <div class="col-md-6 mb-3">
                                <label for="genero" class="form-label">
                                    Género <span class="text-muted">(Opcional)</span>
                                </label>
                                <select class="form-select" id="genero" name="genero">
                                    <option value="">Seleccione género</option>
                                    <option value="1">Hombre</option>
                                    <option value="2">Mujer</option>
                                </select>
                            </div>
                        </div>

                        <!-- Opción para crear ticket después -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="crear_ticket_despues" 
                                       name="crear_ticket_despues" {% if crear_ticket_despues %}checked{% endif %}>
                                <label class="form-check-label" for="crear_ticket_despues">
                                    <strong>Crear ticket después de registrar</strong>
                                    <div class="form-text">Redirigir automáticamente a crear un ticket para este ciudadano</div>
                                </label>
                            </div>
                        </div>

                        <!-- Botones -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancelar
                            </a>
                            <button type="submit" class="btn btn-success" id="btn-crear">
                                <i class="fas fa-save me-2"></i>Registrar Ciudadano
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panel lateral -->
        <div class="col-lg-4">
            <!-- Información de ayuda -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Información Importante
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>DPI:</strong> Debe ser único en el sistema</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Nombre:</strong> Use nombres y apellidos completos</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Teléfono:</strong> Facilita el contacto directo</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Email:</strong> Para notificaciones automáticas</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Estadísticas rápidas -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Después del Registro
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-ticket-alt fa-2x text-primary mb-2"></i>
                            <p class="mb-0">
                                <small>Podrá crear tickets inmediatamente</small>
                            </p>
                        </div>
                        <div class="mb-3">
                            <i class="fas fa-search fa-2x text-info mb-2"></i>
                            <p class="mb-0">
                                <small>Aparecerá en búsquedas de tickets</small>
                            </p>
                        </div>
                        <div>
                            <i class="fas fa-history fa-2x text-success mb-2"></i>
                            <p class="mb-0">
                                <small>Se mantendrá historial completo</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.form-control:focus, .form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('form-crear-ciudadano');
    const btnCrear = document.getElementById('btn-crear');
    const dpiInput = document.getElementById('dpi');
    const nombreInput = document.getElementById('nombre_completo');
    
    // Validación de DPI en tiempo real
    dpiInput.addEventListener('input', function() {
        // Solo permitir números
        this.value = this.value.replace(/[^0-9]/g, '');
        
        if (this.value.length === 13) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (this.value.length > 0) {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
        }
    });
    
    // Validación de nombre en tiempo real
    nombreInput.addEventListener('input', function() {
        if (this.value.length >= 3) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (this.value.length > 0) {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
        }
    });
    
    // Envío del formulario
    form.addEventListener('submit', function(e) {
        btnCrear.disabled = true;
        btnCrear.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Registrando...';
    });
});
</script>
{% endblock %}
