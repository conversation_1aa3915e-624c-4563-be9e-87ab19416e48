{% extends 'reportes/base_reportes.html' %}

{% block page_title %}Sistema de Reportes{% endblock %}
{% block page_description %}Seleccione el tipo de reporte que desea generar{% endblock %}

{% block report_content %}
<div class="row">
    <!-- Reporte por Empleado -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="report-card text-center">
            <div class="report-icon">
                <i class="fas fa-user-tie"></i>
            </div>
            <h4 class="text-primary">Reportes por Empleado</h4>
            <p class="text-muted mb-4">
                Genere reportes detallados de tickets asignados a empleados específicos.
                Incluye resumen por estados y listado completo de tickets.
            </p>
            <a href="{% url 'reportes:reporte_empleado' %}" class="btn btn-primary btn-generate">
                <i class="fas fa-chart-line me-2"></i>Generar Reporte
            </a>
        </div>
    </div>
    
    <!-- Reporte por Área -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="report-card text-center">
            <div class="report-icon">
                <i class="fas fa-building"></i>
            </div>
            <h4 class="text-primary">Reportes por Área</h4>
            <p class="text-muted mb-4">
                Genere reportes de tickets por área o departamento.
                Perfecto para supervisores y jefes de área.
            </p>
            <a href="{% url 'reportes:reporte_area' %}" class="btn btn-primary btn-generate">
                <i class="fas fa-chart-pie me-2"></i>Generar Reporte
            </a>
        </div>
    </div>
    
    <!-- Reporte por Ciudadano -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="report-card text-center">
            <div class="report-icon">
                <i class="fas fa-users"></i>
            </div>
            <h4 class="text-primary">Reportes por Ciudadano</h4>
            <p class="text-muted mb-4">
                Genere reportes de tickets solicitados por ciudadanos específicos.
                Útil para seguimiento de casos individuales.
            </p>
            <a href="{% url 'reportes:reporte_ciudadano' %}" class="btn btn-primary btn-generate">
                <i class="fas fa-user-friends me-2"></i>Generar Reporte
            </a>
        </div>
    </div>
    
    <!-- Reporte General -->
    <div class="col-lg-6 col-md-6 mb-4">
        <div class="report-card text-center">
            <div class="report-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <h4 class="text-primary">Reportes Generales</h4>
            <p class="text-muted mb-4">
                Genere reportes completos del sistema con información detallada.
                Incluye todos los tickets y estadísticas generales.
            </p>
            <a href="{% url 'reportes:reporte_general' %}" class="btn btn-primary btn-generate">
                <i class="fas fa-file-alt me-2"></i>Generar Reporte
            </a>
        </div>
    </div>
</div>

<!-- Información adicional -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Información sobre los Reportes</h5>
            <ul class="mb-0">
                <li><strong>Formatos disponibles:</strong> PDF y Excel</li>
                <li><strong>Filtros:</strong> Todos los reportes pueden filtrarse por rango de fechas</li>
                <li><strong>Selección múltiple:</strong> Puede seleccionar varios empleados, áreas o ciudadanos</li>
                <li><strong>Estructura:</strong> Cada reporte incluye resumen por estados y tablas detalladas</li>
                <li><strong>Acceso:</strong> Solo administradores y supervisores pueden generar reportes</li>
            </ul>
        </div>
    </div>
</div>

<!-- Historial de reportes -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="{% url 'reportes:historial' %}" class="btn btn-outline-primary btn-block mb-2">
                            <i class="fas fa-list me-2"></i>Ver Historial de Reportes
                        </a>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-secondary btn-block mb-2" onclick="showReportInfo()">
                            <i class="fas fa-question-circle me-2"></i>Ayuda sobre Reportes
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_report_js %}
<script>
function showReportInfo() {
    Swal.fire({
        title: 'Ayuda sobre Reportes',
        html: `
            <div class="text-left">
                <h6><i class="fas fa-user-tie text-primary"></i> Reportes por Empleado</h6>
                <p class="small">Muestra todos los tickets asignados a empleados específicos, organizados por estado.</p>
                
                <h6><i class="fas fa-building text-primary"></i> Reportes por Área</h6>
                <p class="small">Muestra todos los tickets asignados a áreas o departamentos específicos.</p>
                
                <h6><i class="fas fa-users text-primary"></i> Reportes por Ciudadano</h6>
                <p class="small">Muestra todos los tickets solicitados por ciudadanos específicos.</p>
                
                <h6><i class="fas fa-chart-bar text-primary"></i> Reportes Generales</h6>
                <p class="small">Reporte completo del sistema con información detallada de todos los tickets.</p>
                
                <hr>
                <p class="small text-muted">
                    <strong>Nota:</strong> Todos los reportes incluyen filtros por fecha y están disponibles en formato PDF y Excel.
                </p>
            </div>
        `,
        width: 600,
        confirmButtonColor: '#1A237E',
        confirmButtonText: 'Entendido'
    });
}
</script>
{% endblock %}
