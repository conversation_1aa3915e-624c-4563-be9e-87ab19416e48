from django.contrib import admin
from django import forms
from django.contrib.auth.models import Group
from django.contrib.auth.admin import GroupAdmin as DefaultGroupAdmin
from django.contrib.auth import get_user_model

User = get_user_model()

class GroupAdminForm(forms.ModelForm):
    """
    Formulario para el admin de Group que añade el campo 'users'.
    """
    users = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        required=False,
        label="Usuarios del grupo",
        widget=admin.widgets.FilteredSelectMultiple("Usuarios", is_stacked=False),
    )

    class Meta:
        model = Group
        fields = ("name", "permissions", "users")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['users'].initial = self.instance.user_set.all()

    def save(self, commit=True):
        group = super().save(commit=False)
        if commit:
            group.save()
        group.user_set.set(self.cleaned_data['users'])
        self.save_m2m()
        return group

class GroupAdmin(DefaultGroupAdmin):
    """
    Admin personalizado para Group, muestra y guarda usuarios.
    """
    form = GroupAdminForm
    filter_horizontal = ("permissions",)
    # Añadimos users al fieldsets para que aparezca en el formulario
    fieldsets = (
        (None, {'fields': ('name',)}),
        ('Permisos', {'fields': ('permissions',)}),
        ('Usuarios asignados', {'fields': ('users',)}),
    )

admin.site.unregister(Group)
admin.site.register(Group, GroupAdmin)
