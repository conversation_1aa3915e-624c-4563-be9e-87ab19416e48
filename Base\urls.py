"""
Base/urls.py
URLs para las vistas auxiliares del sistema base.

Incluye rutas para:
- Demo del sistema de permisos
- Testing de funcionalidades
- Páginas de ayuda
"""

from django.urls import path
from . import views

app_name = 'base'

urlpatterns = [
    # Demo y testing
    path('demo-permisos/', views.demo_permisos, name='demo_permisos'),
    path('test/permissions-info/', views.test_permissions_info, name='test_permissions_info'),

    # Estadísticas de seguridad
    path('security-stats/', views.security_stats, name='security_stats'),

    # Testing de errores
    path('test/403/', views.test_403_error, name='test_403'),
    path('test/404/', views.test_404_error, name='test_404'),
    path('test/500/', views.test_500_error, name='test_500'),
]
