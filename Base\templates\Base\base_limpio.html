<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistema de Tickets{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        /* Variables CSS */
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --tertiary-color: #3F51B5;
            --gray-color: #9E9E9E;
            --white-color: #FFFFFF;
            --accent-orange: #FF9800;
            --accent-green: #4CAF50;
            --accent-yellow: #FFEB3B;
        }

        /* Estilos globales */
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--white-color);
            margin: 0;
            padding: 0;
        }

        /* Sidebar */
        #sidebar-wrapper {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            width: 280px;
            transition: all 0.3s ease;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            overflow: hidden;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-content {
            width: 280px;
            min-height: 100vh;
            overflow-y: auto;
        }

        #sidebar-wrapper.collapsed {
            width: 0;
        }

        /* Sidebar Header */
        .sidebar-heading {
            background: rgba(0,0,0,0.2);
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-heading h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }

        /* Menu Items */
        .list-group-item {
            background: transparent !important;
            border: none !important;
            color: rgba(255,255,255,0.9) !important;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .list-group-item:hover {
            background: rgba(255,255,255,0.1) !important;
            color: white !important;
            transform: translateX(8px);
        }

        /* Dropdown menus */
        .dropdown-menu-custom {
            background: rgba(0,0,0,0.3);
            border: none;
            margin: 0;
            padding: 0;
        }

        .dropdown-item-custom {
            color: rgba(255,255,255,0.8) !important;
            padding: 0.75rem 2.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }

        .dropdown-item-custom:hover {
            background: rgba(255,255,255,0.1) !important;
            color: white !important;
        }

        /* NO flechas automáticas */
        .dropdown-toggle-custom::after {
            display: none !important;
        }

        /* Main content */
        .main-content {
            margin-left: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
            width: calc(100% - 280px);
        }

        .main-content.collapsed {
            margin-left: 0;
            width: 100%;
        }

        /* Navbar sin sticky */
        .navbar {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
            padding: 0.5rem 1rem;
            background-color: var(--white-color);
            position: relative;
            z-index: 1020;
            margin: 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            #sidebar-wrapper {
                width: 0;
                transform: translateX(-100%);
            }
            
            #sidebar-wrapper.active {
                width: 280px;
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        /* Iconos coloridos */
        .text-primary { color: #64B5F6 !important; }
        .text-info { color: #4FC3F7 !important; }
        .text-warning { color: #FFB74D !important; }
        .text-success { color: #81C784 !important; }
        .text-danger { color: #E57373 !important; }
        .text-secondary { color: #B0BEC5 !important; }

        /* Separador */
        .sidebar-divider {
            border-top: 1px solid rgba(255,255,255,0.2);
            margin: 1rem 1.5rem;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <div id="sidebar-wrapper">
            <div class="sidebar-content">
                <!-- Header del Sidebar -->
                <div class="sidebar-heading">
                    <h3><i class="fas fa-ticket-alt me-2"></i>Sistema de Tickets</h3>
                    <small class="text-white-50">Gestión Municipal</small>
                </div>

                <!-- Navegación -->
                <div class="list-group list-group-flush">
                    
                    <!-- Dashboard -->
                    <a href="/inicio/" class="list-group-item">
                        <i class="fas fa-tachometer-alt me-3 text-primary"></i>
                        <span>Dashboard</span>
                    </a>

                    <!-- Tickets -->
                    <a href="#ticketsSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-ticket-alt me-3 text-info"></i>
                        <span>Tickets</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="ticketsSubmenu">
                        <a href="{% url 'tickets:lista_tickets' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Ver Todos los Tickets
                        </a>
                        <a href="{% url 'tickets:crear_ticket' %}" class="dropdown-item-custom">
                            <i class="fas fa-plus me-2"></i> Crear Nuevo Ticket
                        </a>
                    </div>

                    <!-- Asignaciones -->
                    <a href="#asignacionesSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-tasks me-3 text-warning"></i>
                        <span>Asignaciones</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="asignacionesSubmenu">
                        <a href="{% url 'asignaciones:mis_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-check me-2"></i> Mis Asignaciones
                        </a>
                        <a href="{% url 'asignaciones:lista_asignaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-users-cog me-2"></i> Gestionar Asignaciones
                        </a>
                    </div>

                    <!-- Ciudadanos -->
                    <a href="#ciudadanosSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-users me-3 text-success"></i>
                        <span>Ciudadanos</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="ciudadanosSubmenu">
                        <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Lista de Ciudadanos
                        </a>
                        <a href="{% url 'ciudadano:crear_ciudadano' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-plus me-2"></i> Registrar Ciudadano
                        </a>
                        <a href="{% url 'ciudadano:buscar_ciudadano' %}" class="dropdown-item-custom">
                            <i class="fas fa-search me-2"></i> Buscar Ciudadano
                        </a>
                    </div>

                    <!-- Notificaciones -->
                    <a href="#notificacionesSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-bell me-3 text-danger"></i>
                        <span>Notificaciones</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="notificacionesSubmenu">
                        <a href="{% url 'notificaciones:lista_notificaciones' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Todas las Notificaciones
                        </a>
                        <a href="{% url 'notificaciones:crear_notificacion' %}" class="dropdown-item-custom">
                            <i class="fas fa-plus me-2"></i> Crear Notificación
                        </a>
                    </div>

                    <!-- Usuarios -->
                    <a href="#usuariosSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-user-cog me-3 text-secondary"></i>
                        <span>Usuarios</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="usuariosSubmenu">
                        <a href="{% url 'user:lista_usuarios' %}" class="dropdown-item-custom">
                            <i class="fas fa-list me-2"></i> Lista de Usuarios
                        </a>
                        <a href="{% url 'user:crear_usuario_paso1' %}" class="dropdown-item-custom">
                            <i class="fas fa-user-plus me-2"></i> Crear Usuario
                        </a>
                    </div>

                    <!-- Reportes -->
                    <a href="#reportesSubmenu" class="list-group-item dropdown-toggle-custom collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-chart-bar me-3 text-info"></i>
                        <span>Reportes</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse dropdown-menu-custom" id="reportesSubmenu">
                        <a href="{% url 'tickets:reportes' %}" class="dropdown-item-custom">
                            <i class="fas fa-chart-line me-2"></i> Reportes de Tickets
                        </a>
                    </div>

                    <!-- Separador -->
                    <div class="sidebar-divider"></div>

                    <!-- Cerrar Sesión -->
                    <a href="{% url 'logout' %}" class="list-group-item text-danger">
                        <i class="fas fa-sign-out-alt me-3"></i>
                        <span>Cerrar Sesión</span>
                    </a>

                </div>
            </div>
        </div>

        <!-- Contenido principal -->
        <div class="main-content" id="page-content-wrapper">
            <!-- Barra superior -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid px-0">
                    <button class="btn btn-link" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span class="navbar-brand m-0">
                        <i class="fas fa-user-circle me-2"></i>Bienvenido, <strong>{{ user.username }}</strong>
                    </span>
                </div>
            </nav>

            <!-- Contenido dinámico SIN padding excesivo -->
            <div class="container-fluid" style="padding: 1rem;">
                {% block content %}
                {% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            // Toggle sidebar
            $('#sidebarToggle').on('click', function () {
                const sidebar = $('#sidebar-wrapper');
                const content = $('.main-content');

                sidebar.toggleClass('collapsed active');
                content.toggleClass('collapsed');
                $(this).toggleClass('active');
            });

            // Manejar desplegables del sidebar
            $('.dropdown-toggle-custom').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('href');
                $(target).collapse('toggle');
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
