{% extends 'Base/base.html' %}
{% block title %}Teléfonos de {{ usuario.get_full_name }}{% endblock %}
{% block content %}
<div class="card">
    <div class="card-header">
        <h5>Teléfonos de {{ usuario.get_full_name }}</h5>
    </div>
    <div class="card-body">
        <form method="post" id="form-telefono">
            {% csrf_token %}
            {{ form.as_p }}
            <button type="submit" class="btn btn-primary">Agregar</button>
        </form>
        <hr>
        <h6>Teléfonos registrados</h6>
        <ul>
            {% for tel in telefonos %}
                <li>
                    {{ tel.numero }} ({{ tel.get_tipo_display }})
                    <button class="btn btn-sm btn-danger" onclick="eliminarTelefono({{ tel.id }})">Eliminar</button>
                </li>
            {% empty %}
                <li class="text-muted">Sin teléfonos registrados</li>
            {% endfor %}
        </ul>
    </div>
</div>
<script>
function eliminarTelefono(id) {
    fetch("{% url 'user:desactivar_telefono' usuario.id 0 %}".replace('0', id), {
        method: 'POST',
        headers: {'X-CSRFToken': '{{ csrf_token }}'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) location.reload();
    });
}
</script>
{% endblock %}